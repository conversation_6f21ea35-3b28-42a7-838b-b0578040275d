{"__meta": {"id": "01JYVE8EVBBWYEB6K5WPTQZCXY", "datetime": "2025-06-28 14:15:00", "utime": **********.204057, "method": "GET", "uri": "/admin/pago-suscripcions/1/edit", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751120098.659146, "end": **********.204072, "duration": 1.5449259281158447, "duration_str": "1.54s", "measures": [{"label": "Booting", "start": 1751120098.659146, "relative_start": 0, "end": **********.34922, "relative_end": **********.34922, "duration": 0.****************, "duration_str": "690ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.349234, "relative_start": 0.****************, "end": **********.204074, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "855ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.857517, "relative_start": 1.****************, "end": **********.86033, "relative_end": **********.86033, "duration": 0.002813100814819336, "duration_str": "2.81ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.017412, "relative_start": 1.****************, "end": **********.017412, "relative_end": **********.017412, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.022799, "relative_start": 1.****************, "end": **********.022799, "relative_end": **********.022799, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.026882, "relative_start": 1.3677358627319336, "end": **********.026882, "relative_end": **********.026882, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.031521, "relative_start": 1.3723750114440918, "end": **********.031521, "relative_end": **********.031521, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.036869, "relative_start": 1.377722978591919, "end": **********.036869, "relative_end": **********.036869, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.041601, "relative_start": 1.3824548721313477, "end": **********.041601, "relative_end": **********.041601, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.047365, "relative_start": 1.388218879699707, "end": **********.047365, "relative_end": **********.047365, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.051331, "relative_start": 1.3921849727630615, "end": **********.051331, "relative_end": **********.051331, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.063445, "relative_start": 1.404299020767212, "end": **********.063445, "relative_end": **********.063445, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": **********.076191, "relative_start": 1.4170448780059814, "end": **********.076191, "relative_end": **********.076191, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-maps::styles", "start": **********.173501, "relative_start": 1.514354944229126, "end": **********.173501, "relative_end": **********.173501, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-maps::scripts", "start": **********.193741, "relative_start": 1.534595012664795, "end": **********.193741, "relative_end": **********.193741, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.196078, "relative_start": 1.5369319915771484, "end": **********.196458, "relative_end": **********.196458, "duration": 0.0003800392150878906, "duration_str": "380μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.202072, "relative_start": 1.5429258346557617, "end": **********.20215, "relative_end": **********.20215, "duration": 7.82012939453125e-05, "duration_str": "78μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 53412592, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.45.0", "PHP Version": "8.3.8", "Environment": "local", "Debug Mode": "Enabled", "URL": "mia.test", "Timezone": "UTC", "Locale": "es"}}, "views": {"count": 12, "nb_templates": 12, "templates": [{"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.017379, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.022766, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}}, {"name": "__components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.026847, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.031491, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.036766, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}}, {"name": "__components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.041571, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.047337, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.051302, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.063416, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "__components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": **********.076163, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}}, {"name": "filament-maps::styles", "param_count": null, "params": [], "start": **********.173471, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\vendor\\webbingbrasil\\filament-maps\\src\\/../resources/views/styles.blade.phpfilament-maps::styles", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Fwebbingbrasil%2Ffilament-maps%2Fresources%2Fviews%2Fstyles.blade.php&line=1", "ajax": false, "filename": "styles.blade.php", "line": "?"}}, {"name": "filament-maps::scripts", "param_count": null, "params": [], "start": **********.193712, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\vendor\\webbingbrasil\\filament-maps\\src\\/../resources/views/scripts.blade.phpfilament-maps::scripts", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Fwebbingbrasil%2Ffilament-maps%2Fresources%2Fviews%2Fscripts.blade.php&line=1", "ajax": false, "filename": "scripts.blade.php", "line": "?"}}]}, "queries": {"count": 53, "nb_statements": 53, "nb_visible_statements": 53, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.029809999999999996, "accumulated_duration_str": "29.81ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'OGF0xmQNkiM3P99CWPuIofWU8bS7yWbRQ0GqToUS' limit 1", "type": "query", "params": [], "bindings": ["OGF0xmQNkiM3P99CWPuIofWU8bS7yWbRQ0GqToUS"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 105}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 89}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.8657742, "duration": 0.00239, "duration_str": "2.39ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "proyecto-mba", "explain": null, "start_percent": 0, "width_percent": 8.017}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 180}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.875298, "duration": 0.00171, "duration_str": "1.71ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "proyecto-mba", "explain": null, "start_percent": 8.017, "width_percent": 5.736}, {"sql": "select * from `cache` where `key` in ('spatie.permission.cache')", "type": "query", "params": [], "bindings": ["spatie.permission.cache"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 418}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.880549, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "proyecto-mba", "explain": null, "start_percent": 13.754, "width_percent": 1.811}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (1) and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 571}], "start": **********.887092, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:328", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=328", "ajax": false, "filename": "HasPermissions.php", "line": "328"}, "connection": "proyecto-mba", "explain": null, "start_percent": 15.565, "width_percent": 3.287}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 314}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}], "start": **********.889671, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "proyecto-mba", "explain": null, "start_percent": 18.853, "width_percent": 2.08}, {"sql": "select * from `pago_suscripcions` where `id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 192}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/Concerns/InteractsWithRecord.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\filament\\src\\Resources\\Pages\\Concerns\\InteractsWithRecord.php", "line": 23}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.899186, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Resource.php:192", "source": {"index": 16, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 192}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FResources%2FResource.php&line=192", "ajax": false, "filename": "Resource.php", "line": "192"}, "connection": "proyecto-mba", "explain": null, "start_percent": 20.933, "width_percent": 1.979}, {"sql": "select * from `suscripciones`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 785}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 77}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.940867, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "Select.php:785", "source": {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 785}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FSelect.php&line=785", "ajax": false, "filename": "Select.php", "line": "785"}, "connection": "proyecto-mba", "explain": null, "start_percent": 22.912, "width_percent": 2.382}, {"sql": "select * from `negocios` where `negocios`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.943935, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 25.294, "width_percent": 1.744}, {"sql": "select * from `negocios` where `negocios`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.945695, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 27.038, "width_percent": 1.711}, {"sql": "select * from `negocios` where `negocios`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.947539, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 28.749, "width_percent": 1.141}, {"sql": "select * from `negocios` where `negocios`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.948951, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 29.889, "width_percent": 1.208}, {"sql": "select * from `negocios` where `negocios`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.950381, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 31.097, "width_percent": 1.174}, {"sql": "select * from `negocios` where `negocios`.`id` = 6 limit 1", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.9517581, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 32.271, "width_percent": 1.107}, {"sql": "select * from `negocios` where `negocios`.`id` = 7 limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.953201, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 33.378, "width_percent": 2.281}, {"sql": "select * from `negocios` where `negocios`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.954944, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 35.659, "width_percent": 1.308}, {"sql": "select * from `negocios` where `negocios`.`id` = 9 limit 1", "type": "query", "params": [], "bindings": [9], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.9563131, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 36.967, "width_percent": 0.906}, {"sql": "select * from `negocios` where `negocios`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.957527, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 37.873, "width_percent": 0.671}, {"sql": "select * from `negocios` where `negocios`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.958668, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 38.544, "width_percent": 0.57}, {"sql": "select * from `negocios` where `negocios`.`id` = 12 limit 1", "type": "query", "params": [], "bindings": [12], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.95977, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 39.114, "width_percent": 0.772}, {"sql": "select * from `negocios` where `negocios`.`id` = 13 limit 1", "type": "query", "params": [], "bindings": [13], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.96099, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 39.886, "width_percent": 0.872}, {"sql": "select * from `negocios` where `negocios`.`id` = 14 limit 1", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.962206, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 40.758, "width_percent": 0.738}, {"sql": "select * from `negocios` where `negocios`.`id` = 15 limit 1", "type": "query", "params": [], "bindings": [15], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.963371, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 41.496, "width_percent": 1.308}, {"sql": "select * from `negocios` where `negocios`.`id` = 16 limit 1", "type": "query", "params": [], "bindings": [16], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.964759, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 42.804, "width_percent": 1.677}, {"sql": "select * from `negocios` where `negocios`.`id` = 17 limit 1", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.966326, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 44.482, "width_percent": 1.442}, {"sql": "select * from `negocios` where `negocios`.`id` = 18 limit 1", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.967735, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 45.924, "width_percent": 1.51}, {"sql": "select * from `negocios` where `negocios`.`id` = 19 limit 1", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.969114, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 47.434, "width_percent": 0.973}, {"sql": "select * from `negocios` where `negocios`.`id` = 20 limit 1", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.970656, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 48.407, "width_percent": 1.778}, {"sql": "select * from `negocios` where `negocios`.`id` = 21 limit 1", "type": "query", "params": [], "bindings": [21], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.972368, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 50.185, "width_percent": 1.61}, {"sql": "select * from `negocios` where `negocios`.`id` = 22 limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.974042, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 51.795, "width_percent": 1.442}, {"sql": "select * from `negocios` where `negocios`.`id` = 23 limit 1", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.975564, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 53.237, "width_percent": 1.979}, {"sql": "select * from `negocios` where `negocios`.`id` = 24 limit 1", "type": "query", "params": [], "bindings": [24], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.977375, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 55.216, "width_percent": 1.811}, {"sql": "select * from `negocios` where `negocios`.`id` = 25 limit 1", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.979014, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 57.028, "width_percent": 1.744}, {"sql": "select * from `negocios` where `negocios`.`id` = 26 limit 1", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.9811208, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 58.772, "width_percent": 1.979}, {"sql": "select * from `negocios` where `negocios`.`id` = 27 limit 1", "type": "query", "params": [], "bindings": [27], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.9827192, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 60.751, "width_percent": 1.476}, {"sql": "select * from `negocios` where `negocios`.`id` = 28 limit 1", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.984185, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 62.227, "width_percent": 1.241}, {"sql": "select * from `negocios` where `negocios`.`id` = 29 limit 1", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.985781, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 63.469, "width_percent": 1.375}, {"sql": "select * from `negocios` where `negocios`.`id` = 30 limit 1", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.9872649, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 64.844, "width_percent": 1.241}, {"sql": "select * from `negocios` where `negocios`.`id` = 31 limit 1", "type": "query", "params": [], "bindings": [31], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.9889512, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 66.085, "width_percent": 2.08}, {"sql": "select * from `negocios` where `negocios`.`id` = 32 limit 1", "type": "query", "params": [], "bindings": [32], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.990558, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 68.165, "width_percent": 1.375}, {"sql": "select * from `negocios` where `negocios`.`id` = 33 limit 1", "type": "query", "params": [], "bindings": [33], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.991955, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 69.54, "width_percent": 1.342}, {"sql": "select * from `negocios` where `negocios`.`id` = 34 limit 1", "type": "query", "params": [], "bindings": [34], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.993336, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 70.882, "width_percent": 1.241}, {"sql": "select * from `negocios` where `negocios`.`id` = 35 limit 1", "type": "query", "params": [], "bindings": [35], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.994724, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 72.123, "width_percent": 1.879}, {"sql": "select * from `negocios` where `negocios`.`id` = 36 limit 1", "type": "query", "params": [], "bindings": [36], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.996363, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 74.002, "width_percent": 1.409}, {"sql": "select * from `negocios` where `negocios`.`id` = 37 limit 1", "type": "query", "params": [], "bindings": [37], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.997772, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 75.411, "width_percent": 1.241}, {"sql": "select * from `negocios` where `negocios`.`id` = 38 limit 1", "type": "query", "params": [], "bindings": [38], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.999116, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 76.652, "width_percent": 1.107}, {"sql": "select * from `negocios` where `negocios`.`id` = 39 limit 1", "type": "query", "params": [], "bindings": [39], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.000422, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 77.759, "width_percent": 1.375}, {"sql": "select * from `negocios` where `negocios`.`id` = 40 limit 1", "type": "query", "params": [], "bindings": [40], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.002609, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 79.135, "width_percent": 2.684}, {"sql": "select * from `negocios` where `negocios`.`id` = 41 limit 1", "type": "query", "params": [], "bindings": [41], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.00487, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 81.818, "width_percent": 2.415}, {"sql": "select * from `negocios` where `negocios`.`id` = 42 limit 1", "type": "query", "params": [], "bindings": [42], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.0067508, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 84.233, "width_percent": 1.442}, {"sql": "select * from `negocios` where `negocios`.`id` = 43 limit 1", "type": "query", "params": [], "bindings": [43], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.008459, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 85.676, "width_percent": 1.51}, {"sql": "select * from `negocios` where `negocios`.`id` = 44 limit 1", "type": "query", "params": [], "bindings": [44], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.0109289, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 87.186, "width_percent": 1.912}, {"sql": "select * from `negocios` where `negocios`.`id` = 45 limit 1", "type": "query", "params": [], "bindings": [45], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.012768, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 89.098, "width_percent": 1.577}, {"sql": "update `sessions` set `payload` = 'YTo3OntzOjY6Il90b2tlbiI7czo0MDoibEZ5bk0xbldDM3dpUGR0cmhtODI0TnRBWUhpWU1FYTI3anZDb3FKQiI7czozOiJ1cmwiO2E6MDp7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjQ3OiJodHRwczovL21pYS50ZXN0L2FkbWluL3BhZ28tc3VzY3JpcGNpb25zLzEvZWRpdCI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fXM6NTA6ImxvZ2luX3dlYl81OWJhMzZhZGRjMmIyZjk0MDE1ODBmMDE0YzdmNThlYTRlMzA5ODlkIjtpOjE7czoxNzoicGFzc3dvcmRfaGFzaF93ZWIiO3M6NjA6IiQyeSQxMiRXRWxoUlF1YjhqSjU0Rkswa3ZQbXRlbzdvT1BrZzhXSkp5ZzBIUEkxMVhJR0xscUxKa25VMiI7czo4OiJmaWxhbWVudCI7YTowOnt9fQ==', `last_activity` = **********, `user_id` = 1, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'OGF0xmQNkiM3P99CWPuIofWU8bS7yWbRQ0GqToUS'", "type": "query", "params": [], "bindings": ["YTo3OntzOjY6Il90b2tlbiI7czo0MDoibEZ5bk0xbldDM3dpUGR0cmhtODI0TnRBWUhpWU1FYTI3anZDb3FKQiI7czozOiJ1cmwiO2E6MDp7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjQ3OiJodHRwczovL21pYS50ZXN0L2FkbWluL3BhZ28tc3VzY3JpcGNpb25zLzEvZWRpdCI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fXM6NTA6ImxvZ2luX3dlYl81OWJhMzZhZGRjMmIyZjk0MDE1ODBmMDE0YzdmNThlYTRlMzA5ODlkIjtpOjE7czoxNzoicGFzc3dvcmRfaGFzaF93ZWIiO3M6NjA6IiQyeSQxMiRXRWxoUlF1YjhqSjU0Rkswa3ZQbXRlbzdvT1BrZzhXSkp5ZzBIUEkxMVhJR0xscUxKa25VMiI7czo4OiJmaWxhbWVudCI7YTowOnt9fQ==", **********, 1, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "OGF0xmQNkiM3P99CWPuIofWU8bS7yWbRQ0GqToUS"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 140}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 176}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 245}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 130}], "start": **********.19792, "duration": 0.00278, "duration_str": "2.78ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:173", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=173", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "173"}, "connection": "proyecto-mba", "explain": null, "start_percent": 90.674, "width_percent": 9.326}]}, "models": {"data": {"App\\Models\\Suscripcion": {"value": 45, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FModels%2FSuscripcion.php&line=1", "ajax": false, "filename": "Suscripcion.php", "line": "?"}}, "App\\Models\\Negocio": {"value": 45, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FModels%2FNegocio.php&line=1", "ajax": false, "filename": "Negocio.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\PagoSuscripcion": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FModels%2FPagoSuscripcion.php&line=1", "ajax": false, "filename": "PagoSuscripcion.php", "line": "?"}}}, "count": 94, "is_counter": true}, "livewire": {"data": {"app.filament.resources.pago-suscripcion-resource.pages.edit-pago-suscripcion #1CR9Icn0cKkRVQ5WKYxV": "array:4 [\n  \"data\" => array:19 [\n    \"data\" => array:9 [\n      \"id\" => 1\n      \"suscripcion_id\" => 39\n      \"metodo_pago\" => \"Non doloribus ea dui\"\n      \"importe\" => \"97.00\"\n      \"fecha_pago\" => \"1988-09-18\"\n      \"transaccion_id\" => \"At velit magni debit\"\n      \"estado\" => \"Enim id aliquid eni\"\n      \"created_at\" => \"2025-06-28T14:14:57.000000Z\"\n      \"updated_at\" => \"2025-06-28T14:14:57.000000Z\"\n    ]\n    \"previousUrl\" => \"https://mia.test/admin/pago-suscripcions/create\"\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"activeRelationManager\" => null\n    \"record\" => App\\Models\\PagoSuscripcion {#2957\n      #connection: \"mysql\"\n      #table: \"pago_suscripcions\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:9 [\n        \"id\" => 1\n        \"suscripcion_id\" => 39\n        \"metodo_pago\" => \"Non doloribus ea dui\"\n        \"importe\" => \"97.00\"\n        \"fecha_pago\" => \"1988-09-18\"\n        \"transaccion_id\" => \"At velit magni debit\"\n        \"estado\" => \"Enim id aliquid eni\"\n        \"created_at\" => \"2025-06-28 14:14:57\"\n        \"updated_at\" => \"2025-06-28 14:14:57\"\n      ]\n      #original: array:9 [\n        \"id\" => 1\n        \"suscripcion_id\" => 39\n        \"metodo_pago\" => \"Non doloribus ea dui\"\n        \"importe\" => \"97.00\"\n        \"fecha_pago\" => \"1988-09-18\"\n        \"transaccion_id\" => \"At velit magni debit\"\n        \"estado\" => \"Enim id aliquid eni\"\n        \"created_at\" => \"2025-06-28 14:14:57\"\n        \"updated_at\" => \"2025-06-28 14:14:57\"\n      ]\n      #changes: []\n      #casts: []\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: array:6 [\n        0 => \"suscripcion_id\"\n        1 => \"metodo_pago\"\n        2 => \"importe\"\n        3 => \"transaccion_id\"\n        4 => \"estado\"\n        5 => \"fecha_pago\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n    }\n    \"savedDataHash\" => null\n  ]\n  \"name\" => \"app.filament.resources.pago-suscripcion-resource.pages.edit-pago-suscripcion\"\n  \"component\" => \"App\\Filament\\Resources\\PagoSuscripcionResource\\Pages\\EditPagoSuscripcion\"\n  \"id\" => \"1CR9Icn0cKkRVQ5WKYxV\"\n]", "filament.livewire.global-search #yJC7xe8uaqYlnMZSSiH4": "array:4 [\n  \"data\" => array:1 [\n    \"search\" => \"\"\n  ]\n  \"name\" => \"filament.livewire.global-search\"\n  \"component\" => \"Filament\\Livewire\\GlobalSearch\"\n  \"id\" => \"yJC7xe8uaqYlnMZSSiH4\"\n]", "filament.livewire.notifications #p50v13P798rMCWgd3top": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#2575\n      #items: array:1 [\n        \"9f437123-8a29-4935-88bf-b8a2a43503c3\" => Filament\\Notifications\\Notification {#2586\n          #evaluationIdentifier: ? string\n          #view: \"filament-notifications::notification\"\n          #defaultView: null\n          #viewData: array:1 [\n            0 => []\n          ]\n          #viewIdentifier: \"notification\"\n          #safeViews: []\n          #isInline: false\n          #actions: []\n          #body: null\n          #date: null\n          #duration: 6000\n          #icon: \"heroicon-o-check-circle\"\n          #iconPosition: null\n          #iconSize: null\n          #iconColor: \"success\"\n          #id: \"9f437123-8a29-4935-88bf-b8a2a43503c3\"\n          #status: \"success\"\n          #title: \"Creado\"\n          #color: null\n          #defaultColor: null\n        }\n      ]\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"p50v13P798rMCWgd3top\"\n]"}, "count": 3}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 75, "messages": [{"message": "[\n  ability => system.access-panel,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1201185309 data-indent-pad=\"  \"><span class=sf-dump-note>system.access-panel </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">system.access-panel</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1201185309\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.894016, "xdebug_link": null}, {"message": "[\n  ability => system.admin-dashboard,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-44294065 data-indent-pad=\"  \"><span class=sf-dump-note>system.admin-dashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">system.admin-dashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-44294065\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.903012, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\PagoSuscripcion(id=1),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\PagoSuscripcion)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-974755903 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\PagoSuscripcion(id=1)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\PagoSuscripcion(id=1)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\PagoSuscripcion)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-974755903\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.903196, "xdebug_link": null}, {"message": "[\n  ability => system.admin-dashboard,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1743662574 data-indent-pad=\"  \"><span class=sf-dump-note>system.admin-dashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">system.admin-dashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1743662574\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.917726, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\PagoSuscripcion,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\PagoSuscripcion]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1469216743 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\PagoSuscripcion</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\PagoSuscripcion</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; App\\Models\\PagoSuscripcion]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1469216743\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.917891, "xdebug_link": null}, {"message": "[\n  ability => system.admin-dashboard,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1049909029 data-indent-pad=\"  \"><span class=sf-dump-note>system.admin-dashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">system.admin-dashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1049909029\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.923578, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\PagoSuscripcion(id=1),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\PagoSuscripcion)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1594009376 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\PagoSuscripcion(id=1)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\PagoSuscripcion(id=1)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\PagoSuscripcion)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1594009376\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.923775, "xdebug_link": null}, {"message": "[\n  ability => system.admin-dashboard,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-368410236 data-indent-pad=\"  \"><span class=sf-dump-note>system.admin-dashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">system.admin-dashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-368410236\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.056164, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\PagoSuscripcion(id=1),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\PagoSuscripcion)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1443728611 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\PagoSuscripcion(id=1)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\PagoSuscripcion(id=1)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\PagoSuscripcion)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1443728611\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.056357, "xdebug_link": null}, {"message": "[\n  ability => system.admin-dashboard,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-590421499 data-indent-pad=\"  \"><span class=sf-dump-note>system.admin-dashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">system.admin-dashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-590421499\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.057961, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\PagoSuscripcion(id=1),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\PagoSuscripcion)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2107645 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\PagoSuscripcion(id=1)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\PagoSuscripcion(id=1)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\PagoSuscripcion)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2107645\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.05814, "xdebug_link": null}, {"message": "[\n  ability => system.admin,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1805114278 data-indent-pad=\"  \"><span class=sf-dump-note>system.admin </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">system.admin</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1805114278\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.078101, "xdebug_link": null}, {"message": "[\n  ability => user.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-825186135 data-indent-pad=\"  \"><span class=sf-dump-note>user.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">user.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-825186135\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.079839, "xdebug_link": null}, {"message": "[\n  ability => user.admin-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1923641258 data-indent-pad=\"  \"><span class=sf-dump-note>user.admin-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">user.admin-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1923641258\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.080467, "xdebug_link": null}, {"message": "[\n  ability => system.admin,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1559023178 data-indent-pad=\"  \"><span class=sf-dump-note>system.admin </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">system.admin</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1559023178\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.08111, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\AppVersion,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\AppVersion]\n]", "message_html": "<pre class=sf-dump id=sf-dump-606472710 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\AppVersion</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\AppVersion</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"28 characters\">[0 =&gt; App\\Models\\AppVersion]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-606472710\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.082244, "xdebug_link": null}, {"message": "[\n  ability => categoria.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-903899874 data-indent-pad=\"  \"><span class=sf-dump-note>categoria.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">categoria.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-903899874\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.083511, "xdebug_link": null}, {"message": "[\n  ability => categoria.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-156992547 data-indent-pad=\"  \"><span class=sf-dump-note>categoria.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">categoria.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-156992547\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.084796, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Categoria,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Categoria]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1502934908 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Categoria</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Categoria</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Categoria]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1502934908\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.084968, "xdebug_link": null}, {"message": "[\n  ability => user.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2033020325 data-indent-pad=\"  \"><span class=sf-dump-note>user.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">user.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2033020325\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.085816, "xdebug_link": null}, {"message": "[\n  ability => user.admin-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1496345700 data-indent-pad=\"  \"><span class=sf-dump-note>user.admin-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">user.admin-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1496345700\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.08639, "xdebug_link": null}, {"message": "[\n  ability => system.admin-dashboard,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1348664882 data-indent-pad=\"  \"><span class=sf-dump-note>system.admin-dashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">system.admin-dashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1348664882\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.087003, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Evento,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Evento]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1741831975 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Evento</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Evento</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Evento]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1741831975\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.087988, "xdebug_link": null}, {"message": "[\n  ability => localidad.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-528891953 data-indent-pad=\"  \"><span class=sf-dump-note>localidad.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localidad.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-528891953\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.088587, "xdebug_link": null}, {"message": "[\n  ability => localidad.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-945050936 data-indent-pad=\"  \"><span class=sf-dump-note>localidad.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localidad.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-945050936\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.089889, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Localidad,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Localidad]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1144536532 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Localidad</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Localidad</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Localidad]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1144536532\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.090037, "xdebug_link": null}, {"message": "[\n  ability => negocio.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-550061680 data-indent-pad=\"  \"><span class=sf-dump-note>negocio.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">negocio.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-550061680\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.090609, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Negocio,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Negocio]\n]", "message_html": "<pre class=sf-dump id=sf-dump-319332598 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Negocio</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Negocio</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Negocio]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-319332598\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.09148, "xdebug_link": null}, {"message": "[\n  ability => system.admin-dashboard,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-559635192 data-indent-pad=\"  \"><span class=sf-dump-note>system.admin-dashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">system.admin-dashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-559635192\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.092786, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\PagoSuscripcion,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\PagoSuscripcion]\n]", "message_html": "<pre class=sf-dump id=sf-dump-527708811 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\PagoSuscripcion</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\PagoSuscripcion</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; App\\Models\\PagoSuscripcion]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-527708811\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.092932, "xdebug_link": null}, {"message": "[\n  ability => system.permissions,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1935326476 data-indent-pad=\"  \"><span class=sf-dump-note>system.permissions </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">system.permissions</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1935326476\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.093658, "xdebug_link": null}, {"message": "[\n  ability => system.permissions,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1478366071 data-indent-pad=\"  \"><span class=sf-dump-note>system.permissions </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">system.permissions</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1478366071\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.095815, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\Permission\\Models\\Permission,\n  result => true,\n  user => 1,\n  arguments => [0 => <PERSON><PERSON>\\Permission\\Models\\Permission]\n]", "message_html": "<pre class=sf-dump id=sf-dump-968960099 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Permission\\Models\\Permission</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Spatie\\Permission\\Models\\Permission</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"42 characters\">[0 =&gt; Spatie\\Permission\\Models\\Permission]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-968960099\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.095984, "xdebug_link": null}, {"message": "[\n  ability => system.roles,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-300055937 data-indent-pad=\"  \"><span class=sf-dump-note>system.roles </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">system.roles</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-300055937\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.09689, "xdebug_link": null}, {"message": "[\n  ability => system.roles,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-437175630 data-indent-pad=\"  \"><span class=sf-dump-note>system.roles </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">system.roles</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-437175630\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.098303, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\Permission\\Models\\Role,\n  result => true,\n  user => 1,\n  arguments => [0 => <PERSON><PERSON>\\Permission\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1239478212 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Permission\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Spatie\\Permission\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"36 characters\">[0 =&gt; Spatie\\Permission\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1239478212\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.098468, "xdebug_link": null}, {"message": "[\n  ability => user.admin-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1513875979 data-indent-pad=\"  \"><span class=sf-dump-note>user.admin-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">user.admin-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1513875979\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.099448, "xdebug_link": null}, {"message": "[\n  ability => user.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-994228392 data-indent-pad=\"  \"><span class=sf-dump-note>user.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">user.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-994228392\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.100888, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-890414709 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-890414709\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.101049, "xdebug_link": null}, {"message": "[\n  ability => zona.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1908904634 data-indent-pad=\"  \"><span class=sf-dump-note>zona.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">zona.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1908904634\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.101703, "xdebug_link": null}, {"message": "[\n  ability => zona.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1130074838 data-indent-pad=\"  \"><span class=sf-dump-note>zona.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">zona.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1130074838\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.10345, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Zona,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Zona]\n]", "message_html": "<pre class=sf-dump id=sf-dump-999840752 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Zona</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Zona</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Zona]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-999840752\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.103609, "xdebug_link": null}, {"message": "[\n  ability => system.admin-dashboard,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-920815804 data-indent-pad=\"  \"><span class=sf-dump-note>system.admin-dashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">system.admin-dashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-920815804\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.110394, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\PagoSuscripcion,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\PagoSuscripcion]\n]", "message_html": "<pre class=sf-dump id=sf-dump-65282879 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\PagoSuscripcion</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\PagoSuscripcion</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; App\\Models\\PagoSuscripcion]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-65282879\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.110563, "xdebug_link": null}, {"message": "[\n  ability => system.admin,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1411828263 data-indent-pad=\"  \"><span class=sf-dump-note>system.admin </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">system.admin</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1411828263\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.129265, "xdebug_link": null}, {"message": "[\n  ability => user.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-131849887 data-indent-pad=\"  \"><span class=sf-dump-note>user.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">user.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-131849887\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.130193, "xdebug_link": null}, {"message": "[\n  ability => user.admin-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-879045213 data-indent-pad=\"  \"><span class=sf-dump-note>user.admin-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">user.admin-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-879045213\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.130825, "xdebug_link": null}, {"message": "[\n  ability => system.admin,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-161551797 data-indent-pad=\"  \"><span class=sf-dump-note>system.admin </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">system.admin</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-161551797\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.131409, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\AppVersion,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\AppVersion]\n]", "message_html": "<pre class=sf-dump id=sf-dump-306278631 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\AppVersion</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\AppVersion</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"28 characters\">[0 =&gt; App\\Models\\AppVersion]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-306278631\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.132111, "xdebug_link": null}, {"message": "[\n  ability => categoria.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1298820305 data-indent-pad=\"  \"><span class=sf-dump-note>categoria.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">categoria.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1298820305\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.132965, "xdebug_link": null}, {"message": "[\n  ability => categoria.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2111942813 data-indent-pad=\"  \"><span class=sf-dump-note>categoria.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">categoria.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2111942813\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.134011, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Categoria,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Categoria]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1665187768 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Categoria</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Categoria</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Categoria]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1665187768\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.134347, "xdebug_link": null}, {"message": "[\n  ability => user.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-196974508 data-indent-pad=\"  \"><span class=sf-dump-note>user.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">user.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-196974508\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.13522, "xdebug_link": null}, {"message": "[\n  ability => user.admin-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1785526909 data-indent-pad=\"  \"><span class=sf-dump-note>user.admin-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">user.admin-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1785526909\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.135798, "xdebug_link": null}, {"message": "[\n  ability => system.admin-dashboard,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1426270866 data-indent-pad=\"  \"><span class=sf-dump-note>system.admin-dashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">system.admin-dashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1426270866\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.13642, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Evento,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Evento]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1247196268 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Evento</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Evento</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Evento]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1247196268\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.137083, "xdebug_link": null}, {"message": "[\n  ability => localidad.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-860201259 data-indent-pad=\"  \"><span class=sf-dump-note>localidad.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localidad.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-860201259\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.137632, "xdebug_link": null}, {"message": "[\n  ability => localidad.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1979909041 data-indent-pad=\"  \"><span class=sf-dump-note>localidad.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localidad.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1979909041\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.138439, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Localidad,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Localidad]\n]", "message_html": "<pre class=sf-dump id=sf-dump-412153288 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Localidad</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Localidad</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Localidad]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-412153288\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.138581, "xdebug_link": null}, {"message": "[\n  ability => negocio.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1817294739 data-indent-pad=\"  \"><span class=sf-dump-note>negocio.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">negocio.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1817294739\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.13917, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Negocio,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Negocio]\n]", "message_html": "<pre class=sf-dump id=sf-dump-42657410 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Negocio</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Negocio</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Negocio]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-42657410\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.139783, "xdebug_link": null}, {"message": "[\n  ability => system.admin-dashboard,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-818674145 data-indent-pad=\"  \"><span class=sf-dump-note>system.admin-dashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">system.admin-dashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-818674145\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.141018, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\PagoSuscripcion,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\PagoSuscripcion]\n]", "message_html": "<pre class=sf-dump id=sf-dump-798546090 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\PagoSuscripcion</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\PagoSuscripcion</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; App\\Models\\PagoSuscripcion]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-798546090\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.141169, "xdebug_link": null}, {"message": "[\n  ability => system.permissions,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1766305111 data-indent-pad=\"  \"><span class=sf-dump-note>system.permissions </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">system.permissions</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1766305111\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.141902, "xdebug_link": null}, {"message": "[\n  ability => system.permissions,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1748240793 data-indent-pad=\"  \"><span class=sf-dump-note>system.permissions </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">system.permissions</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1748240793\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.143077, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\Permission\\Models\\Permission,\n  result => true,\n  user => 1,\n  arguments => [0 => <PERSON><PERSON>\\Permission\\Models\\Permission]\n]", "message_html": "<pre class=sf-dump id=sf-dump-600431889 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Permission\\Models\\Permission</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Spatie\\Permission\\Models\\Permission</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"42 characters\">[0 =&gt; Spatie\\Permission\\Models\\Permission]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-600431889\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.143241, "xdebug_link": null}, {"message": "[\n  ability => system.roles,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-286201439 data-indent-pad=\"  \"><span class=sf-dump-note>system.roles </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">system.roles</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-286201439\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.144017, "xdebug_link": null}, {"message": "[\n  ability => system.roles,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-695739582 data-indent-pad=\"  \"><span class=sf-dump-note>system.roles </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">system.roles</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-695739582\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.145019, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\Permission\\Models\\Role,\n  result => true,\n  user => 1,\n  arguments => [0 => <PERSON><PERSON>\\Permission\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1505037537 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Permission\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Spatie\\Permission\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"36 characters\">[0 =&gt; Spatie\\Permission\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1505037537\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.145159, "xdebug_link": null}, {"message": "[\n  ability => user.admin-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1846873591 data-indent-pad=\"  \"><span class=sf-dump-note>user.admin-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">user.admin-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1846873591\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.145893, "xdebug_link": null}, {"message": "[\n  ability => user.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-577945091 data-indent-pad=\"  \"><span class=sf-dump-note>user.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">user.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-577945091\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.146889, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1193481911 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1193481911\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.147037, "xdebug_link": null}, {"message": "[\n  ability => zona.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1678249225 data-indent-pad=\"  \"><span class=sf-dump-note>zona.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">zona.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1678249225\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.147539, "xdebug_link": null}, {"message": "[\n  ability => zona.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-322696724 data-indent-pad=\"  \"><span class=sf-dump-note>zona.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">zona.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-322696724\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.148378, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Zona,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Zona]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1988784296 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Zona</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Zona</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Zona]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1988784296\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.148519, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "https://mia.test/admin/pago-suscripcions/1/edit", "action_name": "filament.admin.resources.pago-suscripcions.edit", "controller_action": "App\\Filament\\Resources\\PagoSuscripcionResource\\Pages\\EditPagoSuscripcion", "uri": "GET admin/pago-suscripcions/{record}/edit", "controller": "App\\Filament\\Resources\\PagoSuscripcionResource\\Pages\\EditPagoSuscripcion@render<a href=\"phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/pago-suscripcions", "file": "<a href=\"phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Pages/BasePage.php:51-59</a>", "middleware": "panel:admin, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Filament\\Http\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Filament\\Http\\Middleware\\Authenticate", "duration": "1.14s", "peak_memory": "56MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mia.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"57 characters\">&quot;Brave&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"96 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">es-ES,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">https://mia.test/admin/pago-suscripcions/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"709 characters\">XSRF-TOKEN=eyJpdiI6IjhlOXM5bHBHbnRBMWpxaDNTZGFqaFE9PSIsInZhbHVlIjoiQURpblI5bUJoZ3poekExc1NaSytETVgxbFk1L2htQmFOVWtqNjUzMWY2Wm1vZWdnNTNpSTZXTGlUR3A1OEZyZFhXVGZwVFBkd044OGZNQitiTW1HaDQvRjRzVGRZL0ttaXp5aUtndm85VDlkMFJGM0NXN1pPR282V1NJbzZuTGkiLCJtYWMiOiJlMjk4MmUyMzE2N2QzNDI3ZDQ4MDY3MDljYmNiNmIzNGZmZmVjYWQ2MWU2Njc5ZTQzMzRkOTI0MjQ0MjcxNzgyIiwidGFnIjoiIn0%3D; mia_session=eyJpdiI6IlR4Zkg2YzR1ckl1NXhpU25tTEErSVE9PSIsInZhbHVlIjoia3JVZVRLSDIwUHZ4VHZPMHRVbGFHOW5YMFVnR3JyNm13Y2NkcWlMR3RNbHVWNzl1RldnWEV2N0R3T3pmd0l4QkxiY01YblVFWnE0cHp3S3kyamFmZjlyUWw3bXJiT0w0aE01aGhpUGhFQllWYXJ0L2F6OENtYXAzUHlYcUpFYWoiLCJtYWMiOiJiY2RjMzc4MGM3NWIzMmFhYWQwZTdlM2Q3MTBmNWJkNDE5NWFhYjIyYjM5MDgyNDlmYTU5MzBmYjA4NjAzMzg2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1775235635 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lFynM1nWC3wiPdtrhm824NtAYHiYMEa27jvCoqJB</span>\"\n  \"<span class=sf-dump-key>mia_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OGF0xmQNkiM3P99CWPuIofWU8bS7yWbRQ0GqToUS</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1775235635\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1130783367 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 14:15:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"436 characters\">XSRF-TOKEN=eyJpdiI6IktFU0h6amRjOG9LM09iT0JNamVvOFE9PSIsInZhbHVlIjoiOUY5bUZNUmR6ZW9VZjJ6TnhoV1J4bElXb1Z0S3dMbjAxQmtKNThuT3RSQk02N3hIcGwwOTRHYXl5YzFUUi9kMFBDejdVU0oyV0lScTdsejQvWXNEb1VwbmQ1MGhnZFF0T3lMYVlCNFV0b3o3V2NmVXZyRi9iakd1ckoyWDBrMTQiLCJtYWMiOiJhMGY1MGU0MDRkNDg5OGU3ZjFiY2IxYWI4YTQ0MzljNGZlYWM3ZDEzMjNmM2NlZjQ3OGQ0ZTIyODg3ZGNlMGUwIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 16:15:00 GMT; Max-Age=7200; path=/; secure; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"447 characters\">mia_session=eyJpdiI6ImVsN2Nvdm4yakkzc0pyUERSV1ViSFE9PSIsInZhbHVlIjoiNENjNkhzRjhxNjV5Y3R2TTNIRGk3T0tvak1wa0N4UStDOFdnUS9DYktzSDJqTVhua1lUMUNLQUp5NlBMYjM3RHhCRDd6RkkvMzZCUUdZM3FEcTI0dnpGLzB1dmtreUNNckYrWFJWNU9qL3VkaGhvTjQ4V0d4RXlIMDAvQnlvWGkiLCJtYWMiOiJkMTk2YmM2MzVjMDhiY2Y5YmZjOTI4OWFlNGIwOTdiY2E5Y2M1Mzk1M2YxNTJkMDEzOTBmZjE5ZmNkMDRlMjg3IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 16:15:00 GMT; Max-Age=7200; path=/; secure; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"408 characters\">XSRF-TOKEN=eyJpdiI6IktFU0h6amRjOG9LM09iT0JNamVvOFE9PSIsInZhbHVlIjoiOUY5bUZNUmR6ZW9VZjJ6TnhoV1J4bElXb1Z0S3dMbjAxQmtKNThuT3RSQk02N3hIcGwwOTRHYXl5YzFUUi9kMFBDejdVU0oyV0lScTdsejQvWXNEb1VwbmQ1MGhnZFF0T3lMYVlCNFV0b3o3V2NmVXZyRi9iakd1ckoyWDBrMTQiLCJtYWMiOiJhMGY1MGU0MDRkNDg5OGU3ZjFiY2IxYWI4YTQ0MzljNGZlYWM3ZDEzMjNmM2NlZjQ3OGQ0ZTIyODg3ZGNlMGUwIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 16:15:00 GMT; path=/; secure</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"419 characters\">mia_session=eyJpdiI6ImVsN2Nvdm4yakkzc0pyUERSV1ViSFE9PSIsInZhbHVlIjoiNENjNkhzRjhxNjV5Y3R2TTNIRGk3T0tvak1wa0N4UStDOFdnUS9DYktzSDJqTVhua1lUMUNLQUp5NlBMYjM3RHhCRDd6RkkvMzZCUUdZM3FEcTI0dnpGLzB1dmtreUNNckYrWFJWNU9qL3VkaGhvTjQ4V0d4RXlIMDAvQnlvWGkiLCJtYWMiOiJkMTk2YmM2MzVjMDhiY2Y5YmZjOTI4OWFlNGIwOTdiY2E5Y2M1Mzk1M2YxNTJkMDEzOTBmZjE5ZmNkMDRlMjg3IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 16:15:00 GMT; path=/; secure; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1130783367\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-367812054 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lFynM1nWC3wiPdtrhm824NtAYHiYMEa27jvCoqJB</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"47 characters\">https://mia.test/admin/pago-suscripcions/1/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$WElhRQub8jJ54FK0kvPmteo7oOPkg8WJJyg0HPI11XIGLlqLJknU2</span>\"\n  \"<span class=sf-dump-key>filament</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-367812054\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://mia.test/admin/pago-suscripcions/1/edit", "action_name": "filament.admin.resources.pago-suscripcions.edit", "controller_action": "App\\Filament\\Resources\\PagoSuscripcionResource\\Pages\\EditPagoSuscripcion"}, "badge": null}}