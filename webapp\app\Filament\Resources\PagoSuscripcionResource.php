<?php

namespace App\Filament\Resources;

use App\Filament\Resources\PagoSuscripcionResource\Pages;
use App\Filament\Resources\PagoSuscripcionResource\RelationManagers;
use App\Models\PagoSuscripcion;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class PagoSuscripcionResource extends Resource
{
    protected static ?string $model = PagoSuscripcion::class;

    protected static ?string $navigationIcon = 'heroicon-o-currency-euro';

    protected static ?string $navigationGroup = 'Suscripciones';

    protected static ?string $navigationLabel = 'Pagos';

    protected static ?string $recordTitleAttribute = 'suscripcion.negocio.nombre';

    protected static ?string $label = 'Pago';

    protected static ?string $pluralLabel = 'Pagos';



    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make('suscripcion_id')
                    ->relationship('suscripcion', 'id')
                    ->getOptionLabelFromRecordUsing(fn($record) => $record->negocio->nombre ?? 'Sin negocio')
                    ->required(),
                Forms\Components\TextInput::make('metodo_pago')
                    ->required(),
                Forms\Components\TextInput::make('importe')
                    ->required()
                    ->numeric(),
                Forms\Components\DatePicker::make('fecha_pago')
                    ->required(),
                Forms\Components\TextInput::make('transaccion_id'),
                Forms\Components\TextInput::make('estado'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('suscripcion.negocio.nombre')
                    ->label('Negocio')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('metodo_pago')
                    ->label('Método de Pago')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('importe')
                    ->label('Importe')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('fecha_pago')
                    ->label('Fecha de Pago')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('transaccion_id')
                    ->label('Transacción ID')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('estado')
                    ->label('Estado')
                    ->searchable()
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPagoSuscripcions::route('/'),
            'create' => Pages\CreatePagoSuscripcion::route('/create'),
            'edit' => Pages\EditPagoSuscripcion::route('/{record}/edit'),
        ];
    }
}
