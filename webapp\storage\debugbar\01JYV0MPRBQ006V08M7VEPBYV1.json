{"__meta": {"id": "01JYV0MPRBQ006V08M7VEPBYV1", "datetime": "2025-06-28 10:17:01", "utime": **********.451872, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751105819.851284, "end": **********.451888, "duration": 1.6006040573120117, "duration_str": "1.6s", "measures": [{"label": "Booting", "start": 1751105819.851284, "relative_start": 0, "end": **********.627382, "relative_end": **********.627382, "duration": 0.****************, "duration_str": "776ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.627404, "relative_start": 0.****************, "end": **********.45189, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "824ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.381428, "relative_start": 1.****************, "end": **********.38343, "relative_end": **********.38343, "duration": 0.0020020008087158203, "duration_str": "2ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: filament.widgets.suscriptions-summary", "start": **********.43494, "relative_start": 1.****************, "end": **********.43494, "relative_end": **********.43494, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d2b27f499c5f47b7f077d6781417d93d", "start": **********.443027, "relative_start": 1.***************, "end": **********.443027, "relative_end": **********.443027, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::22ebec865223eccfbe3f9931e31c212a", "start": **********.444328, "relative_start": 1.5930440425872803, "end": **********.444328, "relative_end": **********.444328, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7f15f8c3b13378c90320e903f3b0f1c6", "start": **********.44491, "relative_start": 1.5936260223388672, "end": **********.44491, "relative_end": **********.44491, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.44956, "relative_start": 1.598275899887085, "end": **********.450182, "relative_end": **********.450182, "duration": 0.0006220340728759766, "duration_str": "622μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 45542072, "peak_usage_str": "43MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.45.0", "PHP Version": "8.3.8", "Environment": "local", "Debug Mode": "Enabled", "URL": "mia.test", "Timezone": "UTC", "Locale": "es"}}, "views": {"count": 4, "nb_templates": 4, "templates": [{"name": "filament.widgets.suscriptions-summary", "param_count": null, "params": [], "start": **********.434905, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\resources\\views/filament/widgets/suscriptions-summary.blade.phpfilament.widgets.suscriptions-summary", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fsuscriptions-summary.blade.php&line=1", "ajax": false, "filename": "suscriptions-summary.blade.php", "line": "?"}}, {"name": "__components::d2b27f499c5f47b7f077d6781417d93d", "param_count": null, "params": [], "start": **********.44298, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\storage\\framework\\views/d2b27f499c5f47b7f077d6781417d93d.blade.php__components::d2b27f499c5f47b7f077d6781417d93d", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fstorage%2Fframework%2Fviews%2Fd2b27f499c5f47b7f077d6781417d93d.blade.php&line=1", "ajax": false, "filename": "d2b27f499c5f47b7f077d6781417d93d.blade.php", "line": "?"}}, {"name": "__components::22ebec865223eccfbe3f9931e31c212a", "param_count": null, "params": [], "start": **********.444173, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\storage\\framework\\views/22ebec865223eccfbe3f9931e31c212a.blade.php__components::22ebec865223eccfbe3f9931e31c212a", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fstorage%2Fframework%2Fviews%2F22ebec865223eccfbe3f9931e31c212a.blade.php&line=1", "ajax": false, "filename": "22ebec865223eccfbe3f9931e31c212a.blade.php", "line": "?"}}, {"name": "__components::7f15f8c3b13378c90320e903f3b0f1c6", "param_count": null, "params": [], "start": **********.444819, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\storage\\framework\\views/7f15f8c3b13378c90320e903f3b0f1c6.blade.php__components::7f15f8c3b13378c90320e903f3b0f1c6", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fstorage%2Fframework%2Fviews%2F7f15f8c3b13378c90320e903f3b0f1c6.blade.php&line=1", "ajax": false, "filename": "7f15f8c3b13378c90320e903f3b0f1c6.blade.php", "line": "?"}}]}, "queries": {"count": 7, "nb_statements": 7, "nb_visible_statements": 7, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00552, "accumulated_duration_str": "5.52ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'LqdP3Q0O7a3cwoJmYgCvHDyb4m34SlxmUHxhBxPb' limit 1", "type": "query", "params": [], "bindings": ["LqdP3Q0O7a3cwoJmYgCvHDyb4m34SlxmUHxhBxPb"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 105}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 89}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.3882852, "duration": 0.00239, "duration_str": "2.39ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "proyecto-mba", "explain": null, "start_percent": 0, "width_percent": 43.297}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 180}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.4019198, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "proyecto-mba", "explain": null, "start_percent": 43.297, "width_percent": 10.326}, {"sql": "select * from `cache` where `key` in ('spatie.permission.cache')", "type": "query", "params": [], "bindings": ["spatie.permission.cache"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 418}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.404906, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "proyecto-mba", "explain": null, "start_percent": 53.623, "width_percent": 5.435}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (1) and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 571}], "start": **********.411797, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:328", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=328", "ajax": false, "filename": "HasPermissions.php", "line": "328"}, "connection": "proyecto-mba", "explain": null, "start_percent": 59.058, "width_percent": 12.138}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 314}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}], "start": **********.414051, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "proyecto-mba", "explain": null, "start_percent": 71.196, "width_percent": 6.159}, {"sql": "select count(*) as aggregate from `negocios` where exists (select * from `suscripciones` where `negocios`.`id` = `suscripciones`.`negocio_id` and `status` = 1)", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/SuscriptionsSummary.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Widgets\\SuscriptionsSummary.php", "line": 21}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.428898, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "SuscriptionsSummary.php:21", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/SuscriptionsSummary.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Widgets\\SuscriptionsSummary.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FWidgets%2FSuscriptionsSummary.php&line=21", "ajax": false, "filename": "SuscriptionsSummary.php", "line": "21"}, "connection": "proyecto-mba", "explain": null, "start_percent": 77.355, "width_percent": 15.036}, {"sql": "select count(*) as aggregate from `negocios` where exists (select * from `suscripciones` where `negocios`.`id` = `suscripciones`.`negocio_id` and `status` = 0)", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/SuscriptionsSummary.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Widgets\\SuscriptionsSummary.php", "line": 25}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.431613, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "SuscriptionsSummary.php:25", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/SuscriptionsSummary.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Widgets\\SuscriptionsSummary.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FWidgets%2FSuscriptionsSummary.php&line=25", "ajax": false, "filename": "SuscriptionsSummary.php", "line": "25"}, "connection": "proyecto-mba", "explain": null, "start_percent": 92.391, "width_percent": 7.609}]}, "models": {"data": {"Spatie\\Permission\\Models\\Role": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 3, "is_counter": true}, "livewire": {"data": {"app.filament.widgets.suscriptions-summary #xm4Ir8oDyEu8i3dtPlU7": "array:4 [\n  \"data\" => array:3 [\n    \"negociosActivos\" => 40\n    \"negociosInactivos\" => 7\n    \"negociosTotales\" => 47\n  ]\n  \"name\" => \"app.filament.widgets.suscriptions-summary\"\n  \"component\" => \"App\\Filament\\Widgets\\SuscriptionsSummary\"\n  \"id\" => \"xm4Ir8oDyEu8i3dtPlU7\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 2, "messages": [{"message": "[\n  ability => system.access-panel,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1956699093 data-indent-pad=\"  \"><span class=sf-dump-note>system.access-panel </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">system.access-panel</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1956699093\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.418163, "xdebug_link": null}, {"message": "[\n  ability => system.access-panel,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-834960413 data-indent-pad=\"  \"><span class=sf-dump-note>system.access-panel </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">system.access-panel</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-834960413\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.423668, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "https://mia.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>", "middleware": "web", "duration": "1.19s", "peak_memory": "48MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1821277554 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1821277554\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1821004232 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">AgJTb7TMVHyj2iNWz2N8rcvkS7xhFsqvfp5eqrTe</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"381 characters\">{&quot;data&quot;:{&quot;negociosActivos&quot;:null,&quot;negociosInactivos&quot;:null,&quot;negociosTotales&quot;:null},&quot;memo&quot;:{&quot;id&quot;:&quot;xm4Ir8oDyEu8i3dtPlU7&quot;,&quot;name&quot;:&quot;app.filament.widgets.suscriptions-summary&quot;,&quot;path&quot;:&quot;admin&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;lazyLoaded&quot;:false,&quot;lazyIsolated&quot;:true,&quot;errors&quot;:[],&quot;locale&quot;:&quot;es&quot;},&quot;checksum&quot;:&quot;e00dfff1177c2e45c4810660a61feed18ad43aee0d77b22a8833983f3f66162a&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"10 characters\">__lazyLoad</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"248 characters\">eyJkYXRhIjp7ImZvck1vdW50IjpbW10seyJzIjoiYXJyIn1dfSwibWVtbyI6eyJpZCI6IjdUcWtjUW83VEJiNXFrZlBZM0ZhIiwibmFtZSI6Il9fbW91bnRQYXJhbXNDb250YWluZXIifSwiY2hlY2tzdW0iOiJlYzQ5NTZlNWY4N2Y2NDY3ZDRlOTEyODhjODQwOTExZDU0NWE3Mzg1Yjc0ZjIxOTkwNzFhNzE4OTU5YzJhZDFkIn0=</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1821004232\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1836607568 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mia.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">830</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"57 characters\">&quot;Brave&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">es-ES,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">https://mia.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">https://mia.test/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"709 characters\">XSRF-TOKEN=eyJpdiI6IkNCVVYvMlNvSXdTMnVuREZsVExzU1E9PSIsInZhbHVlIjoiYndDbC9PQ3B2T2xlRmpNdGx5R2ZjWGdkWHNORUVzbkVXcTRsZWExejJFeE5HNDNXU3BSOXdiWFc5TTBxSnlDQUhzS3hKWUxzUXk4d2g0dEZ3WnVhWlBrYjNKRVZ1SUh2MVVyZWZqVFFWQzgvRG54YWJ4MUVMVjZYc1VQcVZYekYiLCJtYWMiOiIwMzcyNTM0MTkyOGY1YTU2M2E0NDU3ZTE0ZmM5ZTUwZTFlNjkzZWI2YmQxOGFlOGY0MmNkNTdiOTFmMzBlNWJjIiwidGFnIjoiIn0%3D; mia_session=eyJpdiI6IkVqcGo3bGljUDZLYmtML0VaQU1Cemc9PSIsInZhbHVlIjoiN0FPbUQwWUF1a2VwQ0czY2lOY2xYU0MvUGwwSDRLcW4weU5qeDMvVjNOd3dVWWx2dkl2ZXlmaVlTNDZIdHUyQzE0Y1cxQnc4UU8vOXhwZW5HY21xS0RmQllRRXZ0QytSNkl4dVFvRGVmcHZqbCtkN0lET3kya0kvckp2WmU5YzUiLCJtYWMiOiJmNDVlYjlhZTViMzc0M2VmNmNmNzVhMjRhMzk0Yzg4NWM4YTNmZmEwY2RhNDUwMDUwOWJiYTM3MzI2ZGMyN2FlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1836607568\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-253668224 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">AgJTb7TMVHyj2iNWz2N8rcvkS7xhFsqvfp5eqrTe</span>\"\n  \"<span class=sf-dump-key>mia_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LqdP3Q0O7a3cwoJmYgCvHDyb4m34SlxmUHxhBxPb</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-253668224\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2141349925 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 10:17:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2141349925\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-747228763 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">AgJTb7TMVHyj2iNWz2N8rcvkS7xhFsqvfp5eqrTe</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">https://mia.test/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$wMJBDtnQUSeJQj5gCLNgV.9Yd/u2xb7xicPuuj0GtQ51C1KTyTSXC</span>\"\n  \"<span class=sf-dump-key>48040ef7f2542b39b9ba9a72983b0d88_filters</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>localidad</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>zona</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>categoria</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>suscripcion</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-747228763\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://mia.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}