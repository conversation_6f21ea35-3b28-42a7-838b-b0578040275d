{"__meta": {"id": "01JYVE8QFRSQ3ETKHQMTSVNFA7", "datetime": "2025-06-28 14:15:09", "utime": **********.048598, "method": "GET", "uri": "/admin/pago-suscripcions/create", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751120107.322261, "end": **********.04861, "duration": 1.726348876953125, "duration_str": "1.73s", "measures": [{"label": "Booting", "start": 1751120107.322261, "relative_start": 0, "end": **********.061015, "relative_end": **********.061015, "duration": 0.****************, "duration_str": "739ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.061026, "relative_start": 0.****************, "end": **********.048612, "relative_end": 2.1457672119140625e-06, "duration": 0.****************, "duration_str": "988ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.711019, "relative_start": 1.****************, "end": **********.714126, "relative_end": **********.714126, "duration": 0.0031070709228515625, "duration_str": "3.11ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.883709, "relative_start": 1.****************, "end": **********.883709, "relative_end": **********.883709, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.88847, "relative_start": 1.***************, "end": **********.88847, "relative_end": **********.88847, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.892066, "relative_start": 1.5698049068450928, "end": **********.892066, "relative_end": **********.892066, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.895862, "relative_start": 1.5736010074615479, "end": **********.895862, "relative_end": **********.895862, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.899937, "relative_start": 1.5776758193969727, "end": **********.899937, "relative_end": **********.899937, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.903767, "relative_start": 1.5815060138702393, "end": **********.903767, "relative_end": **********.903767, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.909257, "relative_start": 1.5869958400726318, "end": **********.909257, "relative_end": **********.909257, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.9127, "relative_start": 1.5904388427734375, "end": **********.9127, "relative_end": **********.9127, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.91573, "relative_start": 1.5934689044952393, "end": **********.91573, "relative_end": **********.91573, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": **********.932458, "relative_start": 1.610196828842163, "end": **********.932458, "relative_end": **********.932458, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-maps::styles", "start": **********.035862, "relative_start": 1.7136008739471436, "end": **********.035862, "relative_end": **********.035862, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-maps::scripts", "start": **********.039516, "relative_start": 1.717254877090454, "end": **********.039516, "relative_end": **********.039516, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.041082, "relative_start": 1.7188208103179932, "end": **********.041189, "relative_end": **********.041189, "duration": 0.00010704994201660156, "duration_str": "107μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.046901, "relative_start": 1.724639892578125, "end": **********.046979, "relative_end": **********.046979, "duration": 7.796287536621094e-05, "duration_str": "78μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 49767128, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.45.0", "PHP Version": "8.3.8", "Environment": "local", "Debug Mode": "Enabled", "URL": "mia.test", "Timezone": "UTC", "Locale": "es"}}, "views": {"count": 12, "nb_templates": 12, "templates": [{"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.883677, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.888435, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}}, {"name": "__components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.892037, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.895833, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.899905, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}}, {"name": "__components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.903736, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.909227, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.912671, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.915698, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "__components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": **********.932427, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}}, {"name": "filament-maps::styles", "param_count": null, "params": [], "start": **********.035833, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\vendor\\webbingbrasil\\filament-maps\\src\\/../resources/views/styles.blade.phpfilament-maps::styles", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Fwebbingbrasil%2Ffilament-maps%2Fresources%2Fviews%2Fstyles.blade.php&line=1", "ajax": false, "filename": "styles.blade.php", "line": "?"}}, {"name": "filament-maps::scripts", "param_count": null, "params": [], "start": **********.039489, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\vendor\\webbingbrasil\\filament-maps\\src\\/../resources/views/scripts.blade.phpfilament-maps::scripts", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Fwebbingbrasil%2Ffilament-maps%2Fresources%2Fviews%2Fscripts.blade.php&line=1", "ajax": false, "filename": "scripts.blade.php", "line": "?"}}]}, "queries": {"count": 52, "nb_statements": 52, "nb_visible_statements": 52, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.04423999999999998, "accumulated_duration_str": "44.24ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'OGF0xmQNkiM3P99CWPuIofWU8bS7yWbRQ0GqToUS' limit 1", "type": "query", "params": [], "bindings": ["OGF0xmQNkiM3P99CWPuIofWU8bS7yWbRQ0GqToUS"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 105}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 89}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.71909, "duration": 0.02262, "duration_str": "22.62ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "proyecto-mba", "explain": null, "start_percent": 0, "width_percent": 51.13}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 180}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.749204, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "proyecto-mba", "explain": null, "start_percent": 51.13, "width_percent": 1.921}, {"sql": "select * from `cache` where `key` in ('spatie.permission.cache')", "type": "query", "params": [], "bindings": ["spatie.permission.cache"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 418}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.7527661, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "proyecto-mba", "explain": null, "start_percent": 53.052, "width_percent": 1.062}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (1) and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 571}], "start": **********.759281, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:328", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=328", "ajax": false, "filename": "HasPermissions.php", "line": "328"}, "connection": "proyecto-mba", "explain": null, "start_percent": 54.114, "width_percent": 1.537}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 314}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}], "start": **********.761721, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "proyecto-mba", "explain": null, "start_percent": 55.651, "width_percent": 2.17}, {"sql": "select * from `suscripciones`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 785}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 77}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.812742, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "Select.php:785", "source": {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 785}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FSelect.php&line=785", "ajax": false, "filename": "Select.php", "line": "785"}, "connection": "proyecto-mba", "explain": null, "start_percent": 57.821, "width_percent": 1.56}, {"sql": "select * from `negocios` where `negocios`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.8155718, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 59.381, "width_percent": 0.972}, {"sql": "select * from `negocios` where `negocios`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.8175051, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 60.353, "width_percent": 0.769}, {"sql": "select * from `negocios` where `negocios`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.819289, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 61.121, "width_percent": 1.243}, {"sql": "select * from `negocios` where `negocios`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.8211799, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 62.364, "width_percent": 1.243}, {"sql": "select * from `negocios` where `negocios`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.822789, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 63.608, "width_percent": 0.565}, {"sql": "select * from `negocios` where `negocios`.`id` = 6 limit 1", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.8240788, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 64.173, "width_percent": 0.565}, {"sql": "select * from `negocios` where `negocios`.`id` = 7 limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.82533, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 64.738, "width_percent": 0.497}, {"sql": "select * from `negocios` where `negocios`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.826563, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 65.235, "width_percent": 0.497}, {"sql": "select * from `negocios` where `negocios`.`id` = 9 limit 1", "type": "query", "params": [], "bindings": [9], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.8278, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 65.732, "width_percent": 0.814}, {"sql": "select * from `negocios` where `negocios`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.8291628, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 66.546, "width_percent": 0.565}, {"sql": "select * from `negocios` where `negocios`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.830432, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 67.111, "width_percent": 0.475}, {"sql": "select * from `negocios` where `negocios`.`id` = 12 limit 1", "type": "query", "params": [], "bindings": [12], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.8316128, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 67.586, "width_percent": 0.542}, {"sql": "select * from `negocios` where `negocios`.`id` = 13 limit 1", "type": "query", "params": [], "bindings": [13], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.8328118, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 68.128, "width_percent": 0.497}, {"sql": "select * from `negocios` where `negocios`.`id` = 14 limit 1", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.834161, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 68.626, "width_percent": 0.475}, {"sql": "select * from `negocios` where `negocios`.`id` = 15 limit 1", "type": "query", "params": [], "bindings": [15], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.83564, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 69.1, "width_percent": 1.221}, {"sql": "select * from `negocios` where `negocios`.`id` = 16 limit 1", "type": "query", "params": [], "bindings": [16], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.837492, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 70.321, "width_percent": 0.927}, {"sql": "select * from `negocios` where `negocios`.`id` = 17 limit 1", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.839069, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 71.248, "width_percent": 0.927}, {"sql": "select * from `negocios` where `negocios`.`id` = 18 limit 1", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.840495, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 72.175, "width_percent": 0.61}, {"sql": "select * from `negocios` where `negocios`.`id` = 19 limit 1", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.8422282, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 72.785, "width_percent": 1.017}, {"sql": "select * from `negocios` where `negocios`.`id` = 20 limit 1", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.843833, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 73.802, "width_percent": 1.221}, {"sql": "select * from `negocios` where `negocios`.`id` = 21 limit 1", "type": "query", "params": [], "bindings": [21], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.845482, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 75.023, "width_percent": 0.791}, {"sql": "select * from `negocios` where `negocios`.`id` = 22 limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.846863, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 75.814, "width_percent": 0.429}, {"sql": "select * from `negocios` where `negocios`.`id` = 23 limit 1", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.848057, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 76.243, "width_percent": 0.497}, {"sql": "select * from `negocios` where `negocios`.`id` = 24 limit 1", "type": "query", "params": [], "bindings": [24], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.8492708, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 76.741, "width_percent": 0.497}, {"sql": "select * from `negocios` where `negocios`.`id` = 25 limit 1", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.85064, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 77.238, "width_percent": 1.062}, {"sql": "select * from `negocios` where `negocios`.`id` = 26 limit 1", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.852202, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 78.3, "width_percent": 0.972}, {"sql": "select * from `negocios` where `negocios`.`id` = 27 limit 1", "type": "query", "params": [], "bindings": [27], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.853687, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 79.272, "width_percent": 0.475}, {"sql": "select * from `negocios` where `negocios`.`id` = 28 limit 1", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.854909, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 79.747, "width_percent": 0.452}, {"sql": "select * from `negocios` where `negocios`.`id` = 29 limit 1", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.856094, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 80.199, "width_percent": 0.475}, {"sql": "select * from `negocios` where `negocios`.`id` = 30 limit 1", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.857269, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 80.674, "width_percent": 0.61}, {"sql": "select * from `negocios` where `negocios`.`id` = 31 limit 1", "type": "query", "params": [], "bindings": [31], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.858639, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 81.284, "width_percent": 1.062}, {"sql": "select * from `negocios` where `negocios`.`id` = 32 limit 1", "type": "query", "params": [], "bindings": [32], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.860229, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 82.346, "width_percent": 0.904}, {"sql": "select * from `negocios` where `negocios`.`id` = 33 limit 1", "type": "query", "params": [], "bindings": [33], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.86167, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 83.25, "width_percent": 0.678}, {"sql": "select * from `negocios` where `negocios`.`id` = 34 limit 1", "type": "query", "params": [], "bindings": [34], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.863009, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 83.929, "width_percent": 0.52}, {"sql": "select * from `negocios` where `negocios`.`id` = 35 limit 1", "type": "query", "params": [], "bindings": [35], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.864232, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 84.448, "width_percent": 0.61}, {"sql": "select * from `negocios` where `negocios`.`id` = 36 limit 1", "type": "query", "params": [], "bindings": [36], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.8655958, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 85.059, "width_percent": 0.949}, {"sql": "select * from `negocios` where `negocios`.`id` = 37 limit 1", "type": "query", "params": [], "bindings": [37], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.867632, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 86.008, "width_percent": 1.175}, {"sql": "select * from `negocios` where `negocios`.`id` = 38 limit 1", "type": "query", "params": [], "bindings": [38], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.869272, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 87.184, "width_percent": 0.859}, {"sql": "select * from `negocios` where `negocios`.`id` = 39 limit 1", "type": "query", "params": [], "bindings": [39], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.87099, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 88.042, "width_percent": 0.769}, {"sql": "select * from `negocios` where `negocios`.`id` = 40 limit 1", "type": "query", "params": [], "bindings": [40], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.8727431, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 88.811, "width_percent": 1.153}, {"sql": "select * from `negocios` where `negocios`.`id` = 41 limit 1", "type": "query", "params": [], "bindings": [41], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.8745642, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 89.964, "width_percent": 0.678}, {"sql": "select * from `negocios` where `negocios`.`id` = 42 limit 1", "type": "query", "params": [], "bindings": [42], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.875972, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 90.642, "width_percent": 0.656}, {"sql": "select * from `negocios` where `negocios`.`id` = 43 limit 1", "type": "query", "params": [], "bindings": [43], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.877264, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 91.297, "width_percent": 0.542}, {"sql": "select * from `negocios` where `negocios`.`id` = 44 limit 1", "type": "query", "params": [], "bindings": [44], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.878498, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 91.84, "width_percent": 0.656}, {"sql": "select * from `negocios` where `negocios`.`id` = 45 limit 1", "type": "query", "params": [], "bindings": [45], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.879831, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 92.495, "width_percent": 0.746}, {"sql": "update `sessions` set `payload` = 'YTo3OntzOjY6Il90b2tlbiI7czo0MDoibEZ5bk0xbldDM3dpUGR0cmhtODI0TnRBWUhpWU1FYTI3anZDb3FKQiI7czozOiJ1cmwiO2E6MDp7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjQ3OiJodHRwczovL21pYS50ZXN0L2FkbWluL3BhZ28tc3VzY3JpcGNpb25zL2NyZWF0ZSI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fXM6NTA6ImxvZ2luX3dlYl81OWJhMzZhZGRjMmIyZjk0MDE1ODBmMDE0YzdmNThlYTRlMzA5ODlkIjtpOjE7czoxNzoicGFzc3dvcmRfaGFzaF93ZWIiO3M6NjA6IiQyeSQxMiRXRWxoUlF1YjhqSjU0Rkswa3ZQbXRlbzdvT1BrZzhXSkp5ZzBIUEkxMVhJR0xscUxKa25VMiI7czo4OiJmaWxhbWVudCI7YTowOnt9fQ==', `last_activity` = **********, `user_id` = 1, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'OGF0xmQNkiM3P99CWPuIofWU8bS7yWbRQ0GqToUS'", "type": "query", "params": [], "bindings": ["YTo3OntzOjY6Il90b2tlbiI7czo0MDoibEZ5bk0xbldDM3dpUGR0cmhtODI0TnRBWUhpWU1FYTI3anZDb3FKQiI7czozOiJ1cmwiO2E6MDp7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjQ3OiJodHRwczovL21pYS50ZXN0L2FkbWluL3BhZ28tc3VzY3JpcGNpb25zL2NyZWF0ZSI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fXM6NTA6ImxvZ2luX3dlYl81OWJhMzZhZGRjMmIyZjk0MDE1ODBmMDE0YzdmNThlYTRlMzA5ODlkIjtpOjE7czoxNzoicGFzc3dvcmRfaGFzaF93ZWIiO3M6NjA6IiQyeSQxMiRXRWxoUlF1YjhqSjU0Rkswa3ZQbXRlbzdvT1BrZzhXSkp5ZzBIUEkxMVhJR0xscUxKa25VMiI7czo4OiJmaWxhbWVudCI7YTowOnt9fQ==", **********, 1, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "OGF0xmQNkiM3P99CWPuIofWU8bS7yWbRQ0GqToUS"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 140}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 176}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 245}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 130}], "start": **********.04254, "duration": 0.00299, "duration_str": "2.99ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:173", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=173", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "173"}, "connection": "proyecto-mba", "explain": null, "start_percent": 93.241, "width_percent": 6.759}]}, "models": {"data": {"App\\Models\\Suscripcion": {"value": 45, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FModels%2FSuscripcion.php&line=1", "ajax": false, "filename": "Suscripcion.php", "line": "?"}}, "App\\Models\\Negocio": {"value": 45, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FModels%2FNegocio.php&line=1", "ajax": false, "filename": "Negocio.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 93, "is_counter": true}, "livewire": {"data": {"app.filament.resources.pago-suscripcion-resource.pages.create-pago-suscripcion #R2oVxPJATT73etOOrqsU": "array:4 [\n  \"data\" => array:18 [\n    \"record\" => null\n    \"data\" => array:6 [\n      \"suscripcion_id\" => null\n      \"metodo_pago\" => null\n      \"importe\" => null\n      \"fecha_pago\" => null\n      \"transaccion_id\" => null\n      \"estado\" => null\n    ]\n    \"previousUrl\" => \"https://mia.test/admin/login\"\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"savedDataHash\" => null\n  ]\n  \"name\" => \"app.filament.resources.pago-suscripcion-resource.pages.create-pago-suscripcion\"\n  \"component\" => \"App\\Filament\\Resources\\PagoSuscripcionResource\\Pages\\CreatePagoSuscripcion\"\n  \"id\" => \"R2oVxPJATT73etOOrqsU\"\n]", "filament.livewire.global-search #uwyUZdOCB3B3Ep8C1HYF": "array:4 [\n  \"data\" => array:1 [\n    \"search\" => \"\"\n  ]\n  \"name\" => \"filament.livewire.global-search\"\n  \"component\" => \"Filament\\Livewire\\GlobalSearch\"\n  \"id\" => \"uwyUZdOCB3B3Ep8C1HYF\"\n]", "filament.livewire.notifications #WdqijFtMIV83LngEiHZa": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#2665\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"WdqijFtMIV83LngEiHZa\"\n]"}, "count": 3}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 69, "messages": [{"message": "[\n  ability => system.access-panel,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1114100785 data-indent-pad=\"  \"><span class=sf-dump-note>system.access-panel </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">system.access-panel</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1114100785\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.766785, "xdebug_link": null}, {"message": "[\n  ability => system.admin-dashboard,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-183007403 data-indent-pad=\"  \"><span class=sf-dump-note>system.admin-dashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">system.admin-dashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-183007403\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.773939, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\PagoSuscripcion,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\PagoSuscripcion]\n]", "message_html": "<pre class=sf-dump id=sf-dump-906333056 data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\PagoSuscripcion</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\PagoSuscripcion</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; App\\Models\\PagoSuscripcion]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-906333056\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.774095, "xdebug_link": null}, {"message": "[\n  ability => system.admin-dashboard,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1063682521 data-indent-pad=\"  \"><span class=sf-dump-note>system.admin-dashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">system.admin-dashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1063682521\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.790854, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\PagoSuscripcion,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\PagoSuscripcion]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1875406184 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\PagoSuscripcion</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\PagoSuscripcion</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; App\\Models\\PagoSuscripcion]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1875406184\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.791128, "xdebug_link": null}, {"message": "[\n  ability => system.admin,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1015945483 data-indent-pad=\"  \"><span class=sf-dump-note>system.admin </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">system.admin</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1015945483\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.934909, "xdebug_link": null}, {"message": "[\n  ability => user.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-289017604 data-indent-pad=\"  \"><span class=sf-dump-note>user.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">user.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-289017604\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.936369, "xdebug_link": null}, {"message": "[\n  ability => user.admin-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-469905570 data-indent-pad=\"  \"><span class=sf-dump-note>user.admin-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">user.admin-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-469905570\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.937167, "xdebug_link": null}, {"message": "[\n  ability => system.admin,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1047927928 data-indent-pad=\"  \"><span class=sf-dump-note>system.admin </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">system.admin</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1047927928\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.938012, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\AppVersion,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\AppVersion]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1984994865 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\AppVersion</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\AppVersion</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"28 characters\">[0 =&gt; App\\Models\\AppVersion]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1984994865\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.939079, "xdebug_link": null}, {"message": "[\n  ability => categoria.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-636190175 data-indent-pad=\"  \"><span class=sf-dump-note>categoria.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">categoria.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-636190175\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.940655, "xdebug_link": null}, {"message": "[\n  ability => categoria.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-336126645 data-indent-pad=\"  \"><span class=sf-dump-note>categoria.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">categoria.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-336126645\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.94227, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Categoria,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Categoria]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1054712344 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Categoria</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Categoria</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Categoria]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1054712344\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.942436, "xdebug_link": null}, {"message": "[\n  ability => user.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1863586118 data-indent-pad=\"  \"><span class=sf-dump-note>user.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">user.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1863586118\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.943308, "xdebug_link": null}, {"message": "[\n  ability => user.admin-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1452695771 data-indent-pad=\"  \"><span class=sf-dump-note>user.admin-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">user.admin-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1452695771\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.943889, "xdebug_link": null}, {"message": "[\n  ability => system.admin-dashboard,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1569538567 data-indent-pad=\"  \"><span class=sf-dump-note>system.admin-dashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">system.admin-dashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1569538567\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.944558, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Evento,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Evento]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2059362929 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Evento</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Evento</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Evento]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2059362929\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.94551, "xdebug_link": null}, {"message": "[\n  ability => localidad.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-787623536 data-indent-pad=\"  \"><span class=sf-dump-note>localidad.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localidad.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-787623536\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.946348, "xdebug_link": null}, {"message": "[\n  ability => localidad.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1945190711 data-indent-pad=\"  \"><span class=sf-dump-note>localidad.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localidad.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1945190711\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.947951, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Localidad,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Localidad]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2077442098 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Localidad</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Localidad</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Localidad]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2077442098\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.948354, "xdebug_link": null}, {"message": "[\n  ability => negocio.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-750591785 data-indent-pad=\"  \"><span class=sf-dump-note>negocio.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">negocio.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-750591785\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.94943, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Negocio,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Negocio]\n]", "message_html": "<pre class=sf-dump id=sf-dump-656237604 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Negocio</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Negocio</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Negocio]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-656237604\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.950828, "xdebug_link": null}, {"message": "[\n  ability => system.admin-dashboard,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-532241704 data-indent-pad=\"  \"><span class=sf-dump-note>system.admin-dashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">system.admin-dashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-532241704\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.953483, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\PagoSuscripcion,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\PagoSuscripcion]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1225471466 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\PagoSuscripcion</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\PagoSuscripcion</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; App\\Models\\PagoSuscripcion]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1225471466\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.953868, "xdebug_link": null}, {"message": "[\n  ability => system.permissions,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-230667142 data-indent-pad=\"  \"><span class=sf-dump-note>system.permissions </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">system.permissions</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-230667142\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.955772, "xdebug_link": null}, {"message": "[\n  ability => system.permissions,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-859829342 data-indent-pad=\"  \"><span class=sf-dump-note>system.permissions </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">system.permissions</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-859829342\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.957674, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\Permission\\Models\\Permission,\n  result => true,\n  user => 1,\n  arguments => [0 => <PERSON><PERSON>\\Permission\\Models\\Permission]\n]", "message_html": "<pre class=sf-dump id=sf-dump-739217801 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Permission\\Models\\Permission</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Spatie\\Permission\\Models\\Permission</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"42 characters\">[0 =&gt; Spatie\\Permission\\Models\\Permission]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-739217801\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.957886, "xdebug_link": null}, {"message": "[\n  ability => system.roles,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1402566419 data-indent-pad=\"  \"><span class=sf-dump-note>system.roles </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">system.roles</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1402566419\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.959068, "xdebug_link": null}, {"message": "[\n  ability => system.roles,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2028049223 data-indent-pad=\"  \"><span class=sf-dump-note>system.roles </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">system.roles</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2028049223\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.961099, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\Permission\\Models\\Role,\n  result => true,\n  user => 1,\n  arguments => [0 => <PERSON><PERSON>\\Permission\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1328482561 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Permission\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Spatie\\Permission\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"36 characters\">[0 =&gt; Spatie\\Permission\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1328482561\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.961329, "xdebug_link": null}, {"message": "[\n  ability => user.admin-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1442983135 data-indent-pad=\"  \"><span class=sf-dump-note>user.admin-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">user.admin-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1442983135\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.963066, "xdebug_link": null}, {"message": "[\n  ability => user.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-193845752 data-indent-pad=\"  \"><span class=sf-dump-note>user.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">user.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-193845752\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.965801, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1366653848 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1366653848\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.966003, "xdebug_link": null}, {"message": "[\n  ability => zona.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1552037844 data-indent-pad=\"  \"><span class=sf-dump-note>zona.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">zona.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1552037844\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.966753, "xdebug_link": null}, {"message": "[\n  ability => zona.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1820928238 data-indent-pad=\"  \"><span class=sf-dump-note>zona.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">zona.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1820928238\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.968933, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Zona,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Zona]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1648787721 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Zona</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Zona</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Zona]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1648787721\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.969293, "xdebug_link": null}, {"message": "[\n  ability => system.admin-dashboard,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-360838750 data-indent-pad=\"  \"><span class=sf-dump-note>system.admin-dashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">system.admin-dashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-360838750\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.97622, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\PagoSuscripcion,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\PagoSuscripcion]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1426191555 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\PagoSuscripcion</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\PagoSuscripcion</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; App\\Models\\PagoSuscripcion]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1426191555\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.976409, "xdebug_link": null}, {"message": "[\n  ability => system.admin,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1436427974 data-indent-pad=\"  \"><span class=sf-dump-note>system.admin </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">system.admin</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1436427974\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.993384, "xdebug_link": null}, {"message": "[\n  ability => user.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-869708544 data-indent-pad=\"  \"><span class=sf-dump-note>user.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">user.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-869708544\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.994337, "xdebug_link": null}, {"message": "[\n  ability => user.admin-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-996345214 data-indent-pad=\"  \"><span class=sf-dump-note>user.admin-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">user.admin-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-996345214\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.994943, "xdebug_link": null}, {"message": "[\n  ability => system.admin,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-365615182 data-indent-pad=\"  \"><span class=sf-dump-note>system.admin </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">system.admin</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-365615182\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.99571, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\AppVersion,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\AppVersion]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1271891469 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\AppVersion</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\AppVersion</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"28 characters\">[0 =&gt; App\\Models\\AppVersion]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1271891469\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.996414, "xdebug_link": null}, {"message": "[\n  ability => categoria.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1451824070 data-indent-pad=\"  \"><span class=sf-dump-note>categoria.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">categoria.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1451824070\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.997137, "xdebug_link": null}, {"message": "[\n  ability => categoria.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-631750631 data-indent-pad=\"  \"><span class=sf-dump-note>categoria.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">categoria.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-631750631\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.998198, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Categoria,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Categoria]\n]", "message_html": "<pre class=sf-dump id=sf-dump-769526615 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Categoria</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Categoria</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Categoria]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-769526615\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.998392, "xdebug_link": null}, {"message": "[\n  ability => user.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1908051364 data-indent-pad=\"  \"><span class=sf-dump-note>user.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">user.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1908051364\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.999218, "xdebug_link": null}, {"message": "[\n  ability => user.admin-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1423195147 data-indent-pad=\"  \"><span class=sf-dump-note>user.admin-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">user.admin-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1423195147\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.99978, "xdebug_link": null}, {"message": "[\n  ability => system.admin-dashboard,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1064229428 data-indent-pad=\"  \"><span class=sf-dump-note>system.admin-dashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">system.admin-dashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1064229428\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.000419, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Evento,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Evento]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2031877107 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Evento</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Evento</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Evento]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2031877107\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.001054, "xdebug_link": null}, {"message": "[\n  ability => localidad.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1521438596 data-indent-pad=\"  \"><span class=sf-dump-note>localidad.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localidad.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1521438596\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.001598, "xdebug_link": null}, {"message": "[\n  ability => localidad.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1367166263 data-indent-pad=\"  \"><span class=sf-dump-note>localidad.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localidad.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1367166263\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.002395, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Localidad,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Localidad]\n]", "message_html": "<pre class=sf-dump id=sf-dump-532036566 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Localidad</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Localidad</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Localidad]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-532036566\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.002537, "xdebug_link": null}, {"message": "[\n  ability => negocio.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1382126374 data-indent-pad=\"  \"><span class=sf-dump-note>negocio.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">negocio.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1382126374\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.003109, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Negocio,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Negocio]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1337604469 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Negocio</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Negocio</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Negocio]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1337604469\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.003834, "xdebug_link": null}, {"message": "[\n  ability => system.admin-dashboard,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1045187744 data-indent-pad=\"  \"><span class=sf-dump-note>system.admin-dashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">system.admin-dashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1045187744\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.005127, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\PagoSuscripcion,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\PagoSuscripcion]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1310297796 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\PagoSuscripcion</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\PagoSuscripcion</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; App\\Models\\PagoSuscripcion]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1310297796\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.005272, "xdebug_link": null}, {"message": "[\n  ability => system.permissions,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1217746059 data-indent-pad=\"  \"><span class=sf-dump-note>system.permissions </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">system.permissions</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1217746059\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.006015, "xdebug_link": null}, {"message": "[\n  ability => system.permissions,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1891930809 data-indent-pad=\"  \"><span class=sf-dump-note>system.permissions </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">system.permissions</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1891930809\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.007024, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\Permission\\Models\\Permission,\n  result => true,\n  user => 1,\n  arguments => [0 => <PERSON><PERSON>\\Permission\\Models\\Permission]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1743965204 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Permission\\Models\\Permission</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Spatie\\Permission\\Models\\Permission</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"42 characters\">[0 =&gt; Spatie\\Permission\\Models\\Permission]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1743965204\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.007169, "xdebug_link": null}, {"message": "[\n  ability => system.roles,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1658508346 data-indent-pad=\"  \"><span class=sf-dump-note>system.roles </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">system.roles</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1658508346\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.008134, "xdebug_link": null}, {"message": "[\n  ability => system.roles,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1630953940 data-indent-pad=\"  \"><span class=sf-dump-note>system.roles </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">system.roles</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1630953940\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.009159, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\Permission\\Models\\Role,\n  result => true,\n  user => 1,\n  arguments => [0 => <PERSON><PERSON>\\Permission\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-908216712 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Permission\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Spatie\\Permission\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"36 characters\">[0 =&gt; Spatie\\Permission\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-908216712\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.009303, "xdebug_link": null}, {"message": "[\n  ability => user.admin-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1568814852 data-indent-pad=\"  \"><span class=sf-dump-note>user.admin-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">user.admin-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1568814852\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.010018, "xdebug_link": null}, {"message": "[\n  ability => user.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1818503172 data-indent-pad=\"  \"><span class=sf-dump-note>user.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">user.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1818503172\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.011144, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-199988895 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-199988895\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.011292, "xdebug_link": null}, {"message": "[\n  ability => zona.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-364097263 data-indent-pad=\"  \"><span class=sf-dump-note>zona.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">zona.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-364097263\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.011862, "xdebug_link": null}, {"message": "[\n  ability => zona.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2038177162 data-indent-pad=\"  \"><span class=sf-dump-note>zona.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">zona.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2038177162\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.012724, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Zona,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Zona]\n]", "message_html": "<pre class=sf-dump id=sf-dump-78891874 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Zona</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Zona</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Zona]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-78891874\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.012866, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "https://mia.test/admin/pago-suscripcions/create", "action_name": "filament.admin.resources.pago-suscripcions.create", "controller_action": "App\\Filament\\Resources\\PagoSuscripcionResource\\Pages\\CreatePagoSuscripcion", "uri": "GET admin/pago-suscripcions/create", "controller": "App\\Filament\\Resources\\PagoSuscripcionResource\\Pages\\CreatePagoSuscripcion@render<a href=\"phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/pago-suscripcions", "file": "<a href=\"phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Pages/BasePage.php:51-59</a>", "middleware": "panel:admin, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Filament\\Http\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Filament\\Http\\Middleware\\Authenticate", "duration": "1.31s", "peak_memory": "52MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mia.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"57 characters\">&quot;Brave&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"96 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">es-ES,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"28 characters\">https://mia.test/admin/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"709 characters\">XSRF-TOKEN=eyJpdiI6IkJyK2dDMlA1MXJOTmdHcHFVTGI2N3c9PSIsInZhbHVlIjoibGZNQjJlOUhLT3JCZGNiZlVLUVFhZjZRQVl5TkhwOGsrMlZ5YlBMSk5iTFRXbW5QMWxDelQzdHFtOTl5LzFPQWZaN2p0aUI2eEUrRUc2OUlvUFRacWlPYVc0dlF3MU9VaUJsdWxpOHBSL3BvYVRxcUJ4YUExMjhreUNrc3dJbmkiLCJtYWMiOiI1NzZkMmY0M2Q2ODNjNjA1NzQ0ZmFhOTM0Yjg1ZGZlZjhhNjczMzJhZjVhNDEzOGU4OTEzMjRhZjJjOTU4NWY3IiwidGFnIjoiIn0%3D; mia_session=eyJpdiI6ImtITU5jVVdnbEFqdG1DZlJKVkp6TVE9PSIsInZhbHVlIjoiTUNDbDdvL0F4eVhvcmJBRVZIRGY3bGFvYmQ0SHduc08rZzR4cGtXanVjUFpKcUZkS0x5alNoUXdUakZIZlhTYUVsS1JyZzhKMWhJdHZrbVFBVC9tZVZMdEtpYWpDck12cXNyTUpuaG44THZmNUphRzRoUWJwVXpNQnhNVFl5UEkiLCJtYWMiOiI1OWVkOTNkMGQ2OGRmZGY1ZmMyMGEwN2RhYWUxZjJlM2IwNjYxN2IwOWQzNjVkNDIxOWNmYzRjYTNiNWQ5MGQyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-803765662 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lFynM1nWC3wiPdtrhm824NtAYHiYMEa27jvCoqJB</span>\"\n  \"<span class=sf-dump-key>mia_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OGF0xmQNkiM3P99CWPuIofWU8bS7yWbRQ0GqToUS</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-803765662\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1562392038 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 14:15:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"436 characters\">XSRF-TOKEN=eyJpdiI6IndHbkRIMFRLOVpBVmtIWWdvRk1kbmc9PSIsInZhbHVlIjoic3pQK3BnZUdqSUZlL2lMSVhzakdWVmFGUEZHek5nS3NCeDJzbnZmU0VKT0lrbmdzKzRTS3ZEeExPQXJZK28yeFdmWUhGK3NxVkVDUlE4ai9ZMTJadEpUTnNpdFVkaXhvK25yRTQ4bUhXZ3NqNE5EMlBJdVhlM1Nac1M3M0ZHSG4iLCJtYWMiOiI5YWJhMDA3Yzc1MzUzNmQ0Njc3OTYzMWExOWMxNWM0YTU1N2EzZDljOGJmMjhiNTRlYzE2MDJhNzc2MTcyMzkwIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 16:15:09 GMT; Max-Age=7200; path=/; secure; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"447 characters\">mia_session=eyJpdiI6InNZdXRvUUZMVGcvTXc4bGZQb0FBSmc9PSIsInZhbHVlIjoiNzR5SU4xcHpBUVVDRlFKdEFJZ00yU0NOM0NWbmJBcVJkUC9TT1dBSkt2akNwb3RvQ3JEU2dWaDhXTGJ6aCtLNnE5ZDl3NGZQMElVdXp2YkNiNWU1OWg3RjhTT3JIOEF1WnI1UnNzN1FUTXNwYkNuRTMrbXI3QkRHc29MN0FFNEoiLCJtYWMiOiI5ODliNjM3MzliNzE1NzZhMGNjNmQ4YWMzZjE3YjUxMzRiZmI2OTA0ZDUxZTliOWViY2E1MGYwZmNiYzQ5YWYyIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 16:15:09 GMT; Max-Age=7200; path=/; secure; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"408 characters\">XSRF-TOKEN=eyJpdiI6IndHbkRIMFRLOVpBVmtIWWdvRk1kbmc9PSIsInZhbHVlIjoic3pQK3BnZUdqSUZlL2lMSVhzakdWVmFGUEZHek5nS3NCeDJzbnZmU0VKT0lrbmdzKzRTS3ZEeExPQXJZK28yeFdmWUhGK3NxVkVDUlE4ai9ZMTJadEpUTnNpdFVkaXhvK25yRTQ4bUhXZ3NqNE5EMlBJdVhlM1Nac1M3M0ZHSG4iLCJtYWMiOiI5YWJhMDA3Yzc1MzUzNmQ0Njc3OTYzMWExOWMxNWM0YTU1N2EzZDljOGJmMjhiNTRlYzE2MDJhNzc2MTcyMzkwIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 16:15:09 GMT; path=/; secure</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"419 characters\">mia_session=eyJpdiI6InNZdXRvUUZMVGcvTXc4bGZQb0FBSmc9PSIsInZhbHVlIjoiNzR5SU4xcHpBUVVDRlFKdEFJZ00yU0NOM0NWbmJBcVJkUC9TT1dBSkt2akNwb3RvQ3JEU2dWaDhXTGJ6aCtLNnE5ZDl3NGZQMElVdXp2YkNiNWU1OWg3RjhTT3JIOEF1WnI1UnNzN1FUTXNwYkNuRTMrbXI3QkRHc29MN0FFNEoiLCJtYWMiOiI5ODliNjM3MzliNzE1NzZhMGNjNmQ4YWMzZjE3YjUxMzRiZmI2OTA0ZDUxZTliOWViY2E1MGYwZmNiYzQ5YWYyIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 16:15:09 GMT; path=/; secure; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1562392038\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1590115921 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lFynM1nWC3wiPdtrhm824NtAYHiYMEa27jvCoqJB</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"47 characters\">https://mia.test/admin/pago-suscripcions/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$WElhRQub8jJ54FK0kvPmteo7oOPkg8WJJyg0HPI11XIGLlqLJknU2</span>\"\n  \"<span class=sf-dump-key>filament</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1590115921\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://mia.test/admin/pago-suscripcions/create", "action_name": "filament.admin.resources.pago-suscripcions.create", "controller_action": "App\\Filament\\Resources\\PagoSuscripcionResource\\Pages\\CreatePagoSuscripcion"}, "badge": null}}