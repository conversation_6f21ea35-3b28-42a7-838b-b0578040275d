{"__meta": {"id": "01JYVFK8JNHWNRFCRXF5XM3110", "datetime": "2025-06-28 14:38:22", "utime": **********.805726, "method": "GET", "uri": "/admin/pago-suscripcions/2/edit", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.258732, "end": **********.80574, "duration": 1.5470080375671387, "duration_str": "1.55s", "measures": [{"label": "Booting", "start": **********.258732, "relative_start": 0, "end": **********.96751, "relative_end": **********.96751, "duration": 0.***************, "duration_str": "709ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.96752, "relative_start": 0.****************, "end": **********.805742, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "838ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.461199, "relative_start": 1.****************, "end": **********.463442, "relative_end": **********.463442, "duration": 0.****************, "duration_str": "2.24ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.627552, "relative_start": 1.****************, "end": **********.627552, "relative_end": **********.627552, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.632163, "relative_start": 1.****************, "end": **********.632163, "relative_end": **********.632163, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.636431, "relative_start": 1.3776988983154297, "end": **********.636431, "relative_end": **********.636431, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.641009, "relative_start": 1.382277011871338, "end": **********.641009, "relative_end": **********.641009, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.644546, "relative_start": 1.3858139514923096, "end": **********.644546, "relative_end": **********.644546, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.648186, "relative_start": 1.3894538879394531, "end": **********.648186, "relative_end": **********.648186, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.65251, "relative_start": 1.393777847290039, "end": **********.65251, "relative_end": **********.65251, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.655912, "relative_start": 1.3971798419952393, "end": **********.655912, "relative_end": **********.655912, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.667809, "relative_start": 1.4090769290924072, "end": **********.667809, "relative_end": **********.667809, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": **********.680361, "relative_start": 1.4216289520263672, "end": **********.680361, "relative_end": **********.680361, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-maps::styles", "start": **********.777497, "relative_start": 1.5187649726867676, "end": **********.777497, "relative_end": **********.777497, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-maps::scripts", "start": **********.797827, "relative_start": 1.5390949249267578, "end": **********.797827, "relative_end": **********.797827, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.799295, "relative_start": 1.5405628681182861, "end": **********.799406, "relative_end": **********.799406, "duration": 0.00011110305786132812, "duration_str": "111μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.80395, "relative_start": 1.5452179908752441, "end": **********.804034, "relative_end": **********.804034, "duration": 8.392333984375e-05, "duration_str": "84μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 53423472, "peak_usage_str": "51MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.45.0", "PHP Version": "8.3.8", "Environment": "local", "Debug Mode": "Enabled", "URL": "mia.test", "Timezone": "UTC", "Locale": "es"}}, "views": {"count": 12, "nb_templates": 12, "templates": [{"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.62752, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.632134, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}}, {"name": "__components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.636393, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.640977, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.644518, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}}, {"name": "__components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.648158, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.652479, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.655884, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.66778, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "__components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": **********.680332, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}}, {"name": "filament-maps::styles", "param_count": null, "params": [], "start": **********.77746, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\vendor\\webbingbrasil\\filament-maps\\src\\/../resources/views/styles.blade.phpfilament-maps::styles", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Fwebbingbrasil%2Ffilament-maps%2Fresources%2Fviews%2Fstyles.blade.php&line=1", "ajax": false, "filename": "styles.blade.php", "line": "?"}}, {"name": "filament-maps::scripts", "param_count": null, "params": [], "start": **********.797797, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\vendor\\webbingbrasil\\filament-maps\\src\\/../resources/views/scripts.blade.phpfilament-maps::scripts", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Fwebbingbrasil%2Ffilament-maps%2Fresources%2Fviews%2Fscripts.blade.php&line=1", "ajax": false, "filename": "scripts.blade.php", "line": "?"}}]}, "queries": {"count": 53, "nb_statements": 53, "nb_visible_statements": 53, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.04099, "accumulated_duration_str": "40.99ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'OGF0xmQNkiM3P99CWPuIofWU8bS7yWbRQ0GqToUS' limit 1", "type": "query", "params": [], "bindings": ["OGF0xmQNkiM3P99CWPuIofWU8bS7yWbRQ0GqToUS"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 105}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 89}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.4676309, "duration": 0.02145, "duration_str": "21.45ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "proyecto-mba", "explain": null, "start_percent": 0, "width_percent": 52.33}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 180}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.495353, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "proyecto-mba", "explain": null, "start_percent": 52.33, "width_percent": 0.781}, {"sql": "select * from `cache` where `key` in ('spatie.permission.cache')", "type": "query", "params": [], "bindings": ["spatie.permission.cache"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 418}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.497916, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "proyecto-mba", "explain": null, "start_percent": 53.111, "width_percent": 0.537}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (1) and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 571}], "start": **********.503388, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:328", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=328", "ajax": false, "filename": "HasPermissions.php", "line": "328"}, "connection": "proyecto-mba", "explain": null, "start_percent": 53.647, "width_percent": 0.781}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 314}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}], "start": **********.5057209, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "proyecto-mba", "explain": null, "start_percent": 54.428, "width_percent": 1.366}, {"sql": "select * from `pago_suscripcions` where `id` = '2' limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 192}, {"index": 17, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/Concerns/InteractsWithRecord.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\filament\\src\\Resources\\Pages\\Concerns\\InteractsWithRecord.php", "line": 23}, {"index": 18, "namespace": null, "name": "vendor/filament/filament/src/Resources/Pages/EditRecord.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\filament\\src\\Resources\\Pages\\EditRecord.php", "line": 73}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.5163002, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Resource.php:192", "source": {"index": 16, "namespace": null, "name": "vendor/filament/filament/src/Resources/Resource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\filament\\src\\Resources\\Resource.php", "line": 192}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FResources%2FResource.php&line=192", "ajax": false, "filename": "Resource.php", "line": "192"}, "connection": "proyecto-mba", "explain": null, "start_percent": 55.794, "width_percent": 1.122}, {"sql": "select * from `suscripciones`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 785}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 77}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.558717, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Select.php:785", "source": {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 785}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FSelect.php&line=785", "ajax": false, "filename": "Select.php", "line": "785"}, "connection": "proyecto-mba", "explain": null, "start_percent": 56.916, "width_percent": 1.513}, {"sql": "select * from `negocios` where `negocios`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.561404, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 58.429, "width_percent": 1.171}, {"sql": "select * from `negocios` where `negocios`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.5629601, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 59.6, "width_percent": 0.659}, {"sql": "select * from `negocios` where `negocios`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.5642579, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 60.259, "width_percent": 0.537}, {"sql": "select * from `negocios` where `negocios`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.5655239, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 60.795, "width_percent": 0.561}, {"sql": "select * from `negocios` where `negocios`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.566748, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 61.356, "width_percent": 0.488}, {"sql": "select * from `negocios` where `negocios`.`id` = 6 limit 1", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.5684638, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 61.844, "width_percent": 1.488}, {"sql": "select * from `negocios` where `negocios`.`id` = 7 limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.5702682, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 63.333, "width_percent": 1.561}, {"sql": "select * from `negocios` where `negocios`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.572073, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 64.894, "width_percent": 1.61}, {"sql": "select * from `negocios` where `negocios`.`id` = 9 limit 1", "type": "query", "params": [], "bindings": [9], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.573843, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 66.504, "width_percent": 1.366}, {"sql": "select * from `negocios` where `negocios`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.5757859, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 67.87, "width_percent": 1.781}, {"sql": "select * from `negocios` where `negocios`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.5777671, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 69.651, "width_percent": 1.147}, {"sql": "select * from `negocios` where `negocios`.`id` = 12 limit 1", "type": "query", "params": [], "bindings": [12], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.5793412, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 70.798, "width_percent": 0.707}, {"sql": "select * from `negocios` where `negocios`.`id` = 13 limit 1", "type": "query", "params": [], "bindings": [13], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.580644, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 71.505, "width_percent": 0.586}, {"sql": "select * from `negocios` where `negocios`.`id` = 14 limit 1", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.581858, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 72.091, "width_percent": 0.586}, {"sql": "select * from `negocios` where `negocios`.`id` = 15 limit 1", "type": "query", "params": [], "bindings": [15], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.583104, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 72.676, "width_percent": 0.634}, {"sql": "select * from `negocios` where `negocios`.`id` = 16 limit 1", "type": "query", "params": [], "bindings": [16], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.5844178, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 73.311, "width_percent": 0.634}, {"sql": "select * from `negocios` where `negocios`.`id` = 17 limit 1", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.585731, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 73.945, "width_percent": 0.683}, {"sql": "select * from `negocios` where `negocios`.`id` = 18 limit 1", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.5870068, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 74.628, "width_percent": 0.561}, {"sql": "select * from `negocios` where `negocios`.`id` = 19 limit 1", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.588201, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 75.189, "width_percent": 0.537}, {"sql": "select * from `negocios` where `negocios`.`id` = 20 limit 1", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.589386, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 75.726, "width_percent": 0.561}, {"sql": "select * from `negocios` where `negocios`.`id` = 21 limit 1", "type": "query", "params": [], "bindings": [21], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.5905619, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 76.287, "width_percent": 0.537}, {"sql": "select * from `negocios` where `negocios`.`id` = 22 limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.591807, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 76.824, "width_percent": 0.61}, {"sql": "select * from `negocios` where `negocios`.`id` = 23 limit 1", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.59303, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 77.434, "width_percent": 0.586}, {"sql": "select * from `negocios` where `negocios`.`id` = 24 limit 1", "type": "query", "params": [], "bindings": [24], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.59426, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 78.019, "width_percent": 0.537}, {"sql": "select * from `negocios` where `negocios`.`id` = 25 limit 1", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.5954618, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 78.556, "width_percent": 0.537}, {"sql": "select * from `negocios` where `negocios`.`id` = 26 limit 1", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.596648, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 79.092, "width_percent": 0.561}, {"sql": "select * from `negocios` where `negocios`.`id` = 27 limit 1", "type": "query", "params": [], "bindings": [27], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.597836, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 79.654, "width_percent": 0.537}, {"sql": "select * from `negocios` where `negocios`.`id` = 28 limit 1", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.599126, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 80.19, "width_percent": 1.317}, {"sql": "select * from `negocios` where `negocios`.`id` = 29 limit 1", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.600958, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 81.508, "width_percent": 1.171}, {"sql": "select * from `negocios` where `negocios`.`id` = 30 limit 1", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.602586, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 82.679, "width_percent": 0.878}, {"sql": "select * from `negocios` where `negocios`.`id` = 31 limit 1", "type": "query", "params": [], "bindings": [31], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.6041868, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 83.557, "width_percent": 1.488}, {"sql": "select * from `negocios` where `negocios`.`id` = 32 limit 1", "type": "query", "params": [], "bindings": [32], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.605964, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 85.045, "width_percent": 2.098}, {"sql": "select * from `negocios` where `negocios`.`id` = 33 limit 1", "type": "query", "params": [], "bindings": [33], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.608022, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 87.143, "width_percent": 0.976}, {"sql": "select * from `negocios` where `negocios`.`id` = 34 limit 1", "type": "query", "params": [], "bindings": [34], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.609488, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 88.119, "width_percent": 0.586}, {"sql": "select * from `negocios` where `negocios`.`id` = 35 limit 1", "type": "query", "params": [], "bindings": [35], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.6107578, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 88.705, "width_percent": 0.634}, {"sql": "select * from `negocios` where `negocios`.`id` = 36 limit 1", "type": "query", "params": [], "bindings": [36], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.612045, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 89.339, "width_percent": 0.659}, {"sql": "select * from `negocios` where `negocios`.`id` = 37 limit 1", "type": "query", "params": [], "bindings": [37], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.61339, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 89.998, "width_percent": 0.976}, {"sql": "select * from `negocios` where `negocios`.`id` = 38 limit 1", "type": "query", "params": [], "bindings": [38], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.61513, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 90.973, "width_percent": 0.805}, {"sql": "select * from `negocios` where `negocios`.`id` = 39 limit 1", "type": "query", "params": [], "bindings": [39], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.6165419, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 91.778, "width_percent": 0.634}, {"sql": "select * from `negocios` where `negocios`.`id` = 40 limit 1", "type": "query", "params": [], "bindings": [40], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.617843, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 92.413, "width_percent": 0.537}, {"sql": "select * from `negocios` where `negocios`.`id` = 41 limit 1", "type": "query", "params": [], "bindings": [41], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.619047, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 92.949, "width_percent": 0.634}, {"sql": "select * from `negocios` where `negocios`.`id` = 42 limit 1", "type": "query", "params": [], "bindings": [42], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.620297, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 93.584, "width_percent": 0.561}, {"sql": "select * from `negocios` where `negocios`.`id` = 43 limit 1", "type": "query", "params": [], "bindings": [43], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.62152, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 94.145, "width_percent": 0.561}, {"sql": "select * from `negocios` where `negocios`.`id` = 44 limit 1", "type": "query", "params": [], "bindings": [44], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.622716, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 94.706, "width_percent": 0.537}, {"sql": "select * from `negocios` where `negocios`.`id` = 45 limit 1", "type": "query", "params": [], "bindings": [45], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.623899, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 95.243, "width_percent": 0.512}, {"sql": "update `sessions` set `payload` = 'YTo3OntzOjY6Il90b2tlbiI7czo0MDoibEZ5bk0xbldDM3dpUGR0cmhtODI0TnRBWUhpWU1FYTI3anZDb3FKQiI7czozOiJ1cmwiO2E6MDp7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjQ3OiJodHRwczovL21pYS50ZXN0L2FkbWluL3BhZ28tc3VzY3JpcGNpb25zLzIvZWRpdCI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fXM6NTA6ImxvZ2luX3dlYl81OWJhMzZhZGRjMmIyZjk0MDE1ODBmMDE0YzdmNThlYTRlMzA5ODlkIjtpOjE7czoxNzoicGFzc3dvcmRfaGFzaF93ZWIiO3M6NjA6IiQyeSQxMiRXRWxoUlF1YjhqSjU0Rkswa3ZQbXRlbzdvT1BrZzhXSkp5ZzBIUEkxMVhJR0xscUxKa25VMiI7czo4OiJmaWxhbWVudCI7YTowOnt9fQ==', `last_activity` = **********, `user_id` = 1, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'OGF0xmQNkiM3P99CWPuIofWU8bS7yWbRQ0GqToUS'", "type": "query", "params": [], "bindings": ["YTo3OntzOjY6Il90b2tlbiI7czo0MDoibEZ5bk0xbldDM3dpUGR0cmhtODI0TnRBWUhpWU1FYTI3anZDb3FKQiI7czozOiJ1cmwiO2E6MDp7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjQ3OiJodHRwczovL21pYS50ZXN0L2FkbWluL3BhZ28tc3VzY3JpcGNpb25zLzIvZWRpdCI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fXM6NTA6ImxvZ2luX3dlYl81OWJhMzZhZGRjMmIyZjk0MDE1ODBmMDE0YzdmNThlYTRlMzA5ODlkIjtpOjE7czoxNzoicGFzc3dvcmRfaGFzaF93ZWIiO3M6NjA6IiQyeSQxMiRXRWxoUlF1YjhqSjU0Rkswa3ZQbXRlbzdvT1BrZzhXSkp5ZzBIUEkxMVhJR0xscUxKa25VMiI7czo4OiJmaWxhbWVudCI7YTowOnt9fQ==", **********, 1, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "OGF0xmQNkiM3P99CWPuIofWU8bS7yWbRQ0GqToUS"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 140}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 176}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 245}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 130}], "start": **********.800501, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:173", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=173", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "173"}, "connection": "proyecto-mba", "explain": null, "start_percent": 95.755, "width_percent": 4.245}]}, "models": {"data": {"App\\Models\\Suscripcion": {"value": 45, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FModels%2FSuscripcion.php&line=1", "ajax": false, "filename": "Suscripcion.php", "line": "?"}}, "App\\Models\\Negocio": {"value": 45, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FModels%2FNegocio.php&line=1", "ajax": false, "filename": "Negocio.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\PagoSuscripcion": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FModels%2FPagoSuscripcion.php&line=1", "ajax": false, "filename": "PagoSuscripcion.php", "line": "?"}}}, "count": 94, "is_counter": true}, "livewire": {"data": {"app.filament.resources.pago-suscripcion-resource.pages.edit-pago-suscripcion #HGbKV3NEpjr0tCwSEAk1": "array:4 [\n  \"data\" => array:19 [\n    \"data\" => array:9 [\n      \"id\" => 2\n      \"suscripcion_id\" => 3\n      \"metodo_pago\" => \"Eligendi occaecat et\"\n      \"importe\" => \"80.00\"\n      \"fecha_pago\" => \"2021-01-15\"\n      \"transaccion_id\" => \"Neque non praesentiu\"\n      \"estado\" => \"Dignissimos deserunt\"\n      \"created_at\" => \"2025-06-28T14:31:25.000000Z\"\n      \"updated_at\" => \"2025-06-28T14:36:30.000000Z\"\n    ]\n    \"previousUrl\" => \"https://mia.test/admin/pago-suscripcions/2/edit\"\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"activeRelationManager\" => null\n    \"record\" => App\\Models\\PagoSuscripcion {#2957\n      #connection: \"mysql\"\n      #table: \"pago_suscripcions\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:9 [\n        \"id\" => 2\n        \"suscripcion_id\" => 3\n        \"metodo_pago\" => \"Eligendi occaecat et\"\n        \"importe\" => \"80.00\"\n        \"fecha_pago\" => \"2021-01-15\"\n        \"transaccion_id\" => \"Neque non praesentiu\"\n        \"estado\" => \"Dignissimos deserunt\"\n        \"created_at\" => \"2025-06-28 14:31:25\"\n        \"updated_at\" => \"2025-06-28 14:36:30\"\n      ]\n      #original: array:9 [\n        \"id\" => 2\n        \"suscripcion_id\" => 3\n        \"metodo_pago\" => \"Eligendi occaecat et\"\n        \"importe\" => \"80.00\"\n        \"fecha_pago\" => \"2021-01-15\"\n        \"transaccion_id\" => \"Neque non praesentiu\"\n        \"estado\" => \"Dignissimos deserunt\"\n        \"created_at\" => \"2025-06-28 14:31:25\"\n        \"updated_at\" => \"2025-06-28 14:36:30\"\n      ]\n      #changes: []\n      #casts: []\n      #classCastCache: []\n      #attributeCastCache: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: true\n      +usesUniqueIds: false\n      #hidden: []\n      #visible: []\n      #fillable: array:6 [\n        0 => \"suscripcion_id\"\n        1 => \"metodo_pago\"\n        2 => \"importe\"\n        3 => \"transaccion_id\"\n        4 => \"estado\"\n        5 => \"fecha_pago\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n    }\n    \"savedDataHash\" => null\n  ]\n  \"name\" => \"app.filament.resources.pago-suscripcion-resource.pages.edit-pago-suscripcion\"\n  \"component\" => \"App\\Filament\\Resources\\PagoSuscripcionResource\\Pages\\EditPagoSuscripcion\"\n  \"id\" => \"HGbKV3NEpjr0tCwSEAk1\"\n]", "filament.livewire.global-search #CbgmvhlURE32qqw6sN0f": "array:4 [\n  \"data\" => array:1 [\n    \"search\" => \"\"\n  ]\n  \"name\" => \"filament.livewire.global-search\"\n  \"component\" => \"Filament\\Livewire\\GlobalSearch\"\n  \"id\" => \"CbgmvhlURE32qqw6sN0f\"\n]", "filament.livewire.notifications #2RZCdhesarrJTQaXIG2P": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#2575\n      #items: array:1 [\n        \"9f43797f-bd48-46d2-9c40-c17e15a3a23b\" => Filament\\Notifications\\Notification {#2586\n          #evaluationIdentifier: ? string\n          #view: \"filament-notifications::notification\"\n          #defaultView: null\n          #viewData: array:1 [\n            0 => []\n          ]\n          #viewIdentifier: \"notification\"\n          #safeViews: []\n          #isInline: false\n          #actions: []\n          #body: null\n          #date: null\n          #duration: 6000\n          #icon: \"heroicon-o-check-circle\"\n          #iconPosition: null\n          #iconSize: null\n          #iconColor: \"success\"\n          #id: \"9f43797f-bd48-46d2-9c40-c17e15a3a23b\"\n          #status: \"success\"\n          #title: \"Guardado\"\n          #color: null\n          #defaultColor: null\n        }\n      ]\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"2RZCdhesarrJTQaXIG2P\"\n]"}, "count": 3}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 75, "messages": [{"message": "[\n  ability => system.access-panel,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1594166869 data-indent-pad=\"  \"><span class=sf-dump-note>system.access-panel </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">system.access-panel</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1594166869\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.510308, "xdebug_link": null}, {"message": "[\n  ability => system.admin-dashboard,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-668564464 data-indent-pad=\"  \"><span class=sf-dump-note>system.admin-dashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">system.admin-dashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-668564464\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.520012, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\PagoSuscripcion(id=2),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\PagoSuscripcion)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-341125021 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\PagoSuscripcion(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\PagoSuscripcion(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\PagoSuscripcion)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-341125021\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.520419, "xdebug_link": null}, {"message": "[\n  ability => system.admin-dashboard,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1006876285 data-indent-pad=\"  \"><span class=sf-dump-note>system.admin-dashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">system.admin-dashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1006876285\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.535259, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\PagoSuscripcion,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\PagoSuscripcion]\n]", "message_html": "<pre class=sf-dump id=sf-dump-32003416 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\PagoSuscripcion</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\PagoSuscripcion</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; App\\Models\\PagoSuscripcion]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-32003416\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.535416, "xdebug_link": null}, {"message": "[\n  ability => system.admin-dashboard,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-959235080 data-indent-pad=\"  \"><span class=sf-dump-note>system.admin-dashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">system.admin-dashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-959235080\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.543429, "xdebug_link": null}, {"message": "[\n  ability => delete,\n  target => App\\Models\\PagoSuscripcion(id=2),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\PagoSuscripcion)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1107472086 data-indent-pad=\"  \"><span class=sf-dump-note>delete App\\Models\\PagoSuscripcion(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">delete</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\PagoSuscripcion(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\PagoSuscripcion)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1107472086\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.54382, "xdebug_link": null}, {"message": "[\n  ability => system.admin-dashboard,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-584535741 data-indent-pad=\"  \"><span class=sf-dump-note>system.admin-dashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">system.admin-dashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-584535741\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.660436, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\PagoSuscripcion(id=2),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\PagoSuscripcion)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-949442150 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\PagoSuscripcion(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\PagoSuscripcion(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\PagoSuscripcion)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-949442150\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.660622, "xdebug_link": null}, {"message": "[\n  ability => system.admin-dashboard,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1296176735 data-indent-pad=\"  \"><span class=sf-dump-note>system.admin-dashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">system.admin-dashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1296176735\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.662102, "xdebug_link": null}, {"message": "[\n  ability => update,\n  target => App\\Models\\PagoSuscripcion(id=2),\n  result => true,\n  user => 1,\n  arguments => [0 => Object(App\\Models\\PagoSuscripcion)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-398082642 data-indent-pad=\"  \"><span class=sf-dump-note>update App\\Models\\PagoSuscripcion(id=2)</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">update</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"32 characters\">App\\Models\\PagoSuscripcion(id=2)</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"41 characters\">[0 =&gt; Object(App\\Models\\PagoSuscripcion)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-398082642\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.662275, "xdebug_link": null}, {"message": "[\n  ability => system.admin,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1435510924 data-indent-pad=\"  \"><span class=sf-dump-note>system.admin </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">system.admin</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1435510924\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.682028, "xdebug_link": null}, {"message": "[\n  ability => user.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-242077785 data-indent-pad=\"  \"><span class=sf-dump-note>user.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">user.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-242077785\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.683278, "xdebug_link": null}, {"message": "[\n  ability => user.admin-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1135399246 data-indent-pad=\"  \"><span class=sf-dump-note>user.admin-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">user.admin-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1135399246\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.683842, "xdebug_link": null}, {"message": "[\n  ability => system.admin,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1191773813 data-indent-pad=\"  \"><span class=sf-dump-note>system.admin </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">system.admin</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1191773813\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.684415, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\AppVersion,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\AppVersion]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2088660864 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\AppVersion</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\AppVersion</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"28 characters\">[0 =&gt; App\\Models\\AppVersion]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2088660864\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.685449, "xdebug_link": null}, {"message": "[\n  ability => categoria.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1739109016 data-indent-pad=\"  \"><span class=sf-dump-note>categoria.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">categoria.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1739109016\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.686531, "xdebug_link": null}, {"message": "[\n  ability => categoria.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-464867050 data-indent-pad=\"  \"><span class=sf-dump-note>categoria.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">categoria.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-464867050\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.687648, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Categoria,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Categoria]\n]", "message_html": "<pre class=sf-dump id=sf-dump-535681796 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Categoria</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Categoria</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Categoria]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-535681796\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.687792, "xdebug_link": null}, {"message": "[\n  ability => user.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1968584329 data-indent-pad=\"  \"><span class=sf-dump-note>user.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">user.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1968584329\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.688545, "xdebug_link": null}, {"message": "[\n  ability => user.admin-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-710017691 data-indent-pad=\"  \"><span class=sf-dump-note>user.admin-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">user.admin-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-710017691\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.689145, "xdebug_link": null}, {"message": "[\n  ability => system.admin-dashboard,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-430512229 data-indent-pad=\"  \"><span class=sf-dump-note>system.admin-dashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">system.admin-dashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-430512229\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.689759, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Evento,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Evento]\n]", "message_html": "<pre class=sf-dump id=sf-dump-106255190 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Evento</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Evento</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Evento]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-106255190\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.690588, "xdebug_link": null}, {"message": "[\n  ability => localidad.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1574751693 data-indent-pad=\"  \"><span class=sf-dump-note>localidad.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localidad.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1574751693\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.691149, "xdebug_link": null}, {"message": "[\n  ability => localidad.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1777706669 data-indent-pad=\"  \"><span class=sf-dump-note>localidad.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localidad.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1777706669\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.692332, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Localidad,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Localidad]\n]", "message_html": "<pre class=sf-dump id=sf-dump-423261926 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Localidad</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Localidad</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Localidad]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-423261926\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.692481, "xdebug_link": null}, {"message": "[\n  ability => negocio.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-57516028 data-indent-pad=\"  \"><span class=sf-dump-note>negocio.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">negocio.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-57516028\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.693072, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Negocio,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Negocio]\n]", "message_html": "<pre class=sf-dump id=sf-dump-420579561 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Negocio</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Negocio</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Negocio]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-420579561\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.693887, "xdebug_link": null}, {"message": "[\n  ability => system.admin-dashboard,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1109302133 data-indent-pad=\"  \"><span class=sf-dump-note>system.admin-dashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">system.admin-dashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1109302133\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.695463, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\PagoSuscripcion,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\PagoSuscripcion]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2047097959 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\PagoSuscripcion</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\PagoSuscripcion</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; App\\Models\\PagoSuscripcion]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2047097959\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.695652, "xdebug_link": null}, {"message": "[\n  ability => system.permissions,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1006669170 data-indent-pad=\"  \"><span class=sf-dump-note>system.permissions </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">system.permissions</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1006669170\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.696664, "xdebug_link": null}, {"message": "[\n  ability => system.permissions,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1090773802 data-indent-pad=\"  \"><span class=sf-dump-note>system.permissions </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">system.permissions</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1090773802\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.697983, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\Permission\\Models\\Permission,\n  result => true,\n  user => 1,\n  arguments => [0 => <PERSON><PERSON>\\Permission\\Models\\Permission]\n]", "message_html": "<pre class=sf-dump id=sf-dump-706523296 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Permission\\Models\\Permission</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Spatie\\Permission\\Models\\Permission</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"42 characters\">[0 =&gt; Spatie\\Permission\\Models\\Permission]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-706523296\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.69814, "xdebug_link": null}, {"message": "[\n  ability => system.roles,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-680259073 data-indent-pad=\"  \"><span class=sf-dump-note>system.roles </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">system.roles</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-680259073\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.69905, "xdebug_link": null}, {"message": "[\n  ability => system.roles,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1726000257 data-indent-pad=\"  \"><span class=sf-dump-note>system.roles </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">system.roles</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1726000257\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.700387, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\Permission\\Models\\Role,\n  result => true,\n  user => 1,\n  arguments => [0 => <PERSON><PERSON>\\Permission\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-449221773 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Permission\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Spatie\\Permission\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"36 characters\">[0 =&gt; Spatie\\Permission\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-449221773\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.700568, "xdebug_link": null}, {"message": "[\n  ability => user.admin-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1076631324 data-indent-pad=\"  \"><span class=sf-dump-note>user.admin-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">user.admin-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1076631324\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.701763, "xdebug_link": null}, {"message": "[\n  ability => user.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-521314710 data-indent-pad=\"  \"><span class=sf-dump-note>user.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">user.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-521314710\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.703452, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-671557817 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-671557817\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.703613, "xdebug_link": null}, {"message": "[\n  ability => zona.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1916229843 data-indent-pad=\"  \"><span class=sf-dump-note>zona.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">zona.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1916229843\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.704187, "xdebug_link": null}, {"message": "[\n  ability => zona.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-634399475 data-indent-pad=\"  \"><span class=sf-dump-note>zona.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">zona.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-634399475\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.7055, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Zona,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Zona]\n]", "message_html": "<pre class=sf-dump id=sf-dump-343758116 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Zona</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Zona</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Zona]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-343758116\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.705654, "xdebug_link": null}, {"message": "[\n  ability => system.admin-dashboard,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-166796205 data-indent-pad=\"  \"><span class=sf-dump-note>system.admin-dashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">system.admin-dashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-166796205\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.712138, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\PagoSuscripcion,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\PagoSuscripcion]\n]", "message_html": "<pre class=sf-dump id=sf-dump-331880901 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\PagoSuscripcion</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\PagoSuscripcion</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; App\\Models\\PagoSuscripcion]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-331880901\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.712302, "xdebug_link": null}, {"message": "[\n  ability => system.admin,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1882388843 data-indent-pad=\"  \"><span class=sf-dump-note>system.admin </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">system.admin</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1882388843\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.728388, "xdebug_link": null}, {"message": "[\n  ability => user.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-311025833 data-indent-pad=\"  \"><span class=sf-dump-note>user.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">user.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-311025833\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.72925, "xdebug_link": null}, {"message": "[\n  ability => user.admin-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1434126953 data-indent-pad=\"  \"><span class=sf-dump-note>user.admin-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">user.admin-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1434126953\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.729825, "xdebug_link": null}, {"message": "[\n  ability => system.admin,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-298675781 data-indent-pad=\"  \"><span class=sf-dump-note>system.admin </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">system.admin</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-298675781\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.730417, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\AppVersion,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\AppVersion]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1190536535 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\AppVersion</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\AppVersion</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"28 characters\">[0 =&gt; App\\Models\\AppVersion]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1190536535\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.731097, "xdebug_link": null}, {"message": "[\n  ability => categoria.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-323142045 data-indent-pad=\"  \"><span class=sf-dump-note>categoria.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">categoria.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-323142045\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.73174, "xdebug_link": null}, {"message": "[\n  ability => categoria.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1414168189 data-indent-pad=\"  \"><span class=sf-dump-note>categoria.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">categoria.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1414168189\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.732644, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Categoria,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Categoria]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1945411908 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Categoria</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Categoria</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Categoria]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1945411908\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.732796, "xdebug_link": null}, {"message": "[\n  ability => user.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1387471218 data-indent-pad=\"  \"><span class=sf-dump-note>user.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">user.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1387471218\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.733528, "xdebug_link": null}, {"message": "[\n  ability => user.admin-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1982315851 data-indent-pad=\"  \"><span class=sf-dump-note>user.admin-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">user.admin-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1982315851\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.734119, "xdebug_link": null}, {"message": "[\n  ability => system.admin-dashboard,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-508901644 data-indent-pad=\"  \"><span class=sf-dump-note>system.admin-dashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">system.admin-dashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-508901644\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.734785, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Evento,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Evento]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1473180683 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Evento</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Evento</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Evento]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1473180683\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.735434, "xdebug_link": null}, {"message": "[\n  ability => localidad.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1329222927 data-indent-pad=\"  \"><span class=sf-dump-note>localidad.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localidad.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1329222927\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.735976, "xdebug_link": null}, {"message": "[\n  ability => localidad.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-345208334 data-indent-pad=\"  \"><span class=sf-dump-note>localidad.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localidad.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-345208334\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.736791, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Localidad,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Localidad]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1268054040 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Localidad</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Localidad</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Localidad]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1268054040\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.736936, "xdebug_link": null}, {"message": "[\n  ability => negocio.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-142973845 data-indent-pad=\"  \"><span class=sf-dump-note>negocio.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">negocio.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-142973845\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.737512, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Negocio,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Negocio]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1411593354 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Negocio</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Negocio</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Negocio]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1411593354\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.738144, "xdebug_link": null}, {"message": "[\n  ability => system.admin-dashboard,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-135460129 data-indent-pad=\"  \"><span class=sf-dump-note>system.admin-dashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">system.admin-dashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-135460129\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.739376, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\PagoSuscripcion,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\PagoSuscripcion]\n]", "message_html": "<pre class=sf-dump id=sf-dump-612205263 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\PagoSuscripcion</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\PagoSuscripcion</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; App\\Models\\PagoSuscripcion]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-612205263\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.73952, "xdebug_link": null}, {"message": "[\n  ability => system.permissions,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1770136701 data-indent-pad=\"  \"><span class=sf-dump-note>system.permissions </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">system.permissions</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1770136701\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.740262, "xdebug_link": null}, {"message": "[\n  ability => system.permissions,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-156773271 data-indent-pad=\"  \"><span class=sf-dump-note>system.permissions </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">system.permissions</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-156773271\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.74222, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\Permission\\Models\\Permission,\n  result => true,\n  user => 1,\n  arguments => [0 => <PERSON><PERSON>\\Permission\\Models\\Permission]\n]", "message_html": "<pre class=sf-dump id=sf-dump-433848182 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Permission\\Models\\Permission</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Spatie\\Permission\\Models\\Permission</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"42 characters\">[0 =&gt; Spatie\\Permission\\Models\\Permission]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-433848182\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.742409, "xdebug_link": null}, {"message": "[\n  ability => system.roles,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-384272302 data-indent-pad=\"  \"><span class=sf-dump-note>system.roles </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">system.roles</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-384272302\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.743868, "xdebug_link": null}, {"message": "[\n  ability => system.roles,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-256437918 data-indent-pad=\"  \"><span class=sf-dump-note>system.roles </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">system.roles</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-256437918\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.745391, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\Permission\\Models\\Role,\n  result => true,\n  user => 1,\n  arguments => [0 => <PERSON><PERSON>\\Permission\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1958146507 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Permission\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Spatie\\Permission\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"36 characters\">[0 =&gt; Spatie\\Permission\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1958146507\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.745553, "xdebug_link": null}, {"message": "[\n  ability => user.admin-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1972766912 data-indent-pad=\"  \"><span class=sf-dump-note>user.admin-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">user.admin-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1972766912\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.746428, "xdebug_link": null}, {"message": "[\n  ability => user.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-548540304 data-indent-pad=\"  \"><span class=sf-dump-note>user.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">user.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-548540304\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.747613, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-607325661 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-607325661\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.747795, "xdebug_link": null}, {"message": "[\n  ability => zona.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1648674248 data-indent-pad=\"  \"><span class=sf-dump-note>zona.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">zona.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1648674248\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.748721, "xdebug_link": null}, {"message": "[\n  ability => zona.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1163315272 data-indent-pad=\"  \"><span class=sf-dump-note>zona.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">zona.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1163315272\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.749677, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Zona,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Zona]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1179368112 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Zona</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Zona</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Zona]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1179368112\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.749898, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "https://mia.test/admin/pago-suscripcions/2/edit", "action_name": "filament.admin.resources.pago-suscripcions.edit", "controller_action": "App\\Filament\\Resources\\PagoSuscripcionResource\\Pages\\EditPagoSuscripcion", "uri": "GET admin/pago-suscripcions/{record}/edit", "controller": "App\\Filament\\Resources\\PagoSuscripcionResource\\Pages\\EditPagoSuscripcion@render<a href=\"phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/pago-suscripcions", "file": "<a href=\"phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Pages/BasePage.php:51-59</a>", "middleware": "panel:admin, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Filament\\Http\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Filament\\Http\\Middleware\\Authenticate", "duration": "1.13s", "peak_memory": "56MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mia.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"57 characters\">&quot;Brave&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"96 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">es-ES,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">https://mia.test/admin/pago-suscripcions/2/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"709 characters\">XSRF-TOKEN=eyJpdiI6IlZ6aVlZMFNxRFowRGZrditNTWV5T0E9PSIsInZhbHVlIjoiQlprdW5UTXhrQi9mQlFSV0NhNUtQRW5YeFUvV2NiN3JCdzg0TDhPcHBBejM2ZGdZeHR5S2FRSE52RmhtREgyanUrVUkyRmczRjYwZkliRlBqS0tsaHFoMEhNT1FNOXRtSEgyTHNpVDZWbGVUU3RqblFPU0VxRHBac2VWN3IwYzkiLCJtYWMiOiI5MmEwYjlkOTU3ZGNiMjUxMzhmZTA4OTNiODQzNDhlNDQxM2NkZjQxYzQyOWVhNTdiZDVmY2JmNWMwMjczOWZhIiwidGFnIjoiIn0%3D; mia_session=eyJpdiI6Im56TkN0dm90K3FCNkZNZ0ZsdVJ3cUE9PSIsInZhbHVlIjoiQnlIL1VmOTFxV3ovclh2TFVrS2xFbmNFTUFtaEoyUmlMSTIxcHF2UFVpa01JeGlpVEhCREtjK3AzakZReFVJTDNFTFNuNjVmcGR2aXBlSG5KSDNBYlNqeWZ6VzY0aGpjbmEzQ3h5RG5xd3hIeE0wTk1vSmxLVUxwVjVXNUNoamsiLCJtYWMiOiJhMTlkMjZlMzM0MWVhNzZkZDZjMTEwOTJiN2QwZjlhNTQwODk3MGJmOGVlNzI3YWJkOTRiODE4Y2MzMTYzZTdlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1429979939 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lFynM1nWC3wiPdtrhm824NtAYHiYMEa27jvCoqJB</span>\"\n  \"<span class=sf-dump-key>mia_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OGF0xmQNkiM3P99CWPuIofWU8bS7yWbRQ0GqToUS</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1429979939\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 14:38:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"436 characters\">XSRF-TOKEN=eyJpdiI6IlRsdGMxb0FveGdobW9TZmhwUDdmcHc9PSIsInZhbHVlIjoiZmVwMHJQN3VUZTRxOWZtR0o5emlnUUd6SHlqV2l2M2dqYkxOWmtCbkNIM2xVZEtoZENDRUV4YTBuS2FTczB1U1c2SVQrSjVXVlptRHM2K1ZZR2RpdUFJcGplSWxoSDV0bm1CekFWYzlpVlZTL1BWaGREbHhVQjludjhEeGdNbjEiLCJtYWMiOiIzYmY1ZWMyMWI3NmQwM2U2MGQ5YzVmMjhmYTcyZmVlNjg4MGZiOTg3YTNmZjY5NTA0NzQ3ODFlYTkyYWI3OGQ0IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 16:38:22 GMT; Max-Age=7200; path=/; secure; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"447 characters\">mia_session=eyJpdiI6IjZuKzBCZk1VN2JRNzhwQ29JTXlqUFE9PSIsInZhbHVlIjoiWVhBU2xjSmx1VnNVKzRHZlp5cVpaSkx4a1Bub2NVRTRUa1I5MFlZcC9mY0pkeks0M2RFTjVIVU96MmhuUmMzbkxpSVpTdG10ZFJFZk03ZHpYQ2hzVC83UzVJM1NFQ2QyQXNVUVVuRVhTYi9CVy9hWWg4azk3MDhiWWhONWgxcmEiLCJtYWMiOiJjMGM4NGY4NmQ3NWIyNmFiZGRkYTExMTFkZTczMDJiNDI5ZjM2NjdlZWRmMmQyNDcwYWMyMmYwNzJlNzAzYzM4IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 16:38:22 GMT; Max-Age=7200; path=/; secure; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"408 characters\">XSRF-TOKEN=eyJpdiI6IlRsdGMxb0FveGdobW9TZmhwUDdmcHc9PSIsInZhbHVlIjoiZmVwMHJQN3VUZTRxOWZtR0o5emlnUUd6SHlqV2l2M2dqYkxOWmtCbkNIM2xVZEtoZENDRUV4YTBuS2FTczB1U1c2SVQrSjVXVlptRHM2K1ZZR2RpdUFJcGplSWxoSDV0bm1CekFWYzlpVlZTL1BWaGREbHhVQjludjhEeGdNbjEiLCJtYWMiOiIzYmY1ZWMyMWI3NmQwM2U2MGQ5YzVmMjhmYTcyZmVlNjg4MGZiOTg3YTNmZjY5NTA0NzQ3ODFlYTkyYWI3OGQ0IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 16:38:22 GMT; path=/; secure</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"419 characters\">mia_session=eyJpdiI6IjZuKzBCZk1VN2JRNzhwQ29JTXlqUFE9PSIsInZhbHVlIjoiWVhBU2xjSmx1VnNVKzRHZlp5cVpaSkx4a1Bub2NVRTRUa1I5MFlZcC9mY0pkeks0M2RFTjVIVU96MmhuUmMzbkxpSVpTdG10ZFJFZk03ZHpYQ2hzVC83UzVJM1NFQ2QyQXNVUVVuRVhTYi9CVy9hWWg4azk3MDhiWWhONWgxcmEiLCJtYWMiOiJjMGM4NGY4NmQ3NWIyNmFiZGRkYTExMTFkZTczMDJiNDI5ZjM2NjdlZWRmMmQyNDcwYWMyMmYwNzJlNzAzYzM4IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 16:38:22 GMT; path=/; secure; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1200753251 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lFynM1nWC3wiPdtrhm824NtAYHiYMEa27jvCoqJB</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"47 characters\">https://mia.test/admin/pago-suscripcions/2/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$WElhRQub8jJ54FK0kvPmteo7oOPkg8WJJyg0HPI11XIGLlqLJknU2</span>\"\n  \"<span class=sf-dump-key>filament</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1200753251\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://mia.test/admin/pago-suscripcions/2/edit", "action_name": "filament.admin.resources.pago-suscripcions.edit", "controller_action": "App\\Filament\\Resources\\PagoSuscripcionResource\\Pages\\EditPagoSuscripcion"}, "badge": null}}