{"version": 1, "defects": {"Tests\\Feature\\NegocioApiTest::test_can_get_all_negocios": 8, "Tests\\Feature\\NegocioApiTest::test_can_get_negocio_by_id": 8, "Tests\\Feature\\NegocioApiTest::test_authenticated_user_can_create_negocio": 7, "Tests\\Feature\\NegocioApiTest::test_authenticated_user_can_update_their_negocio": 7, "Tests\\Feature\\NegocioApiTest::test_user_cannot_delete_another_users_negocio": 7, "Tests\\Feature\\RegistrationTest::test_registration_screen_cannot_be_rendered_if_support_is_disabled": 1, "Tests\\Feature\\RegistrationTest::test_new_users_can_register": 7, "Tests\\Feature\\NegocioTest::test_el_sistema_crea_un_nuevo_negocio_solo_con_datos_validos": 7, "Tests\\Feature\\NegocioTest::test_el_sistema_edita_correctamente_un_negocio_con_datos_válidos": 8, "Tests\\Feature\\Api\\NegocioApiTest::test_can_get_all_negocios": 7, "Tests\\Feature\\Api\\NegocioApiTest::test_can_get_negocio_by_id": 8, "Tests\\Feature\\Api\\NegocioApiTest::test_usuario_autenticado_puede_editar_negocio_que_le_pertenece_con_datos_validos": 8, "Tests\\Feature\\Api\\NegocioApiTest::test_usuario_autenticado_no_puede_editar_negocio_con_datos_invalidos": 8, "Tests\\Feature\\Api\\NegocioApiTest::test_usuario_no_puede_editar_negocio_de_otro_usuario": 8, "Tests\\Feature\\Api\\NegocioApiTest::test_usuario_puede_borrar_su_propio_negocio": 7, "Tests\\Feature\\Api\\NegocioApiTest::test_usuario_no_puede_borrar_negocio_de_otro_usuario": 8, "Tests\\Feature\\Api\\NegocioApiTest::test_admin_puede_editar_negocio_de_otro_usuario": 8, "Tests\\Feature\\Api\\NegocioApiTest::test_admin_puede_borrar_negocio_de_otro_usuario": 8, "Tests\\Feature\\Auth\\RegistrationTest::test_registration_screen_cannot_be_rendered_if_support_is_disabled": 1, "Tests\\Feature\\NegocioTest::test_el_sistema_muestra_las_paginas_de_negocio_correctamente_segun_el_rol_del_usuario": 7, "Tests\\Feature\\NegocioTest::test_el_sistema_elimina_un_negocio_correctamente": 8, "Tests\\Feature\\Api\\NegocioApiTest::test_usuario_autenticado_puede_crear_negocio_con_datos_validos": 8, "Tests\\Feature\\NegocioTest::test_el_sistema_edita_correctamente_un_negocio_con_datos_validos": 7, "Tests\\Feature\\Api\\NegocioApiTest::test_usuario_autenticado_no_puede_crear_negocio_con_datos_invalidos": 7, "Tests\\Feature\\Api\\CategoriaApiTest::test_el_listado_de_categorias_es_accesible_publicamente": 8, "Tests\\Feature\\Api\\CategoriaApiTest::test_mostrar_una_categoria_es_accesible_publicamente": 7, "Tests\\Feature\\Api\\CategoriaApiTest::test_solo_admins_pueden_crear_categorias": 7, "Tests\\Feature\\Api\\CategoriaApiTest::test_solo_admins_pueden_editar_zonas": 7, "Tests\\Feature\\Api\\CategoriaApiTest::test_el_sistema_no_edita_una_zona_con_datos_invalidos": 8, "Tests\\Feature\\Api\\CategoriaApiTest::test_solo_admins_pueden_borrar_zonas": 7, "Tests\\Feature\\CategoriaTest::test_el_sistema_muestra_las_paginas_de_categoria_solo_a_admins": 7, "Tests\\Feature\\CategoriaTest::test_el_sistema_crea_una_nueva_categoria_solo_con_datos_validos": 8, "Tests\\Feature\\CategoriaTest::test_el_sistema_edita_una_categoria_solo_con_datos_validos": 8, "Tests\\Feature\\CategoriaTest::test_el_sistema_elimina_correctamente_una_categoria": 8, "Tests\\Feature\\CategoriaTest::test_can_retrieve_all_descendants_recursively": 8, "Tests\\Feature\\Api\\ZonaApiTest::test_el_listado_de_zonas_es_accesible_publicamente": 8, "Tests\\Feature\\Api\\ZonaApiTest::test_mostrar_una_zona_es_accesible_publicamente": 8, "Tests\\Feature\\Api\\ZonaApiTest::test_solo_admins_puede_crear_zonas": 8, "Tests\\Feature\\Api\\ZonaApiTest::test_solo_admins_puede_editar_zonas": 8, "Tests\\Feature\\Api\\ZonaApiTest::test_el_sistem_no_edita_una_zona_con_datos_invalidos": 8, "Tests\\Feature\\Api\\ZonaApiTest::test_solo_superadmin_puede_borrar_zonas": 8, "Tests\\Feature\\ZonaTest::test_el_sistema_muestra_las_paginas_de_zona_solo_a_admins": 7, "Tests\\Feature\\ZonaTest::test_el_sistema_crea_una_nueva_zona_solo_con_datos_validos": 8, "Tests\\Feature\\ZonaTest::test_el_sistema_actualiza_una_zona_solo_con_datos_validos": 8, "Tests\\Feature\\ZonaTest::test_el_sistema_elimina_correctamente_una_zona": 8, "Tests\\Feature\\LocalidadTest::test_el_sistema_crea_una_nueva_localidad_solo_con_datos_validos": 5, "Tests\\Feature\\Api\\LocalidadApiTest::test_el_listado_de_localidades_es_accesible_publicamente": 7, "Tests\\Feature\\Api\\LocalidadApiTest::test_solo_admins_puede_crear_localidades": 5, "Tests\\Feature\\Api\\LocalidadApiTest::test_el_sistema_no_crea_nueva_localidad_con_datos_inválidos": 8, "Tests\\Feature\\Api\\LocalidadApiTest::test_solo_admins_pueden_editar_localidades": 5, "Tests\\Feature\\Api\\LocalidadApiTest::test_el_sistema_no_edita_una_localidad_con_datos_invalidos": 8, "Tests\\Feature\\Api\\LocalidadApiTest::test_solo_superadmin_puede_borrar_localidades": 5, "Tests\\Feature\\LocalidadTest::test_el_sistema_edita_una_localidad_solo_con_datos_validos": 5, "Tests\\Feature\\LocalidadTest::test_el_sistema_muestra_las_paginas_de_localidad_correctamente_segun_el_rol_del_usuario": 7, "Tests\\Feature\\Api\\ZonaApiTest::test_cache_de_zonas": 7, "Tests\\Feature\\Api\\LocalidadApiTest::test_mostrar_una_localidad_es_accesible_publicamente": 5, "Tests\\Feature\\LocalidadTest::test_el_sistema_elimina_correctamente_una_localidad": 5, "Tests\\Feature\\Api\\LocalidadApiTest::test_el_listado_de_localidades_es_accesible_solo_a_roles_con_permiso": 8, "Tests\\Feature\\Api\\LocalidadApiTest::test_mostrar_una_localidad_es_accesible_solo_a_roles_con_permiso": 8, "Tests\\Feature\\Api\\LocalidadApiTest::test_solo_usuarios_con_permiso_pueden_crear_localidades": 8, "Tests\\Feature\\ZonaTest::test_el_sistema_muestra_las_paginas_de_zona_correctamente_segun_el_rol_del_usuario": 7, "Tests\\Feature\\CategoriaTest::test_el_sistema_muestra_las_paginas_de_categoria_correctamente_segun_el_rol_del_usuario": 7, "Tests\\Feature\\Api\\CategoriaApiTest::test_el_sistema_no_crea_nueva_categoria_con_datos_inválidos": 8, "Tests\\Feature\\Api\\CategoriaApiTest::test_solo_admins_pueden_editar_categorias": 7, "Tests\\Feature\\Api\\CategoriaApiTest::test_mostrar_una_zona_es_accesible_solo_a_roles_con_permiso": 7, "Tests\\Feature\\Api\\CategoriaApiTest::test_el_listado_de_categorias_es_accesible_solo_a_roles_con_permiso": 7, "Tests\\Feature\\Api\\CategoriaApiTest::test_mostrar_una_zona_es_accesible_publicamente": 8, "Tests\\Feature\\Api\\NegocioApiTest::test_solo_usuarios_con_permiso_pueden_crear_negocios": 8, "Tests\\Feature\\Api\\NegocioApiTest::test_usuario_no_puede_borrar_su_propio_negocio": 8, "Tests\\Feature\\Api\\NegocioApiTest::test_solo_usuarios_con_permiso_pueden_editar_negocios": 8, "Tests\\Feature\\EventoTest::test_example": 7, "Tests\\Feature\\EventoTest::test_el_sistema_muestra_las_paginas_de_evento_correctamente_segun_el_rol_del_usuario": 7, "Tests\\Feature\\EventoTest::test_el_sistema_crea_un_nuevo_evento_solo_con_datos_validos": 7, "Tests\\Feature\\EventoTest::test_el_sistema_edita_un_evento_solo_con_datos_validos": 8, "Tests\\Feature\\Api\\EventoApiTest::test_solo_usuarios_con_permiso_pueden_crear_eventos": 8, "Tests\\Feature\\Api\\EventoApiTest::test_solo_usuarios_con_permiso_pueden_editar_eventos": 8, "Tests\\Feature\\Api\\EventoApiTest::test_solo_usuarios_con_permiso_pueden_borrar_eventos": 8, "Tests\\Feature\\Api\\EventoApiTest::test_el_listado_de_eventos_es_accesible_publicamente": 8, "Tests\\Feature\\NegocioTest::test_el_sistema_crea_un_nuevo_negocio_con_datos_validos": 7, "Tests\\Feature\\NegocioTest::test_el_sistema_no_crea_un_negocio_con_datos_invalidos": 8, "Tests\\Feature\\Web\\CategoriaTest::test_el_sistema_crea_una_nueva_categoria_solo_con_datos_validos": 8, "Tests\\Feature\\Web\\CategoriaTest::test_el_sistema_edita_una_categoria_solo_con_datos_validos": 8, "Tests\\Feature\\Web\\EventoTest::test_el_sistema_crea_un_nuevo_evento_solo_con_datos_validos": 8, "Tests\\Feature\\Web\\EventoTest::test_el_sistema_edita_un_evento_solo_con_datos_validos": 8, "Tests\\Feature\\Web\\NegocioTest::test_el_sistema_crea_un_nuevo_negocio_con_datos_validos": 8, "Tests\\Feature\\Web\\NegocioTest::test_el_sistema_edita_correctamente_un_negocio_con_datos_validos": 8, "Tests\\Feature\\Web\\EventoTest::test_el_sistema_elimina_correctamente_un_evento": 8, "Tests\\Feature\\Auth\\AuthenticationTest::test_login_screen_can_be_rendered": 8, "Tests\\Feature\\Auth\\AuthenticationTest::test_users_can_authenticate_using_the_login_screen": 8, "Tests\\Feature\\Auth\\AuthenticationTest::test_users_can_not_authenticate_with_invalid_password": 8, "Tests\\Feature\\Auth\\BrowserSessionsTest::test_other_browser_sessions_can_be_logged_out": 8, "Tests\\Feature\\Auth\\DeleteAccountTest::test_user_accounts_can_be_deleted": 8, "Tests\\Feature\\Auth\\DeleteAccountTest::test_correct_password_must_be_provided_before_account_can_be_deleted": 8, "Tests\\Feature\\Auth\\EmailVerificationTest::test_email_verification_screen_can_be_rendered": 8, "Tests\\Feature\\Auth\\EmailVerificationTest::test_email_can_be_verified": 8, "Tests\\Feature\\Auth\\EmailVerificationTest::test_email_can_not_verified_with_invalid_hash": 8, "Tests\\Feature\\Auth\\PasswordConfirmationTest::test_confirm_password_screen_can_be_rendered": 8, "Tests\\Feature\\Auth\\PasswordConfirmationTest::test_password_can_be_confirmed": 8, "Tests\\Feature\\Auth\\PasswordConfirmationTest::test_password_is_not_confirmed_with_invalid_password": 8, "Tests\\Feature\\Auth\\PasswordResetTest::test_reset_password_link_screen_can_be_rendered": 8, "Tests\\Feature\\Auth\\PasswordResetTest::test_reset_password_link_can_be_requested": 8, "Tests\\Feature\\Auth\\PasswordResetTest::test_reset_password_screen_can_be_rendered": 8, "Tests\\Feature\\Auth\\PasswordResetTest::test_password_can_be_reset_with_valid_token": 8, "Tests\\Feature\\Auth\\ProfileInformationTest::test_current_profile_information_is_available": 8, "Tests\\Feature\\Auth\\ProfileInformationTest::test_profile_information_can_be_updated": 8, "Tests\\Feature\\Auth\\RegistrationTest::test_registration_screen_can_be_rendered": 8, "Tests\\Feature\\Auth\\RegistrationTest::test_new_users_can_register": 8, "Tests\\Feature\\Auth\\TwoFactorAuthenticationSettingsTest::test_two_factor_authentication_can_be_enabled": 8, "Tests\\Feature\\Auth\\TwoFactorAuthenticationSettingsTest::test_recovery_codes_can_be_regenerated": 8, "Tests\\Feature\\Auth\\TwoFactorAuthenticationSettingsTest::test_two_factor_authentication_can_be_disabled": 8, "Tests\\Feature\\Auth\\UpdatePasswordTest::test_password_can_be_updated": 8, "Tests\\Feature\\Auth\\UpdatePasswordTest::test_current_password_must_be_correct": 8, "Tests\\Feature\\Auth\\UpdatePasswordTest::test_new_passwords_must_match": 8, "Tests\\Feature\\Api\\Auth\\ApiTokenPermissionsTest::test_api_token_permissions_can_be_updated": 8, "Tests\\Feature\\Api\\Auth\\CreateApiTokenTest::test_api_tokens_can_be_created": 8, "Tests\\Feature\\Api\\Auth\\DeleteApiTokenTest::test_api_tokens_can_be_deleted": 8, "Tests\\Feature\\Api\\CategoriaApiTest::test_solo_usuarios_con_permiso_pueden_crear_categorias": 8, "Tests\\Feature\\Api\\CategoriaApiTest::test_solo_usuarios_con_permiso_pueden_editar_categorias": 8, "Tests\\Feature\\Api\\CategoriaApiTest::test_solo_los_usuarios_con_permiso_pueden_borrar_categorias": 8, "Tests\\Feature\\Api\\EventoApiTest::test_mostrar_un_evento_es_accesible_publicamente": 8, "Tests\\Feature\\Api\\EventoApiTest::test_el_sistema_no_crea_nuevo_evento_con_datos_inválidos": 8, "Tests\\Feature\\Api\\LocalidadApiTest::test_solo_usuarios_con_permiso_pueden_editar_localidades": 8, "Tests\\Feature\\Api\\LocalidadApiTest::test_solo_los_usuarios_con_permiso_pueden_borrar_localidades": 8, "Tests\\Feature\\Api\\NegocioApiTest::test_el_listado_de_negocios_es_accesible_publicamente": 8, "Tests\\Feature\\Api\\NegocioApiTest::test_mostrar_un_negocio_es_accesible_publicamente": 8, "Tests\\Feature\\Api\\NegocioApiTest::test_el_sistema_no_crea_nuevo_negocio_con_datos_inválidos": 8, "Tests\\Feature\\Api\\NegocioApiTest::test_el_sistema_no_actualiza_negocio_con_datos_inválidos": 8, "Tests\\Feature\\Api\\NegocioApiTest::test_solo_usuarios_con_permiso_pueden_borrar_negocios": 8, "Tests\\Feature\\Api\\ZonaApiTest::test_el_sistema_no_crea_nueva_zona_con_datos_inválidos": 8, "Tests\\Feature\\Api\\ZonaApiTest::test_solo_usuarios_con_permiso_pueden_editar_zonas": 8, "Tests\\Feature\\Api\\ZonaApiTest::test_solo_los_usuarios_con_permiso_pueden_borrar_zonas": 8, "Tests\\Feature\\Web\\CategoriaTest::test_el_sistema_muestra_las_paginas_de_categoria_correctamente_segun_el_rol_del_usuario": 8, "Tests\\Feature\\Web\\CategoriaTest::test_el_sistema_elimina_correctamente_una_categoria": 8, "Tests\\Feature\\Web\\CategoriaTest::test_can_retrieve_all_descendants_recursively": 8, "Tests\\Feature\\Web\\EventoTest::test_el_sistema_muestra_las_paginas_de_evento_correctamente_segun_el_rol_del_usuario": 8, "Tests\\Feature\\Web\\LocalidadTest::test_el_sistema_muestra_las_paginas_de_localidad_correctamente_segun_el_rol_del_usuario": 8, "Tests\\Feature\\Web\\LocalidadTest::test_el_sistema_crea_una_nueva_localidad_solo_con_datos_validos": 8, "Tests\\Feature\\Web\\LocalidadTest::test_el_sistema_edita_una_localidad_solo_con_datos_validos": 8, "Tests\\Feature\\Web\\LocalidadTest::test_el_sistema_elimina_correctamente_una_localidad": 8, "Tests\\Feature\\Web\\NegocioTest::test_el_sistema_muestra_las_paginas_de_negocio_correctamente_segun_el_rol_del_usuario": 8, "Tests\\Feature\\Web\\NegocioTest::test_el_sistema_no_crea_un_negocio_con_datos_invalidos": 8, "Tests\\Feature\\Web\\NegocioTest::test_el_sistema_elimina_un_negocio_correctamente": 8, "Tests\\Feature\\Web\\ZonaTest::test_el_sistema_muestra_las_paginas_de_zona_correctamente_segun_el_rol_del_usuario": 8, "Tests\\Feature\\Web\\ZonaTest::test_el_sistema_crea_una_nueva_zona_solo_con_datos_validos": 8, "Tests\\Feature\\Web\\ZonaTest::test_el_sistema_edita_una_zona_solo_con_datos_validos": 8, "Tests\\Feature\\Web\\ZonaTest::test_el_sistema_elimina_correctamente_una_zona": 8, "Tests\\Feature\\Web\\PagoSuscripcionTest::test_el_sistema_muestra_las_paginas_de_pago_suscripcion_correctamente_segun_el_rol_del_usuario": 8, "Tests\\Feature\\Web\\PagoSuscripcionTest::test_el_sistema_crea_un_nuevo_pago_solo_con_datos_validos": 7, "Tests\\Feature\\Web\\PagoSuscripcionTest::test_el_sistema_edita_un_pago_solo_con_datos_validos": 8}, "times": {"Tests\\Unit\\ExampleTest::test_that_true_is_true": 0.003, "Tests\\Feature\\ApiTokenPermissionsTest::test_api_token_permissions_can_be_updated": 0.461, "Tests\\Feature\\AuthenticationTest::test_login_screen_can_be_rendered": 0.28, "Tests\\Feature\\AuthenticationTest::test_users_can_authenticate_using_the_login_screen": 0.267, "Tests\\Feature\\AuthenticationTest::test_users_can_not_authenticate_with_invalid_password": 0.254, "Tests\\Feature\\BrowserSessionsTest::test_other_browser_sessions_can_be_logged_out": 0.279, "Tests\\Feature\\CreateApiTokenTest::test_api_tokens_can_be_created": 0.301, "Tests\\Feature\\DeleteAccountTest::test_user_accounts_can_be_deleted": 0.29, "Tests\\Feature\\DeleteAccountTest::test_correct_password_must_be_provided_before_account_can_be_deleted": 0.271, "Tests\\Feature\\DeleteApiTokenTest::test_api_tokens_can_be_deleted": 0.29, "Tests\\Feature\\EmailVerificationTest::test_email_verification_screen_can_be_rendered": 0.27, "Tests\\Feature\\EmailVerificationTest::test_email_can_be_verified": 0.264, "Tests\\Feature\\EmailVerificationTest::test_email_can_not_verified_with_invalid_hash": 0.31, "Tests\\Feature\\ExampleTest::test_the_application_returns_a_successful_response": 0.261, "Tests\\Feature\\NegocioApiTest::test_can_get_all_negocios": 0.016, "Tests\\Feature\\NegocioApiTest::test_can_get_negocio_by_id": 0.254, "Tests\\Feature\\NegocioApiTest::test_unauthenticated_user_cannot_create_negocio": 0.255, "Tests\\Feature\\NegocioApiTest::test_authenticated_user_can_create_negocio": 0.019, "Tests\\Feature\\NegocioApiTest::test_authenticated_user_can_update_their_negocio": 0.017, "Tests\\Feature\\NegocioApiTest::test_user_cannot_update_another_users_negocio": 0.016, "Tests\\Feature\\NegocioApiTest::test_authenticated_user_can_delete_their_negocio": 0.394, "Tests\\Feature\\NegocioApiTest::test_user_cannot_delete_another_users_negocio": 0.403, "Tests\\Feature\\PasswordConfirmationTest::test_confirm_password_screen_can_be_rendered": 0.301, "Tests\\Feature\\PasswordConfirmationTest::test_password_can_be_confirmed": 0.293, "Tests\\Feature\\PasswordConfirmationTest::test_password_is_not_confirmed_with_invalid_password": 0.495, "Tests\\Feature\\PasswordResetTest::test_reset_password_link_screen_can_be_rendered": 0.268, "Tests\\Feature\\PasswordResetTest::test_reset_password_link_can_be_requested": 0.3, "Tests\\Feature\\PasswordResetTest::test_reset_password_screen_can_be_rendered": 0.264, "Tests\\Feature\\PasswordResetTest::test_password_can_be_reset_with_valid_token": 0.268, "Tests\\Feature\\ProfileInformationTest::test_current_profile_information_is_available": 0.273, "Tests\\Feature\\ProfileInformationTest::test_profile_information_can_be_updated": 0.271, "Tests\\Feature\\RegistrationTest::test_registration_screen_can_be_rendered": 0.266, "Tests\\Feature\\RegistrationTest::test_registration_screen_cannot_be_rendered_if_support_is_disabled": 0.019, "Tests\\Feature\\RegistrationTest::test_new_users_can_register": 0.259, "Tests\\Feature\\TwoFactorAuthenticationSettingsTest::test_two_factor_authentication_can_be_enabled": 0.37, "Tests\\Feature\\TwoFactorAuthenticationSettingsTest::test_recovery_codes_can_be_regenerated": 0.451, "Tests\\Feature\\TwoFactorAuthenticationSettingsTest::test_two_factor_authentication_can_be_disabled": 0.364, "Tests\\Feature\\UpdatePasswordTest::test_password_can_be_updated": 0.304, "Tests\\Feature\\UpdatePasswordTest::test_current_password_must_be_correct": 0.285, "Tests\\Feature\\UpdatePasswordTest::test_new_passwords_must_match": 0.266, "Tests\\Feature\\CategoriaApiTest::test_el_listado_de_categorias_es_accesible_publicamente": 0.269, "Tests\\Feature\\CategoriaApiTest::test_mostrar_una_categoria_es_accesible_publicamente": 0.252, "Tests\\Feature\\CategoriaApiTest::test_solo_admins_pueden_crear_categorias": 0.295, "Tests\\Feature\\CategoriaApiTest::test_el_sistema_no_crea_nueva_categoria_con_datos_inválidos": 0.27, "Tests\\Feature\\CategoriaApiTest::test_solo_admins_pueden_editar_zonas": 0.267, "Tests\\Feature\\CategoriaApiTest::test_el_sistem_no_edita_una_zona_con_datos_invalidos": 0.255, "Tests\\Feature\\CategoriaApiTest::test_solo_admins_pueden_borrar_zonas": 0.261, "Tests\\Feature\\NegocioApiTest::test_usuario_autenticado_puede_crear_negocio_con_datos_validos": 0.271, "Tests\\Feature\\NegocioApiTest::test_usuario_autenticado_no_puede_crear_negocio_con_datos_invalidos": 0.274, "Tests\\Feature\\NegocioApiTest::test_usuario_autenticado_puede_editar_negocio_que_le_pertenece_con_datos_validos": 0.273, "Tests\\Feature\\NegocioApiTest::test_usuario_autenticado_no_puede_editar_negocio_con_datos_invalidos": 0.269, "Tests\\Feature\\NegocioApiTest::test_usuario_no_puede_editar_negocio_de_otro_usuario": 0.01, "Tests\\Feature\\NegocioApiTest::test_usuario_puede_borrar_su_propio_negocio": 0.25, "Tests\\Feature\\NegocioApiTest::test_usuario_no_puede_borrar_negocio_de_otro_usuario": 0.252, "Tests\\Feature\\NegocioApiTest::test_admin_puede_editar_negocio_de_otro_usuario": 0.275, "Tests\\Feature\\NegocioApiTest::test_admin_puede_borrar_negocio_de_otro_usuario": 0.265, "Tests\\Feature\\ZonaApiTest::test_el_listado_de_zonas_es_accesible_publicamente": 0.259, "Tests\\Feature\\ZonaApiTest::test_mostrar_una_zona_es_accesible_publicamente": 0.247, "Tests\\Feature\\ZonaApiTest::test_solo_superadmin_puede_crear_zonas": 0.267, "Tests\\Feature\\ZonaApiTest::test_el_sistema_no_crea_nueva_zona_con_datos_inválidos": 0.261, "Tests\\Feature\\ZonaApiTest::test_solo_superadmin_puede_editar_zonas": 0.265, "Tests\\Feature\\ZonaApiTest::test_el_sistem_no_edita_una_zona_con_datos_invalidos": 0.257, "Tests\\Feature\\ZonaApiTest::test_solo_superadmin_puede_borrar_zonas": 0.26, "Tests\\Feature\\Api\\NegocioApiTest::test_can_get_all_negocios": 0.368, "Tests\\Feature\\Api\\NegocioApiTest::test_can_get_negocio_by_id": 0.285, "Tests\\Feature\\Api\\NegocioApiTest::test_unauthenticated_user_cannot_create_negocio": 0.258, "Tests\\Feature\\Api\\NegocioApiTest::test_usuario_autenticado_puede_crear_negocio_con_datos_validos": 0.003, "Tests\\Feature\\Api\\NegocioApiTest::test_usuario_autenticado_no_puede_crear_negocio_con_datos_invalidos": 0.314, "Tests\\Feature\\Api\\NegocioApiTest::test_usuario_autenticado_puede_editar_negocio_que_le_pertenece_con_datos_validos": 0.003, "Tests\\Feature\\Api\\NegocioApiTest::test_usuario_autenticado_no_puede_editar_negocio_con_datos_invalidos": 0.001, "Tests\\Feature\\Api\\NegocioApiTest::test_usuario_no_puede_editar_negocio_de_otro_usuario": 0.043, "Tests\\Feature\\Api\\NegocioApiTest::test_usuario_puede_borrar_su_propio_negocio": 0.276, "Tests\\Feature\\Api\\NegocioApiTest::test_usuario_no_puede_borrar_negocio_de_otro_usuario": 0.001, "Tests\\Feature\\Api\\NegocioApiTest::test_admin_puede_editar_negocio_de_otro_usuario": 0.003, "Tests\\Feature\\Api\\NegocioApiTest::test_admin_puede_borrar_negocio_de_otro_usuario": 0.001, "Tests\\Feature\\NegocioTest::test_el_sistema_muestra_las_paginas_de_negocio_correctamente_segun_el_rol_del_usuario": 3.047, "Tests\\Feature\\NegocioTest::test_el_sistema_crea_un_nuevo_negocio_solo_con_datos_validos": 5.428, "Tests\\Feature\\NegocioTest::test_el_sistema_edita_correctamente_un_negocio_con_datos_válidos": 2.684, "Tests\\Feature\\NegocioTest::test_el_sistema_elimina_un_negocio_correctamente": 1.021, "Tests\\Feature\\Api\\Auth\\ApiTokenPermissionsTest::test_api_token_permissions_can_be_updated": 0.531, "Tests\\Feature\\Api\\Auth\\CreateApiTokenTest::test_api_tokens_can_be_created": 0.516, "Tests\\Feature\\Api\\Auth\\DeleteApiTokenTest::test_api_tokens_can_be_deleted": 0.487, "Tests\\Feature\\Api\\CategoriaApiTest::test_el_listado_de_categorias_es_accesible_publicamente": 0.5, "Tests\\Feature\\Api\\CategoriaApiTest::test_mostrar_una_categoria_es_accesible_publicamente": 0.006, "Tests\\Feature\\Api\\CategoriaApiTest::test_solo_admins_pueden_crear_categorias": 0.012, "Tests\\Feature\\Api\\CategoriaApiTest::test_el_sistema_no_crea_nueva_categoria_con_datos_inválidos": 0.479, "Tests\\Feature\\Api\\CategoriaApiTest::test_solo_admins_pueden_editar_zonas": 0.277, "Tests\\Feature\\Api\\CategoriaApiTest::test_el_sistema_no_edita_una_zona_con_datos_invalidos": 0.482, "Tests\\Feature\\Api\\CategoriaApiTest::test_solo_admins_pueden_borrar_zonas": 0.311, "Tests\\Feature\\Api\\ZonaApiTest::test_el_listado_de_zonas_es_accesible_publicamente": 0.501, "Tests\\Feature\\Api\\ZonaApiTest::test_mostrar_una_zona_es_accesible_publicamente": 0.459, "Tests\\Feature\\Api\\ZonaApiTest::test_solo_admins_puede_crear_zonas": 0.494, "Tests\\Feature\\Api\\ZonaApiTest::test_el_sistema_no_crea_nueva_zona_con_datos_inválidos": 0.495, "Tests\\Feature\\Api\\ZonaApiTest::test_solo_admins_puede_editar_zonas": 0.275, "Tests\\Feature\\Api\\ZonaApiTest::test_el_sistem_no_edita_una_zona_con_datos_invalidos": 0.475, "Tests\\Feature\\Api\\ZonaApiTest::test_solo_superadmin_puede_borrar_zonas": 0.27, "Tests\\Feature\\Auth\\AuthenticationTest::test_login_screen_can_be_rendered": 0.577, "Tests\\Feature\\Auth\\AuthenticationTest::test_users_can_authenticate_using_the_login_screen": 0.484, "Tests\\Feature\\Auth\\AuthenticationTest::test_users_can_not_authenticate_with_invalid_password": 0.457, "Tests\\Feature\\Auth\\BrowserSessionsTest::test_other_browser_sessions_can_be_logged_out": 0.519, "Tests\\Feature\\Auth\\DeleteAccountTest::test_user_accounts_can_be_deleted": 0.503, "Tests\\Feature\\Auth\\DeleteAccountTest::test_correct_password_must_be_provided_before_account_can_be_deleted": 0.48, "Tests\\Feature\\Auth\\EmailVerificationTest::test_email_verification_screen_can_be_rendered": 0.465, "Tests\\Feature\\Auth\\EmailVerificationTest::test_email_can_be_verified": 0.475, "Tests\\Feature\\Auth\\EmailVerificationTest::test_email_can_not_verified_with_invalid_hash": 0.467, "Tests\\Feature\\Auth\\PasswordConfirmationTest::test_confirm_password_screen_can_be_rendered": 0.472, "Tests\\Feature\\Auth\\PasswordConfirmationTest::test_password_can_be_confirmed": 0.466, "Tests\\Feature\\Auth\\PasswordConfirmationTest::test_password_is_not_confirmed_with_invalid_password": 0.66, "Tests\\Feature\\Auth\\PasswordResetTest::test_reset_password_link_screen_can_be_rendered": 0.484, "Tests\\Feature\\Auth\\PasswordResetTest::test_reset_password_link_can_be_requested": 0.676, "Tests\\Feature\\Auth\\PasswordResetTest::test_reset_password_screen_can_be_rendered": 0.69, "Tests\\Feature\\Auth\\PasswordResetTest::test_password_can_be_reset_with_valid_token": 0.686, "Tests\\Feature\\Auth\\ProfileInformationTest::test_current_profile_information_is_available": 0.49, "Tests\\Feature\\Auth\\ProfileInformationTest::test_profile_information_can_be_updated": 0.473, "Tests\\Feature\\Auth\\RegistrationTest::test_registration_screen_can_be_rendered": 0.465, "Tests\\Feature\\Auth\\RegistrationTest::test_registration_screen_cannot_be_rendered_if_support_is_disabled": 0.019, "Tests\\Feature\\Auth\\RegistrationTest::test_new_users_can_register": 0.485, "Tests\\Feature\\Auth\\TwoFactorAuthenticationSettingsTest::test_two_factor_authentication_can_be_enabled": 0.597, "Tests\\Feature\\Auth\\TwoFactorAuthenticationSettingsTest::test_recovery_codes_can_be_regenerated": 0.675, "Tests\\Feature\\Auth\\TwoFactorAuthenticationSettingsTest::test_two_factor_authentication_can_be_disabled": 0.525, "Tests\\Feature\\Auth\\UpdatePasswordTest::test_password_can_be_updated": 0.495, "Tests\\Feature\\Auth\\UpdatePasswordTest::test_current_password_must_be_correct": 0.481, "Tests\\Feature\\Auth\\UpdatePasswordTest::test_new_passwords_must_match": 0.461, "Tests\\Feature\\CategoriaTest::test_el_sistema_muestra_las_paginas_de_categoria_solo_a_admins": 0.905, "Tests\\Feature\\CategoriaTest::test_el_sistema_crea_una_nueva_categoria_solo_con_datos_validos": 1.963, "Tests\\Feature\\CategoriaTest::test_el_sistema_edita_una_categoria_solo_con_datos_validos": 1.9, "Tests\\Feature\\CategoriaTest::test_el_sistema_elimina_correctamente_una_categoria": 1.46, "Tests\\Feature\\CategoriaTest::test_can_retrieve_all_descendants_recursively": 0.009, "Tests\\Feature\\ZonaTest::test_el_sistema_muestra_las_paginas_de_zona_solo_a_admins": 0.797, "Tests\\Feature\\ZonaTest::test_el_sistema_crea_una_nueva_zona_solo_con_datos_validos": 1.172, "Tests\\Feature\\ZonaTest::test_el_sistema_actualiza_una_zona_solo_con_datos_validos": 0.981, "Tests\\Feature\\ZonaTest::test_el_sistema_elimina_correctamente_una_zona": 0.941, "Tests\\Feature\\NegocioTest::test_el_sistema_edita_correctamente_un_negocio_con_datos_validos": 7.163, "Tests\\Feature\\Api\\CategoriaApiTest::test_solo_admins_pueden_editar_categorias": 0.007, "Tests\\Feature\\LocalidadTest::test_el_sistema_muestra_las_paginas_de_localidad_correctamente_segun_el_rol_del_usuario": 2.295, "Tests\\Feature\\LocalidadTest::test_el_sistema_crea_una_nueva_localidad_solo_con_datos_validos": 1.855, "Tests\\Feature\\LocalidadTest::test_el_sistema_edita_una_localidad_solo_con_datos_validos": 2.092, "Tests\\Feature\\LocalidadTest::test_el_sistema_elimina_correctamente_una_localidad": 1.331, "Tests\\Feature\\Api\\LocalidadApiTest::test_el_listado_de_localidades_es_accesible_publicamente": 0.031, "Tests\\Feature\\Api\\LocalidadApiTest::test_mostrar_una_localidad_es_accesible_publicamente": 0.005, "Tests\\Feature\\Api\\LocalidadApiTest::test_solo_admins_puede_crear_localidades": 0.303, "Tests\\Feature\\Api\\LocalidadApiTest::test_el_sistema_no_crea_nueva_localidad_con_datos_inválidos": 0.502, "Tests\\Feature\\Api\\LocalidadApiTest::test_solo_admins_pueden_editar_localidades": 0.302, "Tests\\Feature\\Api\\LocalidadApiTest::test_el_sistema_no_edita_una_localidad_con_datos_invalidos": 0.507, "Tests\\Feature\\Api\\LocalidadApiTest::test_solo_superadmin_puede_borrar_localidades": 0.303, "Tests\\Feature\\Api\\ZonaApiTest::test_cache_de_zonas": 0.269, "Tests\\Feature\\CategoriaTest::test_el_sistema_muestra_las_paginas_de_categoria_correctamente_segun_el_rol_del_usuario": 4.002, "Tests\\Feature\\Api\\NegocioApiTest::test_usuario_no_puede_borrar_su_propio_negocio": 0.008, "Tests\\Feature\\Api\\LocalidadApiTest::test_el_listado_de_localidades_es_accesible_solo_a_roles_con_permiso": 0.515, "Tests\\Feature\\Api\\LocalidadApiTest::test_mostrar_una_localidad_es_accesible_solo_a_roles_con_permiso": 0.484, "Tests\\Feature\\Api\\LocalidadApiTest::test_solo_usuarios_con_permiso_pueden_crear_localidades": 0.506, "Tests\\Feature\\Api\\LocalidadApiTest::test_solo_usuarios_con_permiso_pueden_editar_localidades": 0.497, "Tests\\Feature\\Api\\LocalidadApiTest::test_solo_los_usuarios_con_permiso_pueden_borrar_localidades": 0.482, "Tests\\Feature\\Api\\ZonaApiTest::test_el_listado_de_zonas_es_accesible_solo_a_roles_con_permiso": 0.344, "Tests\\Feature\\Api\\ZonaApiTest::test_mostrar_una_zona_es_accesible_solo_a_roles_con_permiso": 0.327, "Tests\\Feature\\Api\\ZonaApiTest::test_solo_usuarios_con_permiso_pueden_editar_zonas": 0.514, "Tests\\Feature\\Api\\ZonaApiTest::test_solo_los_usuarios_con_permiso_pueden_borrar_zonas": 0.483, "Tests\\Feature\\ZonaTest::test_el_sistema_muestra_las_paginas_de_zona_correctamente_segun_el_rol_del_usuario": 1.413, "Tests\\Feature\\ZonaTest::test_el_sistema_edita_una_zona_solo_con_datos_validos": 1.308, "Tests\\Feature\\Api\\CategoriaApiTest::test_el_listado_de_categorias_es_accesible_solo_a_roles_con_permiso": 0.4, "Tests\\Feature\\Api\\CategoriaApiTest::test_mostrar_una_zona_es_accesible_solo_a_roles_con_permiso": 0.306, "Tests\\Feature\\Api\\CategoriaApiTest::test_solo_usuarios_con_permiso_pueden_crear_categorias": 0.505, "Tests\\Feature\\Api\\CategoriaApiTest::test_solo_usuarios_con_permiso_pueden_editar_categorias": 0.504, "Tests\\Feature\\Api\\CategoriaApiTest::test_solo_los_usuarios_con_permiso_pueden_borrar_categorias": 0.499, "Tests\\Feature\\Api\\CategoriaApiTest::test_mostrar_una_zona_es_accesible_publicamente": 0.453, "Tests\\Feature\\Api\\NegocioApiTest::test_el_listado_de_negocios_es_accesible_publicamente": 0.554, "Tests\\Feature\\Api\\NegocioApiTest::test_mostrar_un_negocio_es_accesible_publicamente": 0.512, "Tests\\Feature\\Api\\NegocioApiTest::test_solo_usuarios_con_permiso_pueden_crear_negocios": 0.54, "Tests\\Feature\\Api\\NegocioApiTest::test_el_sistema_no_crea_nuevo_negocio_con_datos_inválidos": 0.537, "Tests\\Feature\\Api\\NegocioApiTest::test_solo_usuarios_con_permiso_pueden_editar_negocios": 0.662, "Tests\\Feature\\Api\\NegocioApiTest::test_el_sistema_no_actualiza_negocio_con_datos_inválidos": 0.598, "Tests\\Feature\\Api\\NegocioApiTest::test_solo_usuarios_con_permiso_pueden_borrar_negocios": 0.614, "Tests\\Feature\\EventoTest::test_example": 0.347, "Tests\\Feature\\EventoTest::test_el_sistema_muestra_las_paginas_de_evento_correctamente_segun_el_rol_del_usuario": 3.686, "Tests\\Feature\\EventoTest::test_el_sistema_crea_un_nuevo_evento_solo_con_datos_validos": 6.731, "Tests\\Feature\\EventoTest::test_el_sistema_edita_un_evento_solo_con_datos_validos": 3.288, "Tests\\Feature\\EventoTest::test_el_sistema_elimina_correctamente_un_evento": 2.019, "Tests\\Feature\\Api\\EventoApiTest::test_el_listado_de_eventos_es_accesible_publicamente": 0.507, "Tests\\Feature\\Api\\EventoApiTest::test_mostrar_un_evento_es_accesible_publicamente": 0.504, "Tests\\Feature\\Api\\EventoApiTest::test_solo_usuarios_con_permiso_pueden_crear_eventos": 0.559, "Tests\\Feature\\Api\\EventoApiTest::test_el_sistema_no_crea_nuevo_evento_con_datos_inválidos": 0.538, "Tests\\Feature\\Api\\EventoApiTest::test_solo_usuarios_con_permiso_pueden_editar_eventos": 0.572, "Tests\\Feature\\Api\\EventoApiTest::test_solo_usuarios_con_permiso_pueden_borrar_eventos": 0.55, "Tests\\Feature\\NegocioTest::test_el_sistema_crea_un_nuevo_negocio_con_datos_validos": 4.701, "Tests\\Feature\\NegocioTest::test_el_sistema_no_crea_un_negocio_con_datos_invalidos": 5.937, "Tests\\Unit\\BasicTest::test_that_true_is_true": 0.01, "Tests\\Feature\\Web\\CategoriaTest::test_el_sistema_muestra_las_paginas_de_categoria_correctamente_segun_el_rol_del_usuario": 1.408, "Tests\\Feature\\Web\\CategoriaTest::test_el_sistema_crea_una_nueva_categoria_solo_con_datos_validos": 1.859, "Tests\\Feature\\Web\\CategoriaTest::test_el_sistema_edita_una_categoria_solo_con_datos_validos": 1.52, "Tests\\Feature\\Web\\CategoriaTest::test_el_sistema_elimina_correctamente_una_categoria": 1.002, "Tests\\Feature\\Web\\CategoriaTest::test_can_retrieve_all_descendants_recursively": 0.005, "Tests\\Feature\\Web\\EventoTest::test_el_sistema_muestra_las_paginas_de_evento_correctamente_segun_el_rol_del_usuario": 1.71, "Tests\\Feature\\Web\\EventoTest::test_el_sistema_crea_un_nuevo_evento_solo_con_datos_validos": 7.717, "Tests\\Feature\\Web\\EventoTest::test_el_sistema_edita_un_evento_solo_con_datos_validos": 2.713, "Tests\\Feature\\Web\\EventoTest::test_el_sistema_elimina_correctamente_un_evento": 1.357, "Tests\\Feature\\Web\\LocalidadTest::test_el_sistema_muestra_las_paginas_de_localidad_correctamente_segun_el_rol_del_usuario": 1.214, "Tests\\Feature\\Web\\LocalidadTest::test_el_sistema_crea_una_nueva_localidad_solo_con_datos_validos": 0.995, "Tests\\Feature\\Web\\LocalidadTest::test_el_sistema_edita_una_localidad_solo_con_datos_validos": 1.061, "Tests\\Feature\\Web\\LocalidadTest::test_el_sistema_elimina_correctamente_una_localidad": 0.882, "Tests\\Feature\\Web\\NegocioTest::test_el_sistema_muestra_las_paginas_de_negocio_correctamente_segun_el_rol_del_usuario": 1.824, "Tests\\Feature\\Web\\NegocioTest::test_el_sistema_crea_un_nuevo_negocio_con_datos_validos": 6.272, "Tests\\Feature\\Web\\NegocioTest::test_el_sistema_no_crea_un_negocio_con_datos_invalidos": 3.991, "Tests\\Feature\\Web\\NegocioTest::test_el_sistema_edita_correctamente_un_negocio_con_datos_validos": 7.698, "Tests\\Feature\\Web\\NegocioTest::test_el_sistema_elimina_un_negocio_correctamente": 1.106, "Tests\\Feature\\Web\\ZonaTest::test_el_sistema_muestra_las_paginas_de_zona_correctamente_segun_el_rol_del_usuario": 1.244, "Tests\\Feature\\Web\\ZonaTest::test_el_sistema_crea_una_nueva_zona_solo_con_datos_validos": 1.137, "Tests\\Feature\\Web\\ZonaTest::test_el_sistema_edita_una_zona_solo_con_datos_validos": 1.261, "Tests\\Feature\\Web\\ZonaTest::test_el_sistema_elimina_correctamente_una_zona": 0.914, "Tests\\Feature\\Web\\PagoSuscripcionTest::test_el_sistema_muestra_las_paginas_de_pago_suscripcion_correctamente_segun_el_rol_del_usuario": 1.355, "Tests\\Feature\\Web\\PagoSuscripcionTest::test_el_sistema_crea_un_nuevo_pago_solo_con_datos_validos": 1.227, "Tests\\Feature\\Web\\PagoSuscripcionTest::test_el_sistema_edita_un_pago_solo_con_datos_validos": 1.387}}