<?php

namespace App\Http\Requests;

use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Foundation\Http\FormRequest;

class UpdatePagoSuscripcionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        /** @var User */
        $user = Auth::user();

        if ($user->can('system.admin-dashboard')) {
            return true;
        }

        return false;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'suscripcion_id' => 'sometimes|exists:suscripciones,id',
            'metodo_pago' => 'sometimes|string',
            'importe' => 'sometimes|numeric',
            'fecha_pago' => 'sometimes|date',
            'transaccion_id' => 'sometimes|string',
            'estado' => 'sometimes|string',
        ];
    }
}
