<?php
// webapp\app\Filament\Widgets\SuscriptionsSummary.php

namespace App\Filament\Widgets;

use App\Models\Negocio;
use App\Enums\EstadoSuscripcion;
use Filament\Widgets\Widget;

class SuscriptionsSummary extends Widget
{
    protected static string $view = 'filament.widgets.suscriptions-summary';

    public $negociosActivos;
    public $negociosInactivos;
    public $negociosTotales;

    public function mount(): void
    {
        $this->negociosActivos = Negocio::whereHas('suscripcion', function ($query) {
            $query->where('status', EstadoSuscripcion::ACTIVE->value);
        })->count();

        $this->negociosInactivos = Negocio::whereHas('suscripcion', function ($query) {
            $query->where('status', EstadoSuscripcion::INACTIVE->value);
        })->count();

        $this->negociosTotales = $this->negociosActivos + $this->negociosInactivos;
    }

    protected static ?int $sort = 1;
}
