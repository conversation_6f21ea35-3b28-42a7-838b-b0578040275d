{"__meta": {"id": "01JYV0QQ192V6WYJHPT7637G82", "datetime": "2025-06-28 10:18:40", "utime": **********.042326, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751105916.731758, "end": **********.042367, "duration": 3.3106088638305664, "duration_str": "3.31s", "measures": [{"label": "Booting", "start": 1751105916.731758, "relative_start": 0, "end": **********.898039, "relative_end": **********.898039, "duration": 1.**************, "duration_str": "1.17s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.898068, "relative_start": 1.****************, "end": **********.042371, "relative_end": 4.0531158447265625e-06, "duration": 2.*************, "duration_str": "2.14s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.41265, "relative_start": 2.***************, "end": **********.416918, "relative_end": **********.416918, "duration": 0.*****************, "duration_str": "4.27ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: filament.widgets.negocios-map-with-filters", "start": **********.800254, "relative_start": 3.***************, "end": **********.800254, "relative_end": **********.800254, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.822322, "relative_start": 3.****************, "end": **********.822322, "relative_end": **********.822322, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.832441, "relative_start": 3.1006829738616943, "end": **********.832441, "relative_end": **********.832441, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.842075, "relative_start": 3.1103169918060303, "end": **********.842075, "relative_end": **********.842075, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.852807, "relative_start": 3.121048927307129, "end": **********.852807, "relative_end": **********.852807, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.widgets.map", "start": **********.956279, "relative_start": 3.2245209217071533, "end": **********.956279, "relative_end": **********.956279, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-maps::components.map", "start": **********.966865, "relative_start": 3.235106945037842, "end": **********.966865, "relative_end": **********.966865, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-maps::zoom-action", "start": **********.968918, "relative_start": 3.2371599674224854, "end": **********.968918, "relative_end": **********.968918, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-maps::components.actions.action", "start": **********.970177, "relative_start": 3.2384188175201416, "end": **********.970177, "relative_end": **********.970177, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7c308a600d1ef5875264e294ad27b34e", "start": **********.971843, "relative_start": 3.2400848865509033, "end": **********.971843, "relative_end": **********.971843, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-maps::components.icon-button", "start": **********.972871, "relative_start": 3.2411129474639893, "end": **********.972871, "relative_end": **********.972871, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-maps::components.actions.action", "start": **********.976847, "relative_start": 3.245088815689087, "end": **********.976847, "relative_end": **********.976847, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7c308a600d1ef5875264e294ad27b34e", "start": **********.978153, "relative_start": 3.2463948726654053, "end": **********.978153, "relative_end": **********.978153, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-maps::components.icon-button", "start": **********.979117, "relative_start": 3.247358798980713, "end": **********.979117, "relative_end": **********.979117, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.037848, "relative_start": 3.3060898780822754, "end": **********.03955, "relative_end": **********.03955, "duration": 0.0017020702362060547, "duration_str": "1.7ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 50764760, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.45.0", "PHP Version": "8.3.8", "Environment": "local", "Debug Mode": "Enabled", "URL": "mia.test", "Timezone": "UTC", "Locale": "es"}}, "views": {"count": 14, "nb_templates": 14, "templates": [{"name": "filament.widgets.negocios-map-with-filters", "param_count": null, "params": [], "start": **********.8002, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\resources\\views/filament/widgets/negocios-map-with-filters.blade.phpfilament.widgets.negocios-map-with-filters", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fnegocios-map-with-filters.blade.php&line=1", "ajax": false, "filename": "negocios-map-with-filters.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.822273, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.832391, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.842007, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.852755, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "filament.widgets.map", "param_count": null, "params": [], "start": **********.956106, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\resources\\views/filament/widgets/map.blade.phpfilament.widgets.map", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fresources%2Fviews%2Ffilament%2Fwidgets%2Fmap.blade.php&line=1", "ajax": false, "filename": "map.blade.php", "line": "?"}}, {"name": "filament-maps::components.map", "param_count": null, "params": [], "start": **********.966754, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\vendor\\webbingbrasil\\filament-maps\\src\\/../resources/views/components/map.blade.phpfilament-maps::components.map", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Fwebbingbrasil%2Ffilament-maps%2Fresources%2Fviews%2Fcomponents%2Fmap.blade.php&line=1", "ajax": false, "filename": "map.blade.php", "line": "?"}}, {"name": "filament-maps::zoom-action", "param_count": null, "params": [], "start": **********.968818, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\vendor\\webbingbrasil\\filament-maps\\src\\/../resources/views/zoom-action.blade.phpfilament-maps::zoom-action", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Fwebbingbrasil%2Ffilament-maps%2Fresources%2Fviews%2Fzoom-action.blade.php&line=1", "ajax": false, "filename": "zoom-action.blade.php", "line": "?"}}, {"name": "filament-maps::components.actions.action", "param_count": null, "params": [], "start": **********.970041, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\vendor\\webbingbrasil\\filament-maps\\src\\/../resources/views/components/actions/action.blade.phpfilament-maps::components.actions.action", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Fwebbingbrasil%2Ffilament-maps%2Fresources%2Fviews%2Fcomponents%2Factions%2Faction.blade.php&line=1", "ajax": false, "filename": "action.blade.php", "line": "?"}}, {"name": "__components::7c308a600d1ef5875264e294ad27b34e", "param_count": null, "params": [], "start": **********.971794, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\storage\\framework\\views/7c308a600d1ef5875264e294ad27b34e.blade.php__components::7c308a600d1ef5875264e294ad27b34e", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fstorage%2Fframework%2Fviews%2F7c308a600d1ef5875264e294ad27b34e.blade.php&line=1", "ajax": false, "filename": "7c308a600d1ef5875264e294ad27b34e.blade.php", "line": "?"}}, {"name": "filament-maps::components.icon-button", "param_count": null, "params": [], "start": **********.972771, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\vendor\\webbingbrasil\\filament-maps\\src\\/../resources/views/components/icon-button.blade.phpfilament-maps::components.icon-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Fwebbingbrasil%2Ffilament-maps%2Fresources%2Fviews%2Fcomponents%2Ficon-button.blade.php&line=1", "ajax": false, "filename": "icon-button.blade.php", "line": "?"}}, {"name": "filament-maps::components.actions.action", "param_count": null, "params": [], "start": **********.976725, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\vendor\\webbingbrasil\\filament-maps\\src\\/../resources/views/components/actions/action.blade.phpfilament-maps::components.actions.action", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Fwebbingbrasil%2Ffilament-maps%2Fresources%2Fviews%2Fcomponents%2Factions%2Faction.blade.php&line=1", "ajax": false, "filename": "action.blade.php", "line": "?"}}, {"name": "__components::7c308a600d1ef5875264e294ad27b34e", "param_count": null, "params": [], "start": **********.978103, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\storage\\framework\\views/7c308a600d1ef5875264e294ad27b34e.blade.php__components::7c308a600d1ef5875264e294ad27b34e", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fstorage%2Fframework%2Fviews%2F7c308a600d1ef5875264e294ad27b34e.blade.php&line=1", "ajax": false, "filename": "7c308a600d1ef5875264e294ad27b34e.blade.php", "line": "?"}}, {"name": "filament-maps::components.icon-button", "param_count": null, "params": [], "start": **********.979015, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\vendor\\webbingbrasil\\filament-maps\\src\\/../resources/views/components/icon-button.blade.phpfilament-maps::components.icon-button", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Fwebbingbrasil%2Ffilament-maps%2Fresources%2Fviews%2Fcomponents%2Ficon-button.blade.php&line=1", "ajax": false, "filename": "icon-button.blade.php", "line": "?"}}]}, "queries": {"count": 17, "nb_statements": 17, "nb_visible_statements": 17, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.027080000000000003, "accumulated_duration_str": "27.08ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'LqdP3Q0O7a3cwoJmYgCvHDyb4m34SlxmUHxhBxPb' limit 1", "type": "query", "params": [], "bindings": ["LqdP3Q0O7a3cwoJmYgCvHDyb4m34SlxmUHxhBxPb"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 105}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 89}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.427291, "duration": 0.00405, "duration_str": "4.05ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "proyecto-mba", "explain": null, "start_percent": 0, "width_percent": 14.956}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 180}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.453195, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "proyecto-mba", "explain": null, "start_percent": 14.956, "width_percent": 2.437}, {"sql": "select * from `cache` where `key` in ('spatie.permission.cache')", "type": "query", "params": [], "bindings": ["spatie.permission.cache"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 418}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.458949, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "proyecto-mba", "explain": null, "start_percent": 17.393, "width_percent": 3.25}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (1) and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 571}], "start": **********.471774, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:328", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=328", "ajax": false, "filename": "HasPermissions.php", "line": "328"}, "connection": "proyecto-mba", "explain": null, "start_percent": 20.643, "width_percent": 2.954}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 314}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}], "start": **********.475301, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "proyecto-mba", "explain": null, "start_percent": 23.597, "width_percent": 3.434}, {"sql": "select * from `localidades`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/NegociosMapWithFilters.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Widgets\\NegociosMapWithFilters.php", "line": 56}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Concerns/InteractsWithForms.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Concerns\\InteractsWithForms.php", "line": 355}, {"index": 20, "namespace": null, "name": "vendor/filament/forms/src/Concerns/InteractsWithForms.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Concerns\\InteractsWithForms.php", "line": 340}, {"index": 21, "namespace": null, "name": "vendor/filament/forms/src/Concerns/InteractsWithForms.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Concerns\\InteractsWithForms.php", "line": 409}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Concerns/InteractsWithForms.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Concerns\\InteractsWithForms.php", "line": 400}], "start": **********.521594, "duration": 0.00302, "duration_str": "3.02ms", "memory": 0, "memory_str": null, "filename": "NegociosMapWithFilters.php:56", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/NegociosMapWithFilters.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Widgets\\NegociosMapWithFilters.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FWidgets%2FNegociosMapWithFilters.php&line=56", "ajax": false, "filename": "NegociosMapWithFilters.php", "line": "56"}, "connection": "proyecto-mba", "explain": null, "start_percent": 27.031, "width_percent": 11.152}, {"sql": "select * from `zonas`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/NegociosMapWithFilters.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Widgets\\NegociosMapWithFilters.php", "line": 65}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Concerns/InteractsWithForms.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Concerns\\InteractsWithForms.php", "line": 355}, {"index": 20, "namespace": null, "name": "vendor/filament/forms/src/Concerns/InteractsWithForms.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Concerns\\InteractsWithForms.php", "line": 340}, {"index": 21, "namespace": null, "name": "vendor/filament/forms/src/Concerns/InteractsWithForms.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Concerns\\InteractsWithForms.php", "line": 409}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Concerns/InteractsWithForms.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Concerns\\InteractsWithForms.php", "line": 400}], "start": **********.527749, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "NegociosMapWithFilters.php:65", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/NegociosMapWithFilters.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Widgets\\NegociosMapWithFilters.php", "line": 65}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FWidgets%2FNegociosMapWithFilters.php&line=65", "ajax": false, "filename": "NegociosMapWithFilters.php", "line": "65"}, "connection": "proyecto-mba", "explain": null, "start_percent": 38.183, "width_percent": 3.804}, {"sql": "select * from `categorias`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/NegociosMapWithFilters.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Widgets\\NegociosMapWithFilters.php", "line": 74}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Concerns/InteractsWithForms.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Concerns\\InteractsWithForms.php", "line": 355}, {"index": 20, "namespace": null, "name": "vendor/filament/forms/src/Concerns/InteractsWithForms.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Concerns\\InteractsWithForms.php", "line": 340}, {"index": 21, "namespace": null, "name": "vendor/filament/forms/src/Concerns/InteractsWithForms.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Concerns\\InteractsWithForms.php", "line": 409}, {"index": 22, "namespace": null, "name": "vendor/filament/forms/src/Concerns/InteractsWithForms.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Concerns\\InteractsWithForms.php", "line": 400}], "start": **********.532176, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "NegociosMapWithFilters.php:74", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/NegociosMapWithFilters.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Widgets\\NegociosMapWithFilters.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FWidgets%2FNegociosMapWithFilters.php&line=74", "ajax": false, "filename": "NegociosMapWithFilters.php", "line": "74"}, "connection": "proyecto-mba", "explain": null, "start_percent": 41.987, "width_percent": 4.985}, {"sql": "select * from `negocios`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/NegociosMapWithFilters.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Widgets\\NegociosMapWithFilters.php", "line": 134}, {"index": 16, "namespace": null, "name": "app/Filament/Widgets/NegociosMapWithFilters.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Widgets\\NegociosMapWithFilters.php", "line": 39}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}], "start": **********.5521312, "duration": 0.00188, "duration_str": "1.88ms", "memory": 0, "memory_str": null, "filename": "NegociosMapWithFilters.php:134", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/NegociosMapWithFilters.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Widgets\\NegociosMapWithFilters.php", "line": 134}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FWidgets%2FNegociosMapWithFilters.php&line=134", "ajax": false, "filename": "NegociosMapWithFilters.php", "line": "134"}, "connection": "proyecto-mba", "explain": null, "start_percent": 46.972, "width_percent": 6.942}, {"sql": "select `categorias`.*, `categoria_negocio`.`negocio_id` as `pivot_negocio_id`, `categoria_negocio`.`categoria_id` as `pivot_categoria_id` from `categorias` inner join `categoria_negocio` on `categorias`.`id` = `categoria_negocio`.`categoria_id` where `categoria_negocio`.`negocio_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Filament/Widgets/NegociosMapWithFilters.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Widgets\\NegociosMapWithFilters.php", "line": 134}, {"index": 20, "namespace": null, "name": "app/Filament/Widgets/NegociosMapWithFilters.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Widgets\\NegociosMapWithFilters.php", "line": 39}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}], "start": **********.5593548, "duration": 0.00166, "duration_str": "1.66ms", "memory": 0, "memory_str": null, "filename": "NegociosMapWithFilters.php:134", "source": {"index": 19, "namespace": null, "name": "app/Filament/Widgets/NegociosMapWithFilters.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Widgets\\NegociosMapWithFilters.php", "line": 134}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FWidgets%2FNegociosMapWithFilters.php&line=134", "ajax": false, "filename": "NegociosMapWithFilters.php", "line": "134"}, "connection": "proyecto-mba", "explain": null, "start_percent": 53.914, "width_percent": 6.13}, {"sql": "select * from `zonas` where `zonas`.`id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Widgets/NegociosMapWithFilters.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Widgets\\NegociosMapWithFilters.php", "line": 134}, {"index": 21, "namespace": null, "name": "app/Filament/Widgets/NegociosMapWithFilters.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Widgets\\NegociosMapWithFilters.php", "line": 39}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}], "start": **********.5734758, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "NegociosMapWithFilters.php:134", "source": {"index": 20, "namespace": null, "name": "app/Filament/Widgets/NegociosMapWithFilters.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Widgets\\NegociosMapWithFilters.php", "line": 134}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FWidgets%2FNegociosMapWithFilters.php&line=134", "ajax": false, "filename": "NegociosMapWithFilters.php", "line": "134"}, "connection": "proyecto-mba", "explain": null, "start_percent": 60.044, "width_percent": 3.508}, {"sql": "select * from `localidades` where `localidades`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "app/Filament/Widgets/NegociosMapWithFilters.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Widgets\\NegociosMapWithFilters.php", "line": 134}, {"index": 26, "namespace": null, "name": "app/Filament/Widgets/NegociosMapWithFilters.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Widgets\\NegociosMapWithFilters.php", "line": 39}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}], "start": **********.57735, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "NegociosMapWithFilters.php:134", "source": {"index": 25, "namespace": null, "name": "app/Filament/Widgets/NegociosMapWithFilters.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Widgets\\NegociosMapWithFilters.php", "line": 134}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FWidgets%2FNegociosMapWithFilters.php&line=134", "ajax": false, "filename": "NegociosMapWithFilters.php", "line": "134"}, "connection": "proyecto-mba", "explain": null, "start_percent": 63.552, "width_percent": 2.548}, {"sql": "select * from `suscripciones` where `suscripciones`.`negocio_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Widgets/NegociosMapWithFilters.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Widgets\\NegociosMapWithFilters.php", "line": 134}, {"index": 21, "namespace": null, "name": "app/Filament/Widgets/NegociosMapWithFilters.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Widgets\\NegociosMapWithFilters.php", "line": 39}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}], "start": **********.586621, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "NegociosMapWithFilters.php:134", "source": {"index": 20, "namespace": null, "name": "app/Filament/Widgets/NegociosMapWithFilters.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Widgets\\NegociosMapWithFilters.php", "line": 134}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FWidgets%2FNegociosMapWithFilters.php&line=134", "ajax": false, "filename": "NegociosMapWithFilters.php", "line": "134"}, "connection": "proyecto-mba", "explain": null, "start_percent": 66.1, "width_percent": 7.201}, {"sql": "select * from `negocios` where `id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47)", "type": "query", "params": [], "bindings": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Widgets/NegociosMap.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Widgets\\NegociosMap.php", "line": 166}, {"index": 16, "namespace": null, "name": "vendor/webbingbrasil/filament-maps/src/Concerns/HasMarkers.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\webbingbrasil\\filament-maps\\src\\Concerns\\HasMarkers.php", "line": 14}, {"index": 17, "namespace": null, "name": "vendor/webbingbrasil/filament-maps/src/Widgets/MapWidget.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\webbingbrasil\\filament-maps\\src\\Widgets\\MapWidget.php", "line": 63}, {"index": 18, "namespace": null, "name": "vendor/webbingbrasil/filament-maps/src/Widgets/MapWidget.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\webbingbrasil\\filament-maps\\src\\Widgets\\MapWidget.php", "line": 57}, {"index": 19, "namespace": null, "name": "app/Filament/Widgets/NegociosMap.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Widgets\\NegociosMap.php", "line": 56}], "start": **********.864131, "duration": 0.00354, "duration_str": "3.54ms", "memory": 0, "memory_str": null, "filename": "NegociosMap.php:166", "source": {"index": 15, "namespace": null, "name": "app/Filament/Widgets/NegociosMap.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Widgets\\NegociosMap.php", "line": 166}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FWidgets%2FNegociosMap.php&line=166", "ajax": false, "filename": "NegociosMap.php", "line": "166"}, "connection": "proyecto-mba", "explain": null, "start_percent": 73.301, "width_percent": 13.072}, {"sql": "select * from `suscripciones` where `suscripciones`.`negocio_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Widgets/NegociosMap.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Widgets\\NegociosMap.php", "line": 166}, {"index": 21, "namespace": null, "name": "vendor/webbingbrasil/filament-maps/src/Concerns/HasMarkers.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\webbingbrasil\\filament-maps\\src\\Concerns\\HasMarkers.php", "line": 14}, {"index": 22, "namespace": null, "name": "vendor/webbingbrasil/filament-maps/src/Widgets/MapWidget.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\webbingbrasil\\filament-maps\\src\\Widgets\\MapWidget.php", "line": 63}, {"index": 23, "namespace": null, "name": "vendor/webbingbrasil/filament-maps/src/Widgets/MapWidget.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\webbingbrasil\\filament-maps\\src\\Widgets\\MapWidget.php", "line": 57}, {"index": 24, "namespace": null, "name": "app/Filament/Widgets/NegociosMap.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Widgets\\NegociosMap.php", "line": 56}], "start": **********.8759341, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "NegociosMap.php:166", "source": {"index": 20, "namespace": null, "name": "app/Filament/Widgets/NegociosMap.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Widgets\\NegociosMap.php", "line": 166}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FWidgets%2FNegociosMap.php&line=166", "ajax": false, "filename": "NegociosMap.php", "line": "166"}, "connection": "proyecto-mba", "explain": null, "start_percent": 86.374, "width_percent": 5.65}, {"sql": "select * from `localidades`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/NegociosMap.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Widgets\\NegociosMap.php", "line": 130}, {"index": 17, "namespace": "view", "name": "filament.widgets.map", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\resources\\views/filament/widgets/map.blade.php", "line": 42}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.958231, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "NegociosMap.php:130", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/NegociosMap.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Widgets\\NegociosMap.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FWidgets%2FNegociosMap.php&line=130", "ajax": false, "filename": "NegociosMap.php", "line": "130"}, "connection": "proyecto-mba", "explain": null, "start_percent": 92.024, "width_percent": 3.951}, {"sql": "select * from `localidades`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/NegociosMap.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Widgets\\NegociosMap.php", "line": 130}, {"index": 17, "namespace": "view", "name": "filament.widgets.map", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\resources\\views/filament/widgets/map.blade.php", "line": 49}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.9630039, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "NegociosMap.php:130", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/NegociosMap.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Widgets\\NegociosMap.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FWidgets%2FNegociosMap.php&line=130", "ajax": false, "filename": "NegociosMap.php", "line": "130"}, "connection": "proyecto-mba", "explain": null, "start_percent": 95.975, "width_percent": 4.025}]}, "models": {"data": {"App\\Models\\Negocio": {"value": 94, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FModels%2FNegocio.php&line=1", "ajax": false, "filename": "Negocio.php", "line": "?"}}, "App\\Models\\Suscripcion": {"value": 94, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FModels%2FSuscripcion.php&line=1", "ajax": false, "filename": "Suscripcion.php", "line": "?"}}, "App\\Models\\Categoria": {"value": 62, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FModels%2FCategoria.php&line=1", "ajax": false, "filename": "Categoria.php", "line": "?"}}, "App\\Models\\Localidad": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FModels%2FLocalidad.php&line=1", "ajax": false, "filename": "Localidad.php", "line": "?"}}, "App\\Models\\Zona": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FModels%2FZona.php&line=1", "ajax": false, "filename": "Zona.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 261, "is_counter": true}, "livewire": {"data": {"app.filament.widgets.negocios-map-with-filters #Q0xgzrnkWgaymMNvbmX5": "array:4 [\n  \"data\" => array:7 [\n    \"negocios\" => Illuminate\\Database\\Eloquent\\Collection {#2989\n      #items: array:47 [\n        0 => App\\Models\\Negocio {#3213\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [\n            \"id\" => 1\n            \"nombre\" => \"Chiringuito Sabor Tropical\"\n            \"descripcion\" => \"Magnam aperiam doloribus ut tempora autem dolore suscipit. Quis ut illum doloremque vero et. Ut illum sequi ex explicabo. Ipsa qui culpa vero amet quos nihil.\"\n            \"direccion\" => \"Ronda Aguilar, 5, 17º 2º, 68143, Gaona Alta\"\n            \"ubicacion\" => \"{\"latitud\": \"37.009604\", \"longitud\": \"-6.563694\"}\"\n            \"horario\" => \"{\"lunes\": [\"09:00-18:00\"], \"jueves\": [\"09:00-18:00\"], \"martes\": [\"09:00-18:00\"], \"domingo\": [\"09:00-18:00\"], \"sábado\": [\"09:00-18:00\"], \"viernes\": [\"09:00-18:00\"], \"miércoles\": [\"09:00-18:00\"]}\"\n            \"enlaces_sociales\" => \"[{\"url\": \"https://es-es.facebook.com/cocoamatalascanas\", \"plataforma\": \"facebook\"}, {\"url\": \"https://www.instagram.com/cocoamatalascanas/\", \"plataforma\": \"instagram\"}, {\"url\": \"https://x.com/cocoamision\", \"plataforma\": \"twitter\"}]\"\n            \"enlaces_propios\" => \"{\"Pagina Web\": \"https://www.instagram.com/cocoamatalascanas/\"}\"\n            \"precios\" => \"[{\"categoria\": \"Ensaladas\", \"productos\": {\"Ensalada César\": \"6.50\", \"Ensalada Capresse\": \"7.00\", \"Ensalada de Frutas\": \"5.50\", \"Ensalada de Verduras\": \"5.00\", \"Ensalada Mediterránea\": \"8.00\"}}, {\"categoria\": \"Entrantes\", \"productos\": {\"Bruschetta\": \"4.50\", \"Pan de Jamón\": \"4.50\", \"Patatas Bravas\": \"4.00\", \"Ensaladilla Rusa\": \"3.50\", \"Croquetas de Pollo\": \"5.00\"}}, {\"categoria\": \"Fritos\", \"productos\": {\"Choco Frito\": \"6.00\", \"Pollo Frito\": \"6.50\", \"Patatas Fritas\": \"3.00\", \"Zucchini Fritas\": \"4.50\", \"Calamares Fritos\": \"7.00\"}}, {\"categoria\": \"Carnes\", \"productos\": {\"Pechuga de pollo\": \"9.50\", \"Bistec de ternera\": \"14.00\", \"Costilla de cerdo\": \"11.00\", \"Filete de ternera\": \"12.00\", \"Chuletón de cerdo\": \"13.50\"}}, {\"categoria\": \"Pescado\", \"productos\": {\"Filete de atún\": \"15.00\", \"Filete de bonito\": \"10.00\", \"Filete de dorado\": \"14.00\", \"Filete de merluza\": \"9.50\", \"Filete de lenguado\": \"12.00\"}}, {\"categoria\": \"Mariscos\", \"productos\": {\"Cigalas\": \"18.00\", \"Carabineros\": \"25.00\", \"Langostinos\": \"15.00\", \"Gambas blancas\": \"12.50\", \"Camarón al ajillo\": \"10.00\"}}, {\"categoria\": \"Arroces\", \"productos\": {\"Arroz con carne\": \"9.00\", \"Arroz con pollo\": \"8.50\", \"Arroz con patatas\": \"7.00\", \"Arroz con mariscos\": \"10.00\", \"Arroz con verduras\": \"7.50\"}}, {\"categoria\": \"Bebidas\", \"productos\": {\"Agua\": \"1.50\", \"Fanta\": \"2.00\", \"Sprite\": \"2.00\", \"Refresco\": \"2.00\", \"Coca-Cola\": \"2.00\"}}, {\"categoria\": \"Postres\", \"productos\": {\"Tarta de fresa\": \"3.50\", \"Tarta de queso\": \"3.50\", \"Tarta de manzana\": \"4.00\", \"Tarta de vainilla\": \"3.50\", \"Tarta de chocolate\": \"4.00\"}}, {\"categoria\": \"Helados\", \"productos\": {\"Helado de fresa\": \"2.50\", \"Helado de limón\": \"2.50\", \"Helado de manzana\": \"2.50\", \"Helado de vainilla\": \"2.50\", \"Helado de chocolate\": \"2.50\"}}, {\"categoria\": \"Cócteles\", \"productos\": {\"Mojito\": \"5.50\", \"Daiquiri\": \"5.50\", \"Margarita\": \"6.00\", \"Mint Julep\": \"6.00\", \"Cosmopolitan\": \"6.50\"}}]\"\n            \"contacto\" => \"+34648999999\"\n            \"zona_id\" => 2\n            \"user_id\" => 7\n            \"created_at\" => \"2025-04-09 18:45:19\"\n            \"updated_at\" => \"2025-04-22 16:14:37\"\n            \"contactos_secundarios\" => \"[]\"\n            \"order\" => 0\n          ]\n          #original: array:16 [\n            \"id\" => 1\n            \"nombre\" => \"Chiringuito Sabor Tropical\"\n            \"descripcion\" => \"Magnam aperiam doloribus ut tempora autem dolore suscipit. Quis ut illum doloremque vero et. Ut illum sequi ex explicabo. Ipsa qui culpa vero amet quos nihil.\"\n            \"direccion\" => \"Ronda Aguilar, 5, 17º 2º, 68143, Gaona Alta\"\n            \"ubicacion\" => \"{\"latitud\": \"37.009604\", \"longitud\": \"-6.563694\"}\"\n            \"horario\" => \"{\"lunes\": [\"09:00-18:00\"], \"jueves\": [\"09:00-18:00\"], \"martes\": [\"09:00-18:00\"], \"domingo\": [\"09:00-18:00\"], \"sábado\": [\"09:00-18:00\"], \"viernes\": [\"09:00-18:00\"], \"miércoles\": [\"09:00-18:00\"]}\"\n            \"enlaces_sociales\" => \"[{\"url\": \"https://es-es.facebook.com/cocoamatalascanas\", \"plataforma\": \"facebook\"}, {\"url\": \"https://www.instagram.com/cocoamatalascanas/\", \"plataforma\": \"instagram\"}, {\"url\": \"https://x.com/cocoamision\", \"plataforma\": \"twitter\"}]\"\n            \"enlaces_propios\" => \"{\"Pagina Web\": \"https://www.instagram.com/cocoamatalascanas/\"}\"\n            \"precios\" => \"[{\"categoria\": \"Ensaladas\", \"productos\": {\"Ensalada César\": \"6.50\", \"Ensalada Capresse\": \"7.00\", \"Ensalada de Frutas\": \"5.50\", \"Ensalada de Verduras\": \"5.00\", \"Ensalada Mediterránea\": \"8.00\"}}, {\"categoria\": \"Entrantes\", \"productos\": {\"Bruschetta\": \"4.50\", \"Pan de Jamón\": \"4.50\", \"Patatas Bravas\": \"4.00\", \"Ensaladilla Rusa\": \"3.50\", \"Croquetas de Pollo\": \"5.00\"}}, {\"categoria\": \"Fritos\", \"productos\": {\"Choco Frito\": \"6.00\", \"Pollo Frito\": \"6.50\", \"Patatas Fritas\": \"3.00\", \"Zucchini Fritas\": \"4.50\", \"Calamares Fritos\": \"7.00\"}}, {\"categoria\": \"Carnes\", \"productos\": {\"Pechuga de pollo\": \"9.50\", \"Bistec de ternera\": \"14.00\", \"Costilla de cerdo\": \"11.00\", \"Filete de ternera\": \"12.00\", \"Chuletón de cerdo\": \"13.50\"}}, {\"categoria\": \"Pescado\", \"productos\": {\"Filete de atún\": \"15.00\", \"Filete de bonito\": \"10.00\", \"Filete de dorado\": \"14.00\", \"Filete de merluza\": \"9.50\", \"Filete de lenguado\": \"12.00\"}}, {\"categoria\": \"Mariscos\", \"productos\": {\"Cigalas\": \"18.00\", \"Carabineros\": \"25.00\", \"Langostinos\": \"15.00\", \"Gambas blancas\": \"12.50\", \"Camarón al ajillo\": \"10.00\"}}, {\"categoria\": \"Arroces\", \"productos\": {\"Arroz con carne\": \"9.00\", \"Arroz con pollo\": \"8.50\", \"Arroz con patatas\": \"7.00\", \"Arroz con mariscos\": \"10.00\", \"Arroz con verduras\": \"7.50\"}}, {\"categoria\": \"Bebidas\", \"productos\": {\"Agua\": \"1.50\", \"Fanta\": \"2.00\", \"Sprite\": \"2.00\", \"Refresco\": \"2.00\", \"Coca-Cola\": \"2.00\"}}, {\"categoria\": \"Postres\", \"productos\": {\"Tarta de fresa\": \"3.50\", \"Tarta de queso\": \"3.50\", \"Tarta de manzana\": \"4.00\", \"Tarta de vainilla\": \"3.50\", \"Tarta de chocolate\": \"4.00\"}}, {\"categoria\": \"Helados\", \"productos\": {\"Helado de fresa\": \"2.50\", \"Helado de limón\": \"2.50\", \"Helado de manzana\": \"2.50\", \"Helado de vainilla\": \"2.50\", \"Helado de chocolate\": \"2.50\"}}, {\"categoria\": \"Cócteles\", \"productos\": {\"Mojito\": \"5.50\", \"Daiquiri\": \"5.50\", \"Margarita\": \"6.00\", \"Mint Julep\": \"6.00\", \"Cosmopolitan\": \"6.50\"}}]\"\n            \"contacto\" => \"+34648999999\"\n            \"zona_id\" => 2\n            \"user_id\" => 7\n            \"created_at\" => \"2025-04-09 18:45:19\"\n            \"updated_at\" => \"2025-04-22 16:14:37\"\n            \"contactos_secundarios\" => \"[]\"\n            \"order\" => 0\n          ]\n          #changes: []\n          #casts: array:6 [\n            \"ubicacion\" => \"array\"\n            \"horario\" => \"array\"\n            \"precios\" => \"array\"\n            \"enlaces_sociales\" => \"array\"\n            \"enlaces_propios\" => \"array\"\n            \"contactos_secundarios\" => \"array\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [\n            \"categorias\" => Illuminate\\Database\\Eloquent\\Collection {#2992 …2}\n            \"zona\" => App\\Models\\Zona {#2885 …30}\n            \"suscripcion\" => App\\Models\\Suscripcion {#2822 …30}\n          ]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [\n            0 => \"nombre\"\n            1 => \"descripcion\"\n            2 => \"direccion\"\n            3 => \"ubicacion\"\n            4 => \"horario\"\n            5 => \"enlaces_sociales\"\n            6 => \"enlaces_propios\"\n            7 => \"contactos_secundarios\"\n            8 => \"contacto\"\n            9 => \"zona_id\"\n            10 => \"user_id\"\n            11 => \"precios\"\n            12 => \"order\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        1 => App\\Models\\Negocio {#3013\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [\n            \"id\" => 2\n            \"nombre\" => \"Chiringuito El Cangrejo\"\n            \"descripcion\" => \"Qui hic accusantium consequuntur est et nam voluptatem. Non omnis porro ea asperiores. Et commodi non et non qui exercitationem ad. Est ut maiores porro error pariatur.\"\n            \"direccion\" => \"Praza Enrique, 76, 6º F, 74469, Los Sanz\"\n            \"ubicacion\" => \"{\"latitud\": \"37.010670\", \"longitud\": \"-6.559759\"}\"\n            \"horario\" => \"{\"lunes\": [\"09:00-18:00\"], \"jueves\": [\"09:00-18:00\"], \"martes\": [\"09:00-18:00\"], \"domingo\": [\"09:00-18:00\"], \"sábado\": [\"09:00-18:00\"], \"viernes\": [\"09:00-18:00\"], \"miércoles\": [\"09:00-18:00\"]}\"\n            \"enlaces_sociales\" => \"[{\"url\": \"https://es-es.facebook.com/cocoamatalascanas\", \"plataforma\": \"facebook\"}, {\"url\": \"https://www.instagram.com/cocoamatalascanas/\", \"plataforma\": \"instagram\"}, {\"url\": \"https://x.com/cocoamision\", \"plataforma\": \"twitter\"}]\"\n            \"enlaces_propios\" => \"{\"Pagina Web\": \"https://www.instagram.com/cocoamatalascanas/\"}\"\n            \"precios\" => \"[{\"categoria\": \"Ensaladas\", \"productos\": {\"Ensalada César\": \"6.50\", \"Ensalada Capresse\": \"7.00\", \"Ensalada de Frutas\": \"5.50\", \"Ensalada de Verduras\": \"5.00\", \"Ensalada Mediterránea\": \"8.00\"}}, {\"categoria\": \"Entrantes\", \"productos\": {\"Bruschetta\": \"4.50\", \"Pan de Jamón\": \"4.50\", \"Patatas Bravas\": \"4.00\", \"Ensaladilla Rusa\": \"3.50\", \"Croquetas de Pollo\": \"5.00\"}}, {\"categoria\": \"Fritos\", \"productos\": {\"Choco Frito\": \"6.00\", \"Pollo Frito\": \"6.50\", \"Patatas Fritas\": \"3.00\", \"Zucchini Fritas\": \"4.50\", \"Calamares Fritos\": \"7.00\"}}, {\"categoria\": \"Carnes\", \"productos\": {\"Pechuga de pollo\": \"9.50\", \"Bistec de ternera\": \"14.00\", \"Costilla de cerdo\": \"11.00\", \"Filete de ternera\": \"12.00\", \"Chuletón de cerdo\": \"13.50\"}}, {\"categoria\": \"Pescado\", \"productos\": {\"Filete de atún\": \"15.00\", \"Filete de bonito\": \"10.00\", \"Filete de dorado\": \"14.00\", \"Filete de merluza\": \"9.50\", \"Filete de lenguado\": \"12.00\"}}, {\"categoria\": \"Mariscos\", \"productos\": {\"Cigalas\": \"18.00\", \"Carabineros\": \"25.00\", \"Langostinos\": \"15.00\", \"Gambas blancas\": \"12.50\", \"Camarón al ajillo\": \"10.00\"}}, {\"categoria\": \"Arroces\", \"productos\": {\"Arroz con carne\": \"9.00\", \"Arroz con pollo\": \"8.50\", \"Arroz con patatas\": \"7.00\", \"Arroz con mariscos\": \"10.00\", \"Arroz con verduras\": \"7.50\"}}, {\"categoria\": \"Bebidas\", \"productos\": {\"Agua\": \"1.50\", \"Fanta\": \"2.00\", \"Sprite\": \"2.00\", \"Refresco\": \"2.00\", \"Coca-Cola\": \"2.00\"}}, {\"categoria\": \"Postres\", \"productos\": {\"Tarta de fresa\": \"3.50\", \"Tarta de queso\": \"3.50\", \"Tarta de manzana\": \"4.00\", \"Tarta de vainilla\": \"3.50\", \"Tarta de chocolate\": \"4.00\"}}, {\"categoria\": \"Helados\", \"productos\": {\"Helado de fresa\": \"2.50\", \"Helado de limón\": \"2.50\", \"Helado de manzana\": \"2.50\", \"Helado de vainilla\": \"2.50\", \"Helado de chocolate\": \"2.50\"}}, {\"categoria\": \"Cócteles\", \"productos\": {\"Mojito\": \"5.50\", \"Daiquiri\": \"5.50\", \"Margarita\": \"6.00\", \"Mint Julep\": \"6.00\", \"Cosmopolitan\": \"6.50\"}}]\"\n            \"contacto\" => \"+34648999999\"\n            \"zona_id\" => 1\n            \"user_id\" => 3\n            \"created_at\" => \"2025-04-09 18:45:19\"\n            \"updated_at\" => \"2025-06-14 16:58:28\"\n            \"contactos_secundarios\" => \"[]\"\n            \"order\" => 1\n          ]\n          #original: array:16 [\n            \"id\" => 2\n            \"nombre\" => \"Chiringuito El Cangrejo\"\n            \"descripcion\" => \"Qui hic accusantium consequuntur est et nam voluptatem. Non omnis porro ea asperiores. Et commodi non et non qui exercitationem ad. Est ut maiores porro error pariatur.\"\n            \"direccion\" => \"Praza Enrique, 76, 6º F, 74469, Los Sanz\"\n            \"ubicacion\" => \"{\"latitud\": \"37.010670\", \"longitud\": \"-6.559759\"}\"\n            \"horario\" => \"{\"lunes\": [\"09:00-18:00\"], \"jueves\": [\"09:00-18:00\"], \"martes\": [\"09:00-18:00\"], \"domingo\": [\"09:00-18:00\"], \"sábado\": [\"09:00-18:00\"], \"viernes\": [\"09:00-18:00\"], \"miércoles\": [\"09:00-18:00\"]}\"\n            \"enlaces_sociales\" => \"[{\"url\": \"https://es-es.facebook.com/cocoamatalascanas\", \"plataforma\": \"facebook\"}, {\"url\": \"https://www.instagram.com/cocoamatalascanas/\", \"plataforma\": \"instagram\"}, {\"url\": \"https://x.com/cocoamision\", \"plataforma\": \"twitter\"}]\"\n            \"enlaces_propios\" => \"{\"Pagina Web\": \"https://www.instagram.com/cocoamatalascanas/\"}\"\n            \"precios\" => \"[{\"categoria\": \"Ensaladas\", \"productos\": {\"Ensalada César\": \"6.50\", \"Ensalada Capresse\": \"7.00\", \"Ensalada de Frutas\": \"5.50\", \"Ensalada de Verduras\": \"5.00\", \"Ensalada Mediterránea\": \"8.00\"}}, {\"categoria\": \"Entrantes\", \"productos\": {\"Bruschetta\": \"4.50\", \"Pan de Jamón\": \"4.50\", \"Patatas Bravas\": \"4.00\", \"Ensaladilla Rusa\": \"3.50\", \"Croquetas de Pollo\": \"5.00\"}}, {\"categoria\": \"Fritos\", \"productos\": {\"Choco Frito\": \"6.00\", \"Pollo Frito\": \"6.50\", \"Patatas Fritas\": \"3.00\", \"Zucchini Fritas\": \"4.50\", \"Calamares Fritos\": \"7.00\"}}, {\"categoria\": \"Carnes\", \"productos\": {\"Pechuga de pollo\": \"9.50\", \"Bistec de ternera\": \"14.00\", \"Costilla de cerdo\": \"11.00\", \"Filete de ternera\": \"12.00\", \"Chuletón de cerdo\": \"13.50\"}}, {\"categoria\": \"Pescado\", \"productos\": {\"Filete de atún\": \"15.00\", \"Filete de bonito\": \"10.00\", \"Filete de dorado\": \"14.00\", \"Filete de merluza\": \"9.50\", \"Filete de lenguado\": \"12.00\"}}, {\"categoria\": \"Mariscos\", \"productos\": {\"Cigalas\": \"18.00\", \"Carabineros\": \"25.00\", \"Langostinos\": \"15.00\", \"Gambas blancas\": \"12.50\", \"Camarón al ajillo\": \"10.00\"}}, {\"categoria\": \"Arroces\", \"productos\": {\"Arroz con carne\": \"9.00\", \"Arroz con pollo\": \"8.50\", \"Arroz con patatas\": \"7.00\", \"Arroz con mariscos\": \"10.00\", \"Arroz con verduras\": \"7.50\"}}, {\"categoria\": \"Bebidas\", \"productos\": {\"Agua\": \"1.50\", \"Fanta\": \"2.00\", \"Sprite\": \"2.00\", \"Refresco\": \"2.00\", \"Coca-Cola\": \"2.00\"}}, {\"categoria\": \"Postres\", \"productos\": {\"Tarta de fresa\": \"3.50\", \"Tarta de queso\": \"3.50\", \"Tarta de manzana\": \"4.00\", \"Tarta de vainilla\": \"3.50\", \"Tarta de chocolate\": \"4.00\"}}, {\"categoria\": \"Helados\", \"productos\": {\"Helado de fresa\": \"2.50\", \"Helado de limón\": \"2.50\", \"Helado de manzana\": \"2.50\", \"Helado de vainilla\": \"2.50\", \"Helado de chocolate\": \"2.50\"}}, {\"categoria\": \"Cócteles\", \"productos\": {\"Mojito\": \"5.50\", \"Daiquiri\": \"5.50\", \"Margarita\": \"6.00\", \"Mint Julep\": \"6.00\", \"Cosmopolitan\": \"6.50\"}}]\"\n            \"contacto\" => \"+34648999999\"\n            \"zona_id\" => 1\n            \"user_id\" => 3\n            \"created_at\" => \"2025-04-09 18:45:19\"\n            \"updated_at\" => \"2025-06-14 16:58:28\"\n            \"contactos_secundarios\" => \"[]\"\n            \"order\" => 1\n          ]\n          #changes: []\n          #casts: array:6 [\n            \"ubicacion\" => \"array\"\n            \"horario\" => \"array\"\n            \"precios\" => \"array\"\n            \"enlaces_sociales\" => \"array\"\n            \"enlaces_propios\" => \"array\"\n            \"contactos_secundarios\" => \"array\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [\n            \"categorias\" => Illuminate\\Database\\Eloquent\\Collection {#3047 …2}\n            \"zona\" => App\\Models\\Zona {#2884 …30}\n            \"suscripcion\" => App\\Models\\Suscripcion {#2794 …30}\n          ]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [\n            0 => \"nombre\"\n            1 => \"descripcion\"\n            2 => \"direccion\"\n            3 => \"ubicacion\"\n            4 => \"horario\"\n            5 => \"enlaces_sociales\"\n            6 => \"enlaces_propios\"\n            7 => \"contactos_secundarios\"\n            8 => \"contacto\"\n            9 => \"zona_id\"\n            10 => \"user_id\"\n            11 => \"precios\"\n            12 => \"order\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        2 => App\\Models\\Negocio {#3014\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [\n            \"id\" => 3\n            \"nombre\" => \"Chiringuito Arena & Sol\"\n            \"descripcion\" => \"Possimus nobis perferendis incidunt qui ipsam. Eaque cum quos adipisci commodi sed. Blanditiis nisi reiciendis laborum laborum sunt nihil.\"\n            \"direccion\" => \"Passeig Josefa, 15, 7º B, 44549, Gonzáles del Penedès\"\n            \"ubicacion\" => \"{\"latitud\": \"37.011675\", \"longitud\": \"-6.562664\"}\"\n            \"horario\" => \"{\"lunes\": [\"09:00-18:00\"], \"jueves\": [\"09:00-18:00\"], \"martes\": [\"09:00-18:00\"], \"domingo\": [\"09:00-18:00\"], \"sábado\": [\"09:00-18:00\"], \"viernes\": [\"09:00-18:00\"], \"miércoles\": [\"09:00-18:00\"]}\"\n            \"enlaces_sociales\" => \"[{\"url\": \"https://es-es.facebook.com/cocoamatalascanas\", \"plataforma\": \"facebook\"}, {\"url\": \"https://www.instagram.com/cocoamatalascanas/\", \"plataforma\": \"instagram\"}, {\"url\": \"https://x.com/cocoamision\", \"plataforma\": \"twitter\"}]\"\n            \"enlaces_propios\" => \"{\"Pagina Web\": \"https://www.instagram.com/cocoamatalascanas/\"}\"\n            \"precios\" => \"[{\"categoria\": \"Ensaladas\", \"productos\": {\"Ensalada César\": \"6.50\", \"Ensalada Capresse\": \"7.00\", \"Ensalada de Frutas\": \"5.50\", \"Ensalada de Verduras\": \"5.00\", \"Ensalada Mediterránea\": \"8.00\"}}, {\"categoria\": \"Entrantes\", \"productos\": {\"Bruschetta\": \"4.50\", \"Pan de Jamón\": \"4.50\", \"Patatas Bravas\": \"4.00\", \"Ensaladilla Rusa\": \"3.50\", \"Croquetas de Pollo\": \"5.00\"}}, {\"categoria\": \"Fritos\", \"productos\": {\"Choco Frito\": \"6.00\", \"Pollo Frito\": \"6.50\", \"Patatas Fritas\": \"3.00\", \"Zucchini Fritas\": \"4.50\", \"Calamares Fritos\": \"7.00\"}}, {\"categoria\": \"Carnes\", \"productos\": {\"Pechuga de pollo\": \"9.50\", \"Bistec de ternera\": \"14.00\", \"Costilla de cerdo\": \"11.00\", \"Filete de ternera\": \"12.00\", \"Chuletón de cerdo\": \"13.50\"}}, {\"categoria\": \"Pescado\", \"productos\": {\"Filete de atún\": \"15.00\", \"Filete de bonito\": \"10.00\", \"Filete de dorado\": \"14.00\", \"Filete de merluza\": \"9.50\", \"Filete de lenguado\": \"12.00\"}}, {\"categoria\": \"Mariscos\", \"productos\": {\"Cigalas\": \"18.00\", \"Carabineros\": \"25.00\", \"Langostinos\": \"15.00\", \"Gambas blancas\": \"12.50\", \"Camarón al ajillo\": \"10.00\"}}, {\"categoria\": \"Arroces\", \"productos\": {\"Arroz con carne\": \"9.00\", \"Arroz con pollo\": \"8.50\", \"Arroz con patatas\": \"7.00\", \"Arroz con mariscos\": \"10.00\", \"Arroz con verduras\": \"7.50\"}}, {\"categoria\": \"Bebidas\", \"productos\": {\"Agua\": \"1.50\", \"Fanta\": \"2.00\", \"Sprite\": \"2.00\", \"Refresco\": \"2.00\", \"Coca-Cola\": \"2.00\"}}, {\"categoria\": \"Postres\", \"productos\": {\"Tarta de fresa\": \"3.50\", \"Tarta de queso\": \"3.50\", \"Tarta de manzana\": \"4.00\", \"Tarta de vainilla\": \"3.50\", \"Tarta de chocolate\": \"4.00\"}}, {\"categoria\": \"Helados\", \"productos\": {\"Helado de fresa\": \"2.50\", \"Helado de limón\": \"2.50\", \"Helado de manzana\": \"2.50\", \"Helado de vainilla\": \"2.50\", \"Helado de chocolate\": \"2.50\"}}, {\"categoria\": \"Cócteles\", \"productos\": {\"Mojito\": \"5.50\", \"Daiquiri\": \"5.50\", \"Margarita\": \"6.00\", \"Mint Julep\": \"6.00\", \"Cosmopolitan\": \"6.50\"}}]\"\n            \"contacto\" => \"+34648999999\"\n            \"zona_id\" => 2\n            \"user_id\" => 4\n            \"created_at\" => \"2025-04-09 18:45:19\"\n            \"updated_at\" => \"2025-06-14 16:58:28\"\n            \"contactos_secundarios\" => \"[]\"\n            \"order\" => 2\n          ]\n          #original: array:16 [\n            \"id\" => 3\n            \"nombre\" => \"Chiringuito Arena & Sol\"\n            \"descripcion\" => \"Possimus nobis perferendis incidunt qui ipsam. Eaque cum quos adipisci commodi sed. Blanditiis nisi reiciendis laborum laborum sunt nihil.\"\n            \"direccion\" => \"Passeig Josefa, 15, 7º B, 44549, Gonzáles del Penedès\"\n            \"ubicacion\" => \"{\"latitud\": \"37.011675\", \"longitud\": \"-6.562664\"}\"\n            \"horario\" => \"{\"lunes\": [\"09:00-18:00\"], \"jueves\": [\"09:00-18:00\"], \"martes\": [\"09:00-18:00\"], \"domingo\": [\"09:00-18:00\"], \"sábado\": [\"09:00-18:00\"], \"viernes\": [\"09:00-18:00\"], \"miércoles\": [\"09:00-18:00\"]}\"\n            \"enlaces_sociales\" => \"[{\"url\": \"https://es-es.facebook.com/cocoamatalascanas\", \"plataforma\": \"facebook\"}, {\"url\": \"https://www.instagram.com/cocoamatalascanas/\", \"plataforma\": \"instagram\"}, {\"url\": \"https://x.com/cocoamision\", \"plataforma\": \"twitter\"}]\"\n            \"enlaces_propios\" => \"{\"Pagina Web\": \"https://www.instagram.com/cocoamatalascanas/\"}\"\n            \"precios\" => \"[{\"categoria\": \"Ensaladas\", \"productos\": {\"Ensalada César\": \"6.50\", \"Ensalada Capresse\": \"7.00\", \"Ensalada de Frutas\": \"5.50\", \"Ensalada de Verduras\": \"5.00\", \"Ensalada Mediterránea\": \"8.00\"}}, {\"categoria\": \"Entrantes\", \"productos\": {\"Bruschetta\": \"4.50\", \"Pan de Jamón\": \"4.50\", \"Patatas Bravas\": \"4.00\", \"Ensaladilla Rusa\": \"3.50\", \"Croquetas de Pollo\": \"5.00\"}}, {\"categoria\": \"Fritos\", \"productos\": {\"Choco Frito\": \"6.00\", \"Pollo Frito\": \"6.50\", \"Patatas Fritas\": \"3.00\", \"Zucchini Fritas\": \"4.50\", \"Calamares Fritos\": \"7.00\"}}, {\"categoria\": \"Carnes\", \"productos\": {\"Pechuga de pollo\": \"9.50\", \"Bistec de ternera\": \"14.00\", \"Costilla de cerdo\": \"11.00\", \"Filete de ternera\": \"12.00\", \"Chuletón de cerdo\": \"13.50\"}}, {\"categoria\": \"Pescado\", \"productos\": {\"Filete de atún\": \"15.00\", \"Filete de bonito\": \"10.00\", \"Filete de dorado\": \"14.00\", \"Filete de merluza\": \"9.50\", \"Filete de lenguado\": \"12.00\"}}, {\"categoria\": \"Mariscos\", \"productos\": {\"Cigalas\": \"18.00\", \"Carabineros\": \"25.00\", \"Langostinos\": \"15.00\", \"Gambas blancas\": \"12.50\", \"Camarón al ajillo\": \"10.00\"}}, {\"categoria\": \"Arroces\", \"productos\": {\"Arroz con carne\": \"9.00\", \"Arroz con pollo\": \"8.50\", \"Arroz con patatas\": \"7.00\", \"Arroz con mariscos\": \"10.00\", \"Arroz con verduras\": \"7.50\"}}, {\"categoria\": \"Bebidas\", \"productos\": {\"Agua\": \"1.50\", \"Fanta\": \"2.00\", \"Sprite\": \"2.00\", \"Refresco\": \"2.00\", \"Coca-Cola\": \"2.00\"}}, {\"categoria\": \"Postres\", \"productos\": {\"Tarta de fresa\": \"3.50\", \"Tarta de queso\": \"3.50\", \"Tarta de manzana\": \"4.00\", \"Tarta de vainilla\": \"3.50\", \"Tarta de chocolate\": \"4.00\"}}, {\"categoria\": \"Helados\", \"productos\": {\"Helado de fresa\": \"2.50\", \"Helado de limón\": \"2.50\", \"Helado de manzana\": \"2.50\", \"Helado de vainilla\": \"2.50\", \"Helado de chocolate\": \"2.50\"}}, {\"categoria\": \"Cócteles\", \"productos\": {\"Mojito\": \"5.50\", \"Daiquiri\": \"5.50\", \"Margarita\": \"6.00\", \"Mint Julep\": \"6.00\", \"Cosmopolitan\": \"6.50\"}}]\"\n            \"contacto\" => \"+34648999999\"\n            \"zona_id\" => 2\n            \"user_id\" => 4\n            \"created_at\" => \"2025-04-09 18:45:19\"\n            \"updated_at\" => \"2025-06-14 16:58:28\"\n            \"contactos_secundarios\" => \"[]\"\n            \"order\" => 2\n          ]\n          #changes: []\n          #casts: array:6 [\n            \"ubicacion\" => \"array\"\n            \"horario\" => \"array\"\n            \"precios\" => \"array\"\n            \"enlaces_sociales\" => \"array\"\n            \"enlaces_propios\" => \"array\"\n            \"contactos_secundarios\" => \"array\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [\n            \"categorias\" => Illuminate\\Database\\Eloquent\\Collection {#3235 …2}\n            \"zona\" => App\\Models\\Zona {#2885 …30}\n            \"suscripcion\" => App\\Models\\Suscripcion {#2795 …30}\n          ]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [\n            0 => \"nombre\"\n            1 => \"descripcion\"\n            2 => \"direccion\"\n            3 => \"ubicacion\"\n            4 => \"horario\"\n            5 => \"enlaces_sociales\"\n            6 => \"enlaces_propios\"\n            7 => \"contactos_secundarios\"\n            8 => \"contacto\"\n            9 => \"zona_id\"\n            10 => \"user_id\"\n            11 => \"precios\"\n            12 => \"order\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        3 => App\\Models\\Negocio {#3015\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [\n            \"id\" => 4\n            \"nombre\" => \"La Parrilla del Faro\"\n            \"descripcion\" => \"Dignissimos quis iste voluptatem dignissimos nulla non molestias. Iusto ab deleniti omnis. Architecto veritatis tenetur vel quaerat ut quaerat.\"\n            \"direccion\" => \"Carrer Pastor, 85, Entre suelo 8º, 28201, Navas del Barco\"\n            \"ubicacion\" => \"{\"latitud\": \"36.985082\", \"longitud\": \"-6.530340\"}\"\n            \"horario\" => \"{\"lunes\": [\"09:00-18:00\"], \"jueves\": [\"09:00-18:00\"], \"martes\": [\"09:00-18:00\"], \"domingo\": [\"09:00-18:00\"], \"sábado\": [\"09:00-18:00\"], \"viernes\": [\"09:00-18:00\"], \"miércoles\": [\"09:00-18:00\"]}\"\n            \"enlaces_sociales\" => \"[{\"url\": \"https://es-es.facebook.com/cocoamatalascanas\", \"plataforma\": \"facebook\"}, {\"url\": \"https://www.instagram.com/cocoamatalascanas/\", \"plataforma\": \"instagram\"}, {\"url\": \"https://x.com/cocoamision\", \"plataforma\": \"twitter\"}]\"\n            \"enlaces_propios\" => \"{\"Pagina Web\": \"https://www.instagram.com/cocoamatalascanas/\"}\"\n            \"precios\" => \"[{\"categoria\": \"Ensaladas\", \"productos\": {\"Ensalada César\": \"6.50\", \"Ensalada Capresse\": \"7.00\", \"Ensalada de Frutas\": \"5.50\", \"Ensalada de Verduras\": \"5.00\", \"Ensalada Mediterránea\": \"8.00\"}}, {\"categoria\": \"Entrantes\", \"productos\": {\"Bruschetta\": \"4.50\", \"Pan de Jamón\": \"4.50\", \"Patatas Bravas\": \"4.00\", \"Ensaladilla Rusa\": \"3.50\", \"Croquetas de Pollo\": \"5.00\"}}, {\"categoria\": \"Fritos\", \"productos\": {\"Choco Frito\": \"6.00\", \"Pollo Frito\": \"6.50\", \"Patatas Fritas\": \"3.00\", \"Zucchini Fritas\": \"4.50\", \"Calamares Fritos\": \"7.00\"}}, {\"categoria\": \"Carnes\", \"productos\": {\"Pechuga de pollo\": \"9.50\", \"Bistec de ternera\": \"14.00\", \"Costilla de cerdo\": \"11.00\", \"Filete de ternera\": \"12.00\", \"Chuletón de cerdo\": \"13.50\"}}, {\"categoria\": \"Pescado\", \"productos\": {\"Filete de atún\": \"15.00\", \"Filete de bonito\": \"10.00\", \"Filete de dorado\": \"14.00\", \"Filete de merluza\": \"9.50\", \"Filete de lenguado\": \"12.00\"}}, {\"categoria\": \"Mariscos\", \"productos\": {\"Cigalas\": \"18.00\", \"Carabineros\": \"25.00\", \"Langostinos\": \"15.00\", \"Gambas blancas\": \"12.50\", \"Camarón al ajillo\": \"10.00\"}}, {\"categoria\": \"Arroces\", \"productos\": {\"Arroz con carne\": \"9.00\", \"Arroz con pollo\": \"8.50\", \"Arroz con patatas\": \"7.00\", \"Arroz con mariscos\": \"10.00\", \"Arroz con verduras\": \"7.50\"}}, {\"categoria\": \"Bebidas\", \"productos\": {\"Agua\": \"1.50\", \"Fanta\": \"2.00\", \"Sprite\": \"2.00\", \"Refresco\": \"2.00\", \"Coca-Cola\": \"2.00\"}}, {\"categoria\": \"Postres\", \"productos\": {\"Tarta de fresa\": \"3.50\", \"Tarta de queso\": \"3.50\", \"Tarta de manzana\": \"4.00\", \"Tarta de vainilla\": \"3.50\", \"Tarta de chocolate\": \"4.00\"}}, {\"categoria\": \"Helados\", \"productos\": {\"Helado de fresa\": \"2.50\", \"Helado de limón\": \"2.50\", \"Helado de manzana\": \"2.50\", \"Helado de vainilla\": \"2.50\", \"Helado de chocolate\": \"2.50\"}}]\"\n            \"contacto\" => \"+34648999999\"\n            \"zona_id\" => 2\n            \"user_id\" => 3\n            \"created_at\" => \"2025-04-09 18:45:30\"\n            \"updated_at\" => \"2025-06-14 16:58:28\"\n            \"contactos_secundarios\" => \"[]\"\n            \"order\" => 3\n          ]\n          #original: array:16 [\n            \"id\" => 4\n            \"nombre\" => \"La Parrilla del Faro\"\n            \"descripcion\" => \"Dignissimos quis iste voluptatem dignissimos nulla non molestias. Iusto ab deleniti omnis. Architecto veritatis tenetur vel quaerat ut quaerat.\"\n            \"direccion\" => \"Carrer Pastor, 85, Entre suelo 8º, 28201, Navas del Barco\"\n            \"ubicacion\" => \"{\"latitud\": \"36.985082\", \"longitud\": \"-6.530340\"}\"\n            \"horario\" => \"{\"lunes\": [\"09:00-18:00\"], \"jueves\": [\"09:00-18:00\"], \"martes\": [\"09:00-18:00\"], \"domingo\": [\"09:00-18:00\"], \"sábado\": [\"09:00-18:00\"], \"viernes\": [\"09:00-18:00\"], \"miércoles\": [\"09:00-18:00\"]}\"\n            \"enlaces_sociales\" => \"[{\"url\": \"https://es-es.facebook.com/cocoamatalascanas\", \"plataforma\": \"facebook\"}, {\"url\": \"https://www.instagram.com/cocoamatalascanas/\", \"plataforma\": \"instagram\"}, {\"url\": \"https://x.com/cocoamision\", \"plataforma\": \"twitter\"}]\"\n            \"enlaces_propios\" => \"{\"Pagina Web\": \"https://www.instagram.com/cocoamatalascanas/\"}\"\n            \"precios\" => \"[{\"categoria\": \"Ensaladas\", \"productos\": {\"Ensalada César\": \"6.50\", \"Ensalada Capresse\": \"7.00\", \"Ensalada de Frutas\": \"5.50\", \"Ensalada de Verduras\": \"5.00\", \"Ensalada Mediterránea\": \"8.00\"}}, {\"categoria\": \"Entrantes\", \"productos\": {\"Bruschetta\": \"4.50\", \"Pan de Jamón\": \"4.50\", \"Patatas Bravas\": \"4.00\", \"Ensaladilla Rusa\": \"3.50\", \"Croquetas de Pollo\": \"5.00\"}}, {\"categoria\": \"Fritos\", \"productos\": {\"Choco Frito\": \"6.00\", \"Pollo Frito\": \"6.50\", \"Patatas Fritas\": \"3.00\", \"Zucchini Fritas\": \"4.50\", \"Calamares Fritos\": \"7.00\"}}, {\"categoria\": \"Carnes\", \"productos\": {\"Pechuga de pollo\": \"9.50\", \"Bistec de ternera\": \"14.00\", \"Costilla de cerdo\": \"11.00\", \"Filete de ternera\": \"12.00\", \"Chuletón de cerdo\": \"13.50\"}}, {\"categoria\": \"Pescado\", \"productos\": {\"Filete de atún\": \"15.00\", \"Filete de bonito\": \"10.00\", \"Filete de dorado\": \"14.00\", \"Filete de merluza\": \"9.50\", \"Filete de lenguado\": \"12.00\"}}, {\"categoria\": \"Mariscos\", \"productos\": {\"Cigalas\": \"18.00\", \"Carabineros\": \"25.00\", \"Langostinos\": \"15.00\", \"Gambas blancas\": \"12.50\", \"Camarón al ajillo\": \"10.00\"}}, {\"categoria\": \"Arroces\", \"productos\": {\"Arroz con carne\": \"9.00\", \"Arroz con pollo\": \"8.50\", \"Arroz con patatas\": \"7.00\", \"Arroz con mariscos\": \"10.00\", \"Arroz con verduras\": \"7.50\"}}, {\"categoria\": \"Bebidas\", \"productos\": {\"Agua\": \"1.50\", \"Fanta\": \"2.00\", \"Sprite\": \"2.00\", \"Refresco\": \"2.00\", \"Coca-Cola\": \"2.00\"}}, {\"categoria\": \"Postres\", \"productos\": {\"Tarta de fresa\": \"3.50\", \"Tarta de queso\": \"3.50\", \"Tarta de manzana\": \"4.00\", \"Tarta de vainilla\": \"3.50\", \"Tarta de chocolate\": \"4.00\"}}, {\"categoria\": \"Helados\", \"productos\": {\"Helado de fresa\": \"2.50\", \"Helado de limón\": \"2.50\", \"Helado de manzana\": \"2.50\", \"Helado de vainilla\": \"2.50\", \"Helado de chocolate\": \"2.50\"}}]\"\n            \"contacto\" => \"+34648999999\"\n            \"zona_id\" => 2\n            \"user_id\" => 3\n            \"created_at\" => \"2025-04-09 18:45:30\"\n            \"updated_at\" => \"2025-06-14 16:58:28\"\n            \"contactos_secundarios\" => \"[]\"\n            \"order\" => 3\n          ]\n          #changes: []\n          #casts: array:6 [\n            \"ubicacion\" => \"array\"\n            \"horario\" => \"array\"\n            \"precios\" => \"array\"\n            \"enlaces_sociales\" => \"array\"\n            \"enlaces_propios\" => \"array\"\n            \"contactos_secundarios\" => \"array\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [\n            \"categorias\" => Illuminate\\Database\\Eloquent\\Collection {#3203 …2}\n            \"zona\" => App\\Models\\Zona {#2885 …30}\n            \"suscripcion\" => App\\Models\\Suscripcion {#2796 …30}\n          ]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [\n            0 => \"nombre\"\n            1 => \"descripcion\"\n            2 => \"direccion\"\n            3 => \"ubicacion\"\n            4 => \"horario\"\n            5 => \"enlaces_sociales\"\n            6 => \"enlaces_propios\"\n            7 => \"contactos_secundarios\"\n            8 => \"contacto\"\n            9 => \"zona_id\"\n            10 => \"user_id\"\n            11 => \"precios\"\n            12 => \"order\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        4 => App\\Models\\Negocio {#3016\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [\n            \"id\" => 5\n            \"nombre\" => \"Casa de la Abuela\"\n            \"descripcion\" => \"Quisquam delectus vero nesciunt itaque ea commodi. Architecto eligendi debitis ipsum in itaque. Nulla aperiam fuga ad modi repellendus. Et rerum et necessitatibus.\"\n            \"direccion\" => \"Passeig Guajardo, 954, Bajos, 73218, La Santamaría\"\n            \"ubicacion\" => \"{\"latitud\": \"36.990715\", \"longitud\": \"-6.530449\"}\"\n            \"horario\" => \"{\"lunes\": [\"09:00-18:00\"], \"jueves\": [\"09:00-18:00\"], \"martes\": [\"09:00-18:00\"], \"domingo\": [\"09:00-18:00\"], \"sábado\": [\"09:00-18:00\"], \"viernes\": [\"09:00-18:00\"], \"miércoles\": [\"09:00-18:00\"]}\"\n            \"enlaces_sociales\" => \"[{\"url\": \"https://es-es.facebook.com/cocoamatalascanas\", \"plataforma\": \"facebook\"}, {\"url\": \"https://www.instagram.com/cocoamatalascanas/\", \"plataforma\": \"instagram\"}, {\"url\": \"https://x.com/cocoamision\", \"plataforma\": \"twitter\"}]\"\n            \"enlaces_propios\" => \"{\"Pagina Web\": \"https://www.instagram.com/cocoamatalascanas/\"}\"\n            \"precios\" => \"[{\"categoria\": \"Ensaladas\", \"productos\": {\"Ensalada César\": \"6.50\", \"Ensalada Capresse\": \"7.00\", \"Ensalada de Frutas\": \"5.50\", \"Ensalada de Verduras\": \"5.00\", \"Ensalada Mediterránea\": \"8.00\"}}, {\"categoria\": \"Entrantes\", \"productos\": {\"Bruschetta\": \"4.50\", \"Pan de Jamón\": \"4.50\", \"Patatas Bravas\": \"4.00\", \"Ensaladilla Rusa\": \"3.50\", \"Croquetas de Pollo\": \"5.00\"}}, {\"categoria\": \"Fritos\", \"productos\": {\"Choco Frito\": \"6.00\", \"Pollo Frito\": \"6.50\", \"Patatas Fritas\": \"3.00\", \"Zucchini Fritas\": \"4.50\", \"Calamares Fritos\": \"7.00\"}}, {\"categoria\": \"Carnes\", \"productos\": {\"Pechuga de pollo\": \"9.50\", \"Bistec de ternera\": \"14.00\", \"Costilla de cerdo\": \"11.00\", \"Filete de ternera\": \"12.00\", \"Chuletón de cerdo\": \"13.50\"}}, {\"categoria\": \"Pescado\", \"productos\": {\"Filete de atún\": \"15.00\", \"Filete de bonito\": \"10.00\", \"Filete de dorado\": \"14.00\", \"Filete de merluza\": \"9.50\", \"Filete de lenguado\": \"12.00\"}}, {\"categoria\": \"Mariscos\", \"productos\": {\"Cigalas\": \"18.00\", \"Carabineros\": \"25.00\", \"Langostinos\": \"15.00\", \"Gambas blancas\": \"12.50\", \"Camarón al ajillo\": \"10.00\"}}, {\"categoria\": \"Arroces\", \"productos\": {\"Arroz con carne\": \"9.00\", \"Arroz con pollo\": \"8.50\", \"Arroz con patatas\": \"7.00\", \"Arroz con mariscos\": \"10.00\", \"Arroz con verduras\": \"7.50\"}}, {\"categoria\": \"Bebidas\", \"productos\": {\"Agua\": \"1.50\", \"Fanta\": \"2.00\", \"Sprite\": \"2.00\", \"Refresco\": \"2.00\", \"Coca-Cola\": \"2.00\"}}, {\"categoria\": \"Postres\", \"productos\": {\"Tarta de fresa\": \"3.50\", \"Tarta de queso\": \"3.50\", \"Tarta de manzana\": \"4.00\", \"Tarta de vainilla\": \"3.50\", \"Tarta de chocolate\": \"4.00\"}}, {\"categoria\": \"Helados\", \"productos\": {\"Helado de fresa\": \"2.50\", \"Helado de limón\": \"2.50\", \"Helado de manzana\": \"2.50\", \"Helado de vainilla\": \"2.50\", \"Helado de chocolate\": \"2.50\"}}]\"\n            \"contacto\" => \"+34648999999\"\n            \"zona_id\" => 2\n            \"user_id\" => 6\n            \"created_at\" => \"2025-04-09 18:45:30\"\n            \"updated_at\" => \"2025-06-14 16:58:28\"\n            \"contactos_secundarios\" => \"[]\"\n            \"order\" => 4\n          ]\n          #original: array:16 [\n            \"id\" => 5\n            \"nombre\" => \"Casa de la Abuela\"\n            \"descripcion\" => \"Quisquam delectus vero nesciunt itaque ea commodi. Architecto eligendi debitis ipsum in itaque. Nulla aperiam fuga ad modi repellendus. Et rerum et necessitatibus.\"\n            \"direccion\" => \"Passeig Guajardo, 954, Bajos, 73218, La Santamaría\"\n            \"ubicacion\" => \"{\"latitud\": \"36.990715\", \"longitud\": \"-6.530449\"}\"\n            \"horario\" => \"{\"lunes\": [\"09:00-18:00\"], \"jueves\": [\"09:00-18:00\"], \"martes\": [\"09:00-18:00\"], \"domingo\": [\"09:00-18:00\"], \"sábado\": [\"09:00-18:00\"], \"viernes\": [\"09:00-18:00\"], \"miércoles\": [\"09:00-18:00\"]}\"\n            \"enlaces_sociales\" => \"[{\"url\": \"https://es-es.facebook.com/cocoamatalascanas\", \"plataforma\": \"facebook\"}, {\"url\": \"https://www.instagram.com/cocoamatalascanas/\", \"plataforma\": \"instagram\"}, {\"url\": \"https://x.com/cocoamision\", \"plataforma\": \"twitter\"}]\"\n            \"enlaces_propios\" => \"{\"Pagina Web\": \"https://www.instagram.com/cocoamatalascanas/\"}\"\n            \"precios\" => \"[{\"categoria\": \"Ensaladas\", \"productos\": {\"Ensalada César\": \"6.50\", \"Ensalada Capresse\": \"7.00\", \"Ensalada de Frutas\": \"5.50\", \"Ensalada de Verduras\": \"5.00\", \"Ensalada Mediterránea\": \"8.00\"}}, {\"categoria\": \"Entrantes\", \"productos\": {\"Bruschetta\": \"4.50\", \"Pan de Jamón\": \"4.50\", \"Patatas Bravas\": \"4.00\", \"Ensaladilla Rusa\": \"3.50\", \"Croquetas de Pollo\": \"5.00\"}}, {\"categoria\": \"Fritos\", \"productos\": {\"Choco Frito\": \"6.00\", \"Pollo Frito\": \"6.50\", \"Patatas Fritas\": \"3.00\", \"Zucchini Fritas\": \"4.50\", \"Calamares Fritos\": \"7.00\"}}, {\"categoria\": \"Carnes\", \"productos\": {\"Pechuga de pollo\": \"9.50\", \"Bistec de ternera\": \"14.00\", \"Costilla de cerdo\": \"11.00\", \"Filete de ternera\": \"12.00\", \"Chuletón de cerdo\": \"13.50\"}}, {\"categoria\": \"Pescado\", \"productos\": {\"Filete de atún\": \"15.00\", \"Filete de bonito\": \"10.00\", \"Filete de dorado\": \"14.00\", \"Filete de merluza\": \"9.50\", \"Filete de lenguado\": \"12.00\"}}, {\"categoria\": \"Mariscos\", \"productos\": {\"Cigalas\": \"18.00\", \"Carabineros\": \"25.00\", \"Langostinos\": \"15.00\", \"Gambas blancas\": \"12.50\", \"Camarón al ajillo\": \"10.00\"}}, {\"categoria\": \"Arroces\", \"productos\": {\"Arroz con carne\": \"9.00\", \"Arroz con pollo\": \"8.50\", \"Arroz con patatas\": \"7.00\", \"Arroz con mariscos\": \"10.00\", \"Arroz con verduras\": \"7.50\"}}, {\"categoria\": \"Bebidas\", \"productos\": {\"Agua\": \"1.50\", \"Fanta\": \"2.00\", \"Sprite\": \"2.00\", \"Refresco\": \"2.00\", \"Coca-Cola\": \"2.00\"}}, {\"categoria\": \"Postres\", \"productos\": {\"Tarta de fresa\": \"3.50\", \"Tarta de queso\": \"3.50\", \"Tarta de manzana\": \"4.00\", \"Tarta de vainilla\": \"3.50\", \"Tarta de chocolate\": \"4.00\"}}, {\"categoria\": \"Helados\", \"productos\": {\"Helado de fresa\": \"2.50\", \"Helado de limón\": \"2.50\", \"Helado de manzana\": \"2.50\", \"Helado de vainilla\": \"2.50\", \"Helado de chocolate\": \"2.50\"}}]\"\n            \"contacto\" => \"+34648999999\"\n            \"zona_id\" => 2\n            \"user_id\" => 6\n            \"created_at\" => \"2025-04-09 18:45:30\"\n            \"updated_at\" => \"2025-06-14 16:58:28\"\n            \"contactos_secundarios\" => \"[]\"\n            \"order\" => 4\n          ]\n          #changes: []\n          #casts: array:6 [\n            \"ubicacion\" => \"array\"\n            \"horario\" => \"array\"\n            \"precios\" => \"array\"\n            \"enlaces_sociales\" => \"array\"\n            \"enlaces_propios\" => \"array\"\n            \"contactos_secundarios\" => \"array\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [\n            \"categorias\" => Illuminate\\Database\\Eloquent\\Collection {#3202 …2}\n            \"zona\" => App\\Models\\Zona {#2885 …30}\n            \"suscripcion\" => App\\Models\\Suscripcion {#2797 …30}\n          ]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [\n            0 => \"nombre\"\n            1 => \"descripcion\"\n            2 => \"direccion\"\n            3 => \"ubicacion\"\n            4 => \"horario\"\n            5 => \"enlaces_sociales\"\n            6 => \"enlaces_propios\"\n            7 => \"contactos_secundarios\"\n            8 => \"contacto\"\n            9 => \"zona_id\"\n            10 => \"user_id\"\n            11 => \"precios\"\n            12 => \"order\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        5 => App\\Models\\Negocio {#3017\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [\n            \"id\" => 6\n            \"nombre\" => \"Marisquería del Puerto\"\n            \"descripcion\" => \"Aut amet est repellendus qui officiis. Molestiae vel quibusdam atque odio.\"\n            \"direccion\" => \"Ruela Corona, 32, 3º A, 75210, La Carbonell del Pozo\"\n            \"ubicacion\" => \"{\"latitud\": \"37.004410\", \"longitud\": \"-6.550451\"}\"\n            \"horario\" => \"{\"lunes\": [\"09:00-18:00\"], \"jueves\": [\"09:00-18:00\"], \"martes\": [\"09:00-18:00\"], \"domingo\": [\"09:00-18:00\"], \"sábado\": [\"09:00-18:00\"], \"viernes\": [\"09:00-18:00\"], \"miércoles\": [\"09:00-18:00\"]}\"\n            \"enlaces_sociales\" => \"[{\"url\": \"https://es-es.facebook.com/cocoamatalascanas\", \"plataforma\": \"facebook\"}, {\"url\": \"https://www.instagram.com/cocoamatalascanas/\", \"plataforma\": \"instagram\"}, {\"url\": \"https://x.com/cocoamision\", \"plataforma\": \"twitter\"}]\"\n            \"enlaces_propios\" => \"{\"Pagina Web\": \"https://www.instagram.com/cocoamatalascanas/\"}\"\n            \"precios\" => \"[{\"categoria\": \"Ensaladas\", \"productos\": {\"Ensalada César\": \"6.50\", \"Ensalada Capresse\": \"7.00\", \"Ensalada de Frutas\": \"5.50\", \"Ensalada de Verduras\": \"5.00\", \"Ensalada Mediterránea\": \"8.00\"}}, {\"categoria\": \"Entrantes\", \"productos\": {\"Bruschetta\": \"4.50\", \"Pan de Jamón\": \"4.50\", \"Patatas Bravas\": \"4.00\", \"Ensaladilla Rusa\": \"3.50\", \"Croquetas de Pollo\": \"5.00\"}}, {\"categoria\": \"Fritos\", \"productos\": {\"Choco Frito\": \"6.00\", \"Pollo Frito\": \"6.50\", \"Patatas Fritas\": \"3.00\", \"Zucchini Fritas\": \"4.50\", \"Calamares Fritos\": \"7.00\"}}, {\"categoria\": \"Carnes\", \"productos\": {\"Pechuga de pollo\": \"9.50\", \"Bistec de ternera\": \"14.00\", \"Costilla de cerdo\": \"11.00\", \"Filete de ternera\": \"12.00\", \"Chuletón de cerdo\": \"13.50\"}}, {\"categoria\": \"Pescado\", \"productos\": {\"Filete de atún\": \"15.00\", \"Filete de bonito\": \"10.00\", \"Filete de dorado\": \"14.00\", \"Filete de merluza\": \"9.50\", \"Filete de lenguado\": \"12.00\"}}, {\"categoria\": \"Mariscos\", \"productos\": {\"Cigalas\": \"18.00\", \"Carabineros\": \"25.00\", \"Langostinos\": \"15.00\", \"Gambas blancas\": \"12.50\", \"Camarón al ajillo\": \"10.00\"}}, {\"categoria\": \"Arroces\", \"productos\": {\"Arroz con carne\": \"9.00\", \"Arroz con pollo\": \"8.50\", \"Arroz con patatas\": \"7.00\", \"Arroz con mariscos\": \"10.00\", \"Arroz con verduras\": \"7.50\"}}, {\"categoria\": \"Bebidas\", \"productos\": {\"Agua\": \"1.50\", \"Fanta\": \"2.00\", \"Sprite\": \"2.00\", \"Refresco\": \"2.00\", \"Coca-Cola\": \"2.00\"}}, {\"categoria\": \"Postres\", \"productos\": {\"Tarta de fresa\": \"3.50\", \"Tarta de queso\": \"3.50\", \"Tarta de manzana\": \"4.00\", \"Tarta de vainilla\": \"3.50\", \"Tarta de chocolate\": \"4.00\"}}, {\"categoria\": \"Helados\", \"productos\": {\"Helado de fresa\": \"2.50\", \"Helado de limón\": \"2.50\", \"Helado de manzana\": \"2.50\", \"Helado de vainilla\": \"2.50\", \"Helado de chocolate\": \"2.50\"}}]\"\n            \"contacto\" => \"+34648999999\"\n            \"zona_id\" => 2\n            \"user_id\" => 7\n            \"created_at\" => \"2025-04-09 18:45:30\"\n            \"updated_at\" => \"2025-06-14 16:58:28\"\n            \"contactos_secundarios\" => \"[]\"\n            \"order\" => 5\n          ]\n          #original: array:16 [\n            \"id\" => 6\n            \"nombre\" => \"Marisquería del Puerto\"\n            \"descripcion\" => \"Aut amet est repellendus qui officiis. Molestiae vel quibusdam atque odio.\"\n            \"direccion\" => \"Ruela Corona, 32, 3º A, 75210, La Carbonell del Pozo\"\n            \"ubicacion\" => \"{\"latitud\": \"37.004410\", \"longitud\": \"-6.550451\"}\"\n            \"horario\" => \"{\"lunes\": [\"09:00-18:00\"], \"jueves\": [\"09:00-18:00\"], \"martes\": [\"09:00-18:00\"], \"domingo\": [\"09:00-18:00\"], \"sábado\": [\"09:00-18:00\"], \"viernes\": [\"09:00-18:00\"], \"miércoles\": [\"09:00-18:00\"]}\"\n            \"enlaces_sociales\" => \"[{\"url\": \"https://es-es.facebook.com/cocoamatalascanas\", \"plataforma\": \"facebook\"}, {\"url\": \"https://www.instagram.com/cocoamatalascanas/\", \"plataforma\": \"instagram\"}, {\"url\": \"https://x.com/cocoamision\", \"plataforma\": \"twitter\"}]\"\n            \"enlaces_propios\" => \"{\"Pagina Web\": \"https://www.instagram.com/cocoamatalascanas/\"}\"\n            \"precios\" => \"[{\"categoria\": \"Ensaladas\", \"productos\": {\"Ensalada César\": \"6.50\", \"Ensalada Capresse\": \"7.00\", \"Ensalada de Frutas\": \"5.50\", \"Ensalada de Verduras\": \"5.00\", \"Ensalada Mediterránea\": \"8.00\"}}, {\"categoria\": \"Entrantes\", \"productos\": {\"Bruschetta\": \"4.50\", \"Pan de Jamón\": \"4.50\", \"Patatas Bravas\": \"4.00\", \"Ensaladilla Rusa\": \"3.50\", \"Croquetas de Pollo\": \"5.00\"}}, {\"categoria\": \"Fritos\", \"productos\": {\"Choco Frito\": \"6.00\", \"Pollo Frito\": \"6.50\", \"Patatas Fritas\": \"3.00\", \"Zucchini Fritas\": \"4.50\", \"Calamares Fritos\": \"7.00\"}}, {\"categoria\": \"Carnes\", \"productos\": {\"Pechuga de pollo\": \"9.50\", \"Bistec de ternera\": \"14.00\", \"Costilla de cerdo\": \"11.00\", \"Filete de ternera\": \"12.00\", \"Chuletón de cerdo\": \"13.50\"}}, {\"categoria\": \"Pescado\", \"productos\": {\"Filete de atún\": \"15.00\", \"Filete de bonito\": \"10.00\", \"Filete de dorado\": \"14.00\", \"Filete de merluza\": \"9.50\", \"Filete de lenguado\": \"12.00\"}}, {\"categoria\": \"Mariscos\", \"productos\": {\"Cigalas\": \"18.00\", \"Carabineros\": \"25.00\", \"Langostinos\": \"15.00\", \"Gambas blancas\": \"12.50\", \"Camarón al ajillo\": \"10.00\"}}, {\"categoria\": \"Arroces\", \"productos\": {\"Arroz con carne\": \"9.00\", \"Arroz con pollo\": \"8.50\", \"Arroz con patatas\": \"7.00\", \"Arroz con mariscos\": \"10.00\", \"Arroz con verduras\": \"7.50\"}}, {\"categoria\": \"Bebidas\", \"productos\": {\"Agua\": \"1.50\", \"Fanta\": \"2.00\", \"Sprite\": \"2.00\", \"Refresco\": \"2.00\", \"Coca-Cola\": \"2.00\"}}, {\"categoria\": \"Postres\", \"productos\": {\"Tarta de fresa\": \"3.50\", \"Tarta de queso\": \"3.50\", \"Tarta de manzana\": \"4.00\", \"Tarta de vainilla\": \"3.50\", \"Tarta de chocolate\": \"4.00\"}}, {\"categoria\": \"Helados\", \"productos\": {\"Helado de fresa\": \"2.50\", \"Helado de limón\": \"2.50\", \"Helado de manzana\": \"2.50\", \"Helado de vainilla\": \"2.50\", \"Helado de chocolate\": \"2.50\"}}]\"\n            \"contacto\" => \"+34648999999\"\n            \"zona_id\" => 2\n            \"user_id\" => 7\n            \"created_at\" => \"2025-04-09 18:45:30\"\n            \"updated_at\" => \"2025-06-14 16:58:28\"\n            \"contactos_secundarios\" => \"[]\"\n            \"order\" => 5\n          ]\n          #changes: []\n          #casts: array:6 [\n            \"ubicacion\" => \"array\"\n            \"horario\" => \"array\"\n            \"precios\" => \"array\"\n            \"enlaces_sociales\" => \"array\"\n            \"enlaces_propios\" => \"array\"\n            \"contactos_secundarios\" => \"array\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [\n            \"categorias\" => Illuminate\\Database\\Eloquent\\Collection {#3234 …2}\n            \"zona\" => App\\Models\\Zona {#2885 …30}\n            \"suscripcion\" => App\\Models\\Suscripcion {#2798 …30}\n          ]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [\n            0 => \"nombre\"\n            1 => \"descripcion\"\n            2 => \"direccion\"\n            3 => \"ubicacion\"\n            4 => \"horario\"\n            5 => \"enlaces_sociales\"\n            6 => \"enlaces_propios\"\n            7 => \"contactos_secundarios\"\n            8 => \"contacto\"\n            9 => \"zona_id\"\n            10 => \"user_id\"\n            11 => \"precios\"\n            12 => \"order\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        6 => App\\Models\\Negocio {#3018\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [\n            \"id\" => 7\n            \"nombre\" => \"Bar de Tapas Mar Azul\"\n            \"descripcion\" => \"Porro consequatur qui cum ratione. Molestias blanditiis fugit aut quo impedit enim. Eius ab beatae earum veniam earum iste.\"\n            \"direccion\" => \"Camiño Castañeda, 17, Bajo 2º, 60712, Os Lerma del Barco\"\n            \"ubicacion\" => \"{\"latitud\": \"36.999523\", \"longitud\": \"-6.538771\"}\"\n            \"horario\" => \"{\"lunes\": [\"09:00-18:00\"], \"jueves\": [\"09:00-18:00\"], \"martes\": [\"09:00-18:00\"], \"domingo\": [\"09:00-18:00\"], \"sábado\": [\"09:00-18:00\"], \"viernes\": [\"09:00-18:00\"], \"miércoles\": [\"09:00-18:00\"]}\"\n            \"enlaces_sociales\" => \"[{\"url\": \"https://es-es.facebook.com/cocoamatalascanas\", \"plataforma\": \"facebook\"}, {\"url\": \"https://www.instagram.com/cocoamatalascanas/\", \"plataforma\": \"instagram\"}, {\"url\": \"https://x.com/cocoamision\", \"plataforma\": \"twitter\"}]\"\n            \"enlaces_propios\" => \"{\"Pagina Web\": \"https://www.instagram.com/cocoamatalascanas/\"}\"\n            \"precios\" => \"[{\"categoria\": \"Fritos\", \"productos\": {\"Choco Frito\": \"6.00\", \"Pollo Frito\": \"6.50\", \"Patatas Fritas\": \"3.00\", \"Zucchini Fritas\": \"4.50\", \"Calamares Fritos\": \"7.00\"}}, {\"categoria\": \"Montaditos\", \"productos\": {\"Montadito de Lomo\": \"2.50\", \"Montadito de Queso\": \"2.00\", \"Montadito de Jamón\": \"2.50\", \"Montadito de Pringá\": \"2.50\", \"Montadito de Tortilla\": \"2.00\"}}, {\"categoria\": \"Raciones\", \"productos\": {\"Ración de Carne\": \"9.50\", \"Ración de Queso\": \"8.00\", \"Ración de Jamón\": \"10.00\", \"Ración de Patatas\": \"5.50\", \"Ración de Calamares\": \"9.00\"}}, {\"categoria\": \"Ensaladas\", \"productos\": {\"Ensalada César\": \"6.50\", \"Ensalada Capresse\": \"7.00\", \"Ensalada de Frutas\": \"5.50\", \"Ensalada de Verduras\": \"5.00\", \"Ensalada Mediterránea\": \"8.00\"}}, {\"categoria\": \"Bebidas\", \"productos\": {\"Agua\": \"1.50\", \"Fanta\": \"2.00\", \"Sprite\": \"2.00\", \"Refresco\": \"2.00\", \"Coca-Cola\": \"2.00\"}}]\"\n            \"contacto\" => \"+34648999999\"\n            \"zona_id\" => 2\n            \"user_id\" => 6\n            \"created_at\" => \"2025-04-09 18:45:44\"\n            \"updated_at\" => \"2025-06-14 16:58:28\"\n            \"contactos_secundarios\" => \"[]\"\n            \"order\" => 6\n          ]\n          #original: array:16 [\n            \"id\" => 7\n            \"nombre\" => \"Bar de Tapas Mar Azul\"\n            \"descripcion\" => \"Porro consequatur qui cum ratione. Molestias blanditiis fugit aut quo impedit enim. Eius ab beatae earum veniam earum iste.\"\n            \"direccion\" => \"Camiño Castañeda, 17, Bajo 2º, 60712, Os Lerma del Barco\"\n            \"ubicacion\" => \"{\"latitud\": \"36.999523\", \"longitud\": \"-6.538771\"}\"\n            \"horario\" => \"{\"lunes\": [\"09:00-18:00\"], \"jueves\": [\"09:00-18:00\"], \"martes\": [\"09:00-18:00\"], \"domingo\": [\"09:00-18:00\"], \"sábado\": [\"09:00-18:00\"], \"viernes\": [\"09:00-18:00\"], \"miércoles\": [\"09:00-18:00\"]}\"\n            \"enlaces_sociales\" => \"[{\"url\": \"https://es-es.facebook.com/cocoamatalascanas\", \"plataforma\": \"facebook\"}, {\"url\": \"https://www.instagram.com/cocoamatalascanas/\", \"plataforma\": \"instagram\"}, {\"url\": \"https://x.com/cocoamision\", \"plataforma\": \"twitter\"}]\"\n            \"enlaces_propios\" => \"{\"Pagina Web\": \"https://www.instagram.com/cocoamatalascanas/\"}\"\n            \"precios\" => \"[{\"categoria\": \"Fritos\", \"productos\": {\"Choco Frito\": \"6.00\", \"Pollo Frito\": \"6.50\", \"Patatas Fritas\": \"3.00\", \"Zucchini Fritas\": \"4.50\", \"Calamares Fritos\": \"7.00\"}}, {\"categoria\": \"Montaditos\", \"productos\": {\"Montadito de Lomo\": \"2.50\", \"Montadito de Queso\": \"2.00\", \"Montadito de Jamón\": \"2.50\", \"Montadito de Pringá\": \"2.50\", \"Montadito de Tortilla\": \"2.00\"}}, {\"categoria\": \"Raciones\", \"productos\": {\"Ración de Carne\": \"9.50\", \"Ración de Queso\": \"8.00\", \"Ración de Jamón\": \"10.00\", \"Ración de Patatas\": \"5.50\", \"Ración de Calamares\": \"9.00\"}}, {\"categoria\": \"Ensaladas\", \"productos\": {\"Ensalada César\": \"6.50\", \"Ensalada Capresse\": \"7.00\", \"Ensalada de Frutas\": \"5.50\", \"Ensalada de Verduras\": \"5.00\", \"Ensalada Mediterránea\": \"8.00\"}}, {\"categoria\": \"Bebidas\", \"productos\": {\"Agua\": \"1.50\", \"Fanta\": \"2.00\", \"Sprite\": \"2.00\", \"Refresco\": \"2.00\", \"Coca-Cola\": \"2.00\"}}]\"\n            \"contacto\" => \"+34648999999\"\n            \"zona_id\" => 2\n            \"user_id\" => 6\n            \"created_at\" => \"2025-04-09 18:45:44\"\n            \"updated_at\" => \"2025-06-14 16:58:28\"\n            \"contactos_secundarios\" => \"[]\"\n            \"order\" => 6\n          ]\n          #changes: []\n          #casts: array:6 [\n            \"ubicacion\" => \"array\"\n            \"horario\" => \"array\"\n            \"precios\" => \"array\"\n            \"enlaces_sociales\" => \"array\"\n            \"enlaces_propios\" => \"array\"\n            \"contactos_secundarios\" => \"array\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [\n            \"categorias\" => Illuminate\\Database\\Eloquent\\Collection {#3236 …2}\n            \"zona\" => App\\Models\\Zona {#2885 …30}\n            \"suscripcion\" => App\\Models\\Suscripcion {#2799 …30}\n          ]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [\n            0 => \"nombre\"\n            1 => \"descripcion\"\n            2 => \"direccion\"\n            3 => \"ubicacion\"\n            4 => \"horario\"\n            5 => \"enlaces_sociales\"\n            6 => \"enlaces_propios\"\n            7 => \"contactos_secundarios\"\n            8 => \"contacto\"\n            9 => \"zona_id\"\n            10 => \"user_id\"\n            11 => \"precios\"\n            12 => \"order\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        7 => App\\Models\\Negocio {#3019\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [\n            \"id\" => 8\n            \"nombre\" => \"Tapas & Más\"\n            \"descripcion\" => \"Voluptatem delectus aut qui et provident. Veritatis saepe rem quibusdam quae sunt. Illum totam harum sit exercitationem placeat aut.\"\n            \"direccion\" => \"Camino Valeria, 6, 6º E, 70576, Villalobos Baja\"\n            \"ubicacion\" => \"{\"latitud\": \"36.999479\", \"longitud\": \"-6.549389\"}\"\n            \"horario\" => \"{\"lunes\": [\"09:00-18:00\"], \"jueves\": [\"09:00-18:00\"], \"martes\": [\"09:00-18:00\"], \"domingo\": [\"09:00-18:00\"], \"sábado\": [\"09:00-18:00\"], \"viernes\": [\"09:00-18:00\"], \"miércoles\": [\"09:00-18:00\"]}\"\n            \"enlaces_sociales\" => \"[{\"url\": \"https://es-es.facebook.com/cocoamatalascanas\", \"plataforma\": \"facebook\"}, {\"url\": \"https://www.instagram.com/cocoamatalascanas/\", \"plataforma\": \"instagram\"}, {\"url\": \"https://x.com/cocoamision\", \"plataforma\": \"twitter\"}]\"\n            \"enlaces_propios\" => \"{\"Pagina Web\": \"https://www.instagram.com/cocoamatalascanas/\"}\"\n            \"precios\" => \"[{\"categoria\": \"Fritos\", \"productos\": {\"Choco Frito\": \"6.00\", \"Pollo Frito\": \"6.50\", \"Patatas Fritas\": \"3.00\", \"Zucchini Fritas\": \"4.50\", \"Calamares Fritos\": \"7.00\"}}, {\"categoria\": \"Montaditos\", \"productos\": {\"Montadito de Lomo\": \"2.50\", \"Montadito de Queso\": \"2.00\", \"Montadito de Jamón\": \"2.50\", \"Montadito de Pringá\": \"2.50\", \"Montadito de Tortilla\": \"2.00\"}}, {\"categoria\": \"Raciones\", \"productos\": {\"Ración de Carne\": \"9.50\", \"Ración de Queso\": \"8.00\", \"Ración de Jamón\": \"10.00\", \"Ración de Patatas\": \"5.50\", \"Ración de Calamares\": \"9.00\"}}, {\"categoria\": \"Ensaladas\", \"productos\": {\"Ensalada César\": \"6.50\", \"Ensalada Capresse\": \"7.00\", \"Ensalada de Frutas\": \"5.50\", \"Ensalada de Verduras\": \"5.00\", \"Ensalada Mediterránea\": \"8.00\"}}, {\"categoria\": \"Bebidas\", \"productos\": {\"Agua\": \"1.50\", \"Fanta\": \"2.00\", \"Sprite\": \"2.00\", \"Refresco\": \"2.00\", \"Coca-Cola\": \"2.00\"}}]\"\n            \"contacto\" => \"+34648999999\"\n            \"zona_id\" => 2\n            \"user_id\" => 5\n            \"created_at\" => \"2025-04-09 18:45:44\"\n            \"updated_at\" => \"2025-06-14 16:58:28\"\n            \"contactos_secundarios\" => \"[]\"\n            \"order\" => 7\n          ]\n          #original: array:16 [\n            \"id\" => 8\n            \"nombre\" => \"Tapas & Más\"\n            \"descripcion\" => \"Voluptatem delectus aut qui et provident. Veritatis saepe rem quibusdam quae sunt. Illum totam harum sit exercitationem placeat aut.\"\n            \"direccion\" => \"Camino Valeria, 6, 6º E, 70576, Villalobos Baja\"\n            \"ubicacion\" => \"{\"latitud\": \"36.999479\", \"longitud\": \"-6.549389\"}\"\n            \"horario\" => \"{\"lunes\": [\"09:00-18:00\"], \"jueves\": [\"09:00-18:00\"], \"martes\": [\"09:00-18:00\"], \"domingo\": [\"09:00-18:00\"], \"sábado\": [\"09:00-18:00\"], \"viernes\": [\"09:00-18:00\"], \"miércoles\": [\"09:00-18:00\"]}\"\n            \"enlaces_sociales\" => \"[{\"url\": \"https://es-es.facebook.com/cocoamatalascanas\", \"plataforma\": \"facebook\"}, {\"url\": \"https://www.instagram.com/cocoamatalascanas/\", \"plataforma\": \"instagram\"}, {\"url\": \"https://x.com/cocoamision\", \"plataforma\": \"twitter\"}]\"\n            \"enlaces_propios\" => \"{\"Pagina Web\": \"https://www.instagram.com/cocoamatalascanas/\"}\"\n            \"precios\" => \"[{\"categoria\": \"Fritos\", \"productos\": {\"Choco Frito\": \"6.00\", \"Pollo Frito\": \"6.50\", \"Patatas Fritas\": \"3.00\", \"Zucchini Fritas\": \"4.50\", \"Calamares Fritos\": \"7.00\"}}, {\"categoria\": \"Montaditos\", \"productos\": {\"Montadito de Lomo\": \"2.50\", \"Montadito de Queso\": \"2.00\", \"Montadito de Jamón\": \"2.50\", \"Montadito de Pringá\": \"2.50\", \"Montadito de Tortilla\": \"2.00\"}}, {\"categoria\": \"Raciones\", \"productos\": {\"Ración de Carne\": \"9.50\", \"Ración de Queso\": \"8.00\", \"Ración de Jamón\": \"10.00\", \"Ración de Patatas\": \"5.50\", \"Ración de Calamares\": \"9.00\"}}, {\"categoria\": \"Ensaladas\", \"productos\": {\"Ensalada César\": \"6.50\", \"Ensalada Capresse\": \"7.00\", \"Ensalada de Frutas\": \"5.50\", \"Ensalada de Verduras\": \"5.00\", \"Ensalada Mediterránea\": \"8.00\"}}, {\"categoria\": \"Bebidas\", \"productos\": {\"Agua\": \"1.50\", \"Fanta\": \"2.00\", \"Sprite\": \"2.00\", \"Refresco\": \"2.00\", \"Coca-Cola\": \"2.00\"}}]\"\n            \"contacto\" => \"+34648999999\"\n            \"zona_id\" => 2\n            \"user_id\" => 5\n            \"created_at\" => \"2025-04-09 18:45:44\"\n            \"updated_at\" => \"2025-06-14 16:58:28\"\n            \"contactos_secundarios\" => \"[]\"\n            \"order\" => 7\n          ]\n          #changes: []\n          #casts: array:6 [\n            \"ubicacion\" => \"array\"\n            \"horario\" => \"array\"\n            \"precios\" => \"array\"\n            \"enlaces_sociales\" => \"array\"\n            \"enlaces_propios\" => \"array\"\n            \"contactos_secundarios\" => \"array\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [\n            \"categorias\" => Illuminate\\Database\\Eloquent\\Collection {#3237 …2}\n            \"zona\" => App\\Models\\Zona {#2885 …30}\n            \"suscripcion\" => App\\Models\\Suscripcion {#2800 …30}\n          ]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [\n            0 => \"nombre\"\n            1 => \"descripcion\"\n            2 => \"direccion\"\n            3 => \"ubicacion\"\n            4 => \"horario\"\n            5 => \"enlaces_sociales\"\n            6 => \"enlaces_propios\"\n            7 => \"contactos_secundarios\"\n            8 => \"contacto\"\n            9 => \"zona_id\"\n            10 => \"user_id\"\n            11 => \"precios\"\n            12 => \"order\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        8 => App\\Models\\Negocio {#3020\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [\n            \"id\" => 9\n            \"nombre\" => \"El Rincón de las Tapas\"\n            \"descripcion\" => \"Recusandae illum et ex enim necessitatibus. Vero nisi modi dolores vitae quas esse. Rem officia est amet sunt soluta.\"\n            \"direccion\" => \"Rúa Malak, 3, 95º E, 72340, San Puga de Arriba\"\n            \"ubicacion\" => \"{\"latitud\": \"36.995297\", \"longitud\": \"-6.537181\"}\"\n            \"horario\" => \"{\"lunes\": [\"09:00-18:00\"], \"jueves\": [\"09:00-18:00\"], \"martes\": [\"09:00-18:00\"], \"domingo\": [\"09:00-18:00\"], \"sábado\": [\"09:00-18:00\"], \"viernes\": [\"09:00-18:00\"], \"miércoles\": [\"09:00-18:00\"]}\"\n            \"enlaces_sociales\" => \"[{\"url\": \"https://es-es.facebook.com/cocoamatalascanas\", \"plataforma\": \"facebook\"}, {\"url\": \"https://www.instagram.com/cocoamatalascanas/\", \"plataforma\": \"instagram\"}, {\"url\": \"https://x.com/cocoamision\", \"plataforma\": \"twitter\"}]\"\n            \"enlaces_propios\" => \"{\"Pagina Web\": \"https://www.instagram.com/cocoamatalascanas/\"}\"\n            \"precios\" => \"[{\"categoria\": \"Fritos\", \"productos\": {\"Choco Frito\": \"6.00\", \"Pollo Frito\": \"6.50\", \"Patatas Fritas\": \"3.00\", \"Zucchini Fritas\": \"4.50\", \"Calamares Fritos\": \"7.00\"}}, {\"categoria\": \"Montaditos\", \"productos\": {\"Montadito de Lomo\": \"2.50\", \"Montadito de Queso\": \"2.00\", \"Montadito de Jamón\": \"2.50\", \"Montadito de Pringá\": \"2.50\", \"Montadito de Tortilla\": \"2.00\"}}, {\"categoria\": \"Raciones\", \"productos\": {\"Ración de Carne\": \"9.50\", \"Ración de Queso\": \"8.00\", \"Ración de Jamón\": \"10.00\", \"Ración de Patatas\": \"5.50\", \"Ración de Calamares\": \"9.00\"}}, {\"categoria\": \"Ensaladas\", \"productos\": {\"Ensalada César\": \"6.50\", \"Ensalada Capresse\": \"7.00\", \"Ensalada de Frutas\": \"5.50\", \"Ensalada de Verduras\": \"5.00\", \"Ensalada Mediterránea\": \"8.00\"}}, {\"categoria\": \"Bebidas\", \"productos\": {\"Agua\": \"1.50\", \"Fanta\": \"2.00\", \"Sprite\": \"2.00\", \"Refresco\": \"2.00\", \"Coca-Cola\": \"2.00\"}}]\"\n            \"contacto\" => \"+34648999999\"\n            \"zona_id\" => 1\n            \"user_id\" => 6\n            \"created_at\" => \"2025-04-09 18:45:44\"\n            \"updated_at\" => \"2025-06-14 16:58:28\"\n            \"contactos_secundarios\" => \"[]\"\n            \"order\" => 8\n          ]\n          #original: array:16 [\n            \"id\" => 9\n            \"nombre\" => \"El Rincón de las Tapas\"\n            \"descripcion\" => \"Recusandae illum et ex enim necessitatibus. Vero nisi modi dolores vitae quas esse. Rem officia est amet sunt soluta.\"\n            \"direccion\" => \"Rúa Malak, 3, 95º E, 72340, San Puga de Arriba\"\n            \"ubicacion\" => \"{\"latitud\": \"36.995297\", \"longitud\": \"-6.537181\"}\"\n            \"horario\" => \"{\"lunes\": [\"09:00-18:00\"], \"jueves\": [\"09:00-18:00\"], \"martes\": [\"09:00-18:00\"], \"domingo\": [\"09:00-18:00\"], \"sábado\": [\"09:00-18:00\"], \"viernes\": [\"09:00-18:00\"], \"miércoles\": [\"09:00-18:00\"]}\"\n            \"enlaces_sociales\" => \"[{\"url\": \"https://es-es.facebook.com/cocoamatalascanas\", \"plataforma\": \"facebook\"}, {\"url\": \"https://www.instagram.com/cocoamatalascanas/\", \"plataforma\": \"instagram\"}, {\"url\": \"https://x.com/cocoamision\", \"plataforma\": \"twitter\"}]\"\n            \"enlaces_propios\" => \"{\"Pagina Web\": \"https://www.instagram.com/cocoamatalascanas/\"}\"\n            \"precios\" => \"[{\"categoria\": \"Fritos\", \"productos\": {\"Choco Frito\": \"6.00\", \"Pollo Frito\": \"6.50\", \"Patatas Fritas\": \"3.00\", \"Zucchini Fritas\": \"4.50\", \"Calamares Fritos\": \"7.00\"}}, {\"categoria\": \"Montaditos\", \"productos\": {\"Montadito de Lomo\": \"2.50\", \"Montadito de Queso\": \"2.00\", \"Montadito de Jamón\": \"2.50\", \"Montadito de Pringá\": \"2.50\", \"Montadito de Tortilla\": \"2.00\"}}, {\"categoria\": \"Raciones\", \"productos\": {\"Ración de Carne\": \"9.50\", \"Ración de Queso\": \"8.00\", \"Ración de Jamón\": \"10.00\", \"Ración de Patatas\": \"5.50\", \"Ración de Calamares\": \"9.00\"}}, {\"categoria\": \"Ensaladas\", \"productos\": {\"Ensalada César\": \"6.50\", \"Ensalada Capresse\": \"7.00\", \"Ensalada de Frutas\": \"5.50\", \"Ensalada de Verduras\": \"5.00\", \"Ensalada Mediterránea\": \"8.00\"}}, {\"categoria\": \"Bebidas\", \"productos\": {\"Agua\": \"1.50\", \"Fanta\": \"2.00\", \"Sprite\": \"2.00\", \"Refresco\": \"2.00\", \"Coca-Cola\": \"2.00\"}}]\"\n            \"contacto\" => \"+34648999999\"\n            \"zona_id\" => 1\n            \"user_id\" => 6\n            \"created_at\" => \"2025-04-09 18:45:44\"\n            \"updated_at\" => \"2025-06-14 16:58:28\"\n            \"contactos_secundarios\" => \"[]\"\n            \"order\" => 8\n          ]\n          #changes: []\n          #casts: array:6 [\n            \"ubicacion\" => \"array\"\n            \"horario\" => \"array\"\n            \"precios\" => \"array\"\n            \"enlaces_sociales\" => \"array\"\n            \"enlaces_propios\" => \"array\"\n            \"contactos_secundarios\" => \"array\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [\n            \"categorias\" => Illuminate\\Database\\Eloquent\\Collection {#3238 …2}\n            \"zona\" => App\\Models\\Zona {#2884 …30}\n            \"suscripcion\" => App\\Models\\Suscripcion {#2801 …30}\n          ]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [\n            0 => \"nombre\"\n            1 => \"descripcion\"\n            2 => \"direccion\"\n            3 => \"ubicacion\"\n            4 => \"horario\"\n            5 => \"enlaces_sociales\"\n            6 => \"enlaces_propios\"\n            7 => \"contactos_secundarios\"\n            8 => \"contacto\"\n            9 => \"zona_id\"\n            10 => \"user_id\"\n            11 => \"precios\"\n            12 => \"order\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        9 => App\\Models\\Negocio {#3021\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [\n            \"id\" => 10\n            \"nombre\" => \"Heladería Delicias Frías\"\n            \"descripcion\" => \"Ea et et error. Omnis aspernatur placeat nobis sed.\"\n            \"direccion\" => \"Plaça Loya, 223, 97º F, 31557, L' Arteaga del Puerto\"\n            \"ubicacion\" => \"{\"latitud\": \"36.986461\", \"longitud\": \"-6.527904\"}\"\n            \"horario\" => \"{\"lunes\": [\"09:00-18:00\"], \"jueves\": [\"09:00-18:00\"], \"martes\": [\"09:00-18:00\"], \"domingo\": [\"09:00-18:00\"], \"sábado\": [\"09:00-18:00\"], \"viernes\": [\"09:00-18:00\"], \"miércoles\": [\"09:00-18:00\"]}\"\n            \"enlaces_sociales\" => \"[{\"url\": \"https://es-es.facebook.com/cocoamatalascanas\", \"plataforma\": \"facebook\"}, {\"url\": \"https://www.instagram.com/cocoamatalascanas/\", \"plataforma\": \"instagram\"}, {\"url\": \"https://x.com/cocoamision\", \"plataforma\": \"twitter\"}]\"\n            \"enlaces_propios\" => \"{\"Pagina Web\": \"https://www.instagram.com/cocoamatalascanas/\"}\"\n            \"precios\" => \"[{\"categoria\": \"Cafés\", \"productos\": {\"Café Solo\": \"1.20\", \"Cappuccino\": \"2.00\", \"Café Americano\": \"1.30\", \"Café con Leche\": \"1.50\", \"Latte Macchiato\": \"2.20\"}}, {\"categoria\": \"Helados\", \"productos\": {\"Helado de fresa\": \"2.50\", \"Helado de limón\": \"2.50\", \"Helado de manzana\": \"2.50\", \"Helado de vainilla\": \"2.50\", \"Helado de chocolate\": \"2.50\"}}, {\"categoria\": \"Bebidas\", \"productos\": {\"Agua\": \"1.50\", \"Fanta\": \"2.00\", \"Sprite\": \"2.00\", \"Refresco\": \"2.00\", \"Coca-Cola\": \"2.00\"}}, {\"categoria\": \"Postres\", \"productos\": {\"Tarta de fresa\": \"3.50\", \"Tarta de queso\": \"3.50\", \"Tarta de manzana\": \"4.00\", \"Tarta de vainilla\": \"3.50\", \"Tarta de chocolate\": \"4.00\"}}, {\"categoria\": \"Desayunos\", \"productos\": {\"Croissant\": \"1.50\", \"Zumo Natural\": \"2.50\", \"Desayuno Completo\": \"4.50\", \"Tostada con Aceite\": \"1.80\", \"Tostada con Tomate\": \"2.00\"}}, {\"categoria\": \"Meriendas\", \"productos\": {\"Batido Natural\": \"3.20\", \"Té con Pastas\": \"3.00\", \"Sándwich Mixto\": \"3.00\", \"Chocolate con Churros\": \"3.50\", \"Tostada con Mermelada\": \"2.00\"}}]\"\n            \"contacto\" => \"+34648999999\"\n            \"zona_id\" => 1\n            \"user_id\" => 3\n            \"created_at\" => \"2025-04-09 18:45:54\"\n            \"updated_at\" => \"2025-06-14 16:58:28\"\n            \"contactos_secundarios\" => \"[]\"\n            \"order\" => 9\n          ]\n          #original: array:16 [\n            \"id\" => 10\n            \"nombre\" => \"Heladería Delicias Frías\"\n            \"descripcion\" => \"Ea et et error. Omnis aspernatur placeat nobis sed.\"\n            \"direccion\" => \"Plaça Loya, 223, 97º F, 31557, L' Arteaga del Puerto\"\n            \"ubicacion\" => \"{\"latitud\": \"36.986461\", \"longitud\": \"-6.527904\"}\"\n            \"horario\" => \"{\"lunes\": [\"09:00-18:00\"], \"jueves\": [\"09:00-18:00\"], \"martes\": [\"09:00-18:00\"], \"domingo\": [\"09:00-18:00\"], \"sábado\": [\"09:00-18:00\"], \"viernes\": [\"09:00-18:00\"], \"miércoles\": [\"09:00-18:00\"]}\"\n            \"enlaces_sociales\" => \"[{\"url\": \"https://es-es.facebook.com/cocoamatalascanas\", \"plataforma\": \"facebook\"}, {\"url\": \"https://www.instagram.com/cocoamatalascanas/\", \"plataforma\": \"instagram\"}, {\"url\": \"https://x.com/cocoamision\", \"plataforma\": \"twitter\"}]\"\n            \"enlaces_propios\" => \"{\"Pagina Web\": \"https://www.instagram.com/cocoamatalascanas/\"}\"\n            \"precios\" => \"[{\"categoria\": \"Cafés\", \"productos\": {\"Café Solo\": \"1.20\", \"Cappuccino\": \"2.00\", \"Café Americano\": \"1.30\", \"Café con Leche\": \"1.50\", \"Latte Macchiato\": \"2.20\"}}, {\"categoria\": \"Helados\", \"productos\": {\"Helado de fresa\": \"2.50\", \"Helado de limón\": \"2.50\", \"Helado de manzana\": \"2.50\", \"Helado de vainilla\": \"2.50\", \"Helado de chocolate\": \"2.50\"}}, {\"categoria\": \"Bebidas\", \"productos\": {\"Agua\": \"1.50\", \"Fanta\": \"2.00\", \"Sprite\": \"2.00\", \"Refresco\": \"2.00\", \"Coca-Cola\": \"2.00\"}}, {\"categoria\": \"Postres\", \"productos\": {\"Tarta de fresa\": \"3.50\", \"Tarta de queso\": \"3.50\", \"Tarta de manzana\": \"4.00\", \"Tarta de vainilla\": \"3.50\", \"Tarta de chocolate\": \"4.00\"}}, {\"categoria\": \"Desayunos\", \"productos\": {\"Croissant\": \"1.50\", \"Zumo Natural\": \"2.50\", \"Desayuno Completo\": \"4.50\", \"Tostada con Aceite\": \"1.80\", \"Tostada con Tomate\": \"2.00\"}}, {\"categoria\": \"Meriendas\", \"productos\": {\"Batido Natural\": \"3.20\", \"Té con Pastas\": \"3.00\", \"Sándwich Mixto\": \"3.00\", \"Chocolate con Churros\": \"3.50\", \"Tostada con Mermelada\": \"2.00\"}}]\"\n            \"contacto\" => \"+34648999999\"\n            \"zona_id\" => 1\n            \"user_id\" => 3\n            \"created_at\" => \"2025-04-09 18:45:54\"\n            \"updated_at\" => \"2025-06-14 16:58:28\"\n            \"contactos_secundarios\" => \"[]\"\n            \"order\" => 9\n          ]\n          #changes: []\n          #casts: array:6 [\n            \"ubicacion\" => \"array\"\n            \"horario\" => \"array\"\n            \"precios\" => \"array\"\n            \"enlaces_sociales\" => \"array\"\n            \"enlaces_propios\" => \"array\"\n            \"contactos_secundarios\" => \"array\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [\n            \"categorias\" => Illuminate\\Database\\Eloquent\\Collection {#3239 …2}\n            \"zona\" => App\\Models\\Zona {#2884 …30}\n            \"suscripcion\" => App\\Models\\Suscripcion {#2802 …30}\n          ]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [\n            0 => \"nombre\"\n            1 => \"descripcion\"\n            2 => \"direccion\"\n            3 => \"ubicacion\"\n            4 => \"horario\"\n            5 => \"enlaces_sociales\"\n            6 => \"enlaces_propios\"\n            7 => \"contactos_secundarios\"\n            8 => \"contacto\"\n            9 => \"zona_id\"\n            10 => \"user_id\"\n            11 => \"precios\"\n            12 => \"order\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        10 => App\\Models\\Negocio {#3022\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [\n            \"id\" => 11\n            \"nombre\" => \"Cafetería Tarde de Sol\"\n            \"descripcion\" => \"Porro iste culpa odit ipsam enim qui et earum. Consequatur autem libero et et omnis. Optio expedita et ducimus sed saepe.\"\n            \"direccion\" => \"Rúa Rojas, 5, 6º D, 04206, As Oquendo\"\n            \"ubicacion\" => \"{\"latitud\": \"37.003775\", \"longitud\": \"-6.554554\"}\"\n            \"horario\" => \"{\"lunes\": [\"09:00-18:00\"], \"jueves\": [\"09:00-18:00\"], \"martes\": [\"09:00-18:00\"], \"domingo\": [\"09:00-18:00\"], \"sábado\": [\"09:00-18:00\"], \"viernes\": [\"09:00-18:00\"], \"miércoles\": [\"09:00-18:00\"]}\"\n            \"enlaces_sociales\" => \"[{\"url\": \"https://es-es.facebook.com/cocoamatalascanas\", \"plataforma\": \"facebook\"}, {\"url\": \"https://www.instagram.com/cocoamatalascanas/\", \"plataforma\": \"instagram\"}, {\"url\": \"https://x.com/cocoamision\", \"plataforma\": \"twitter\"}]\"\n            \"enlaces_propios\" => \"{\"Pagina Web\": \"https://www.instagram.com/cocoamatalascanas/\"}\"\n            \"precios\" => \"[{\"categoria\": \"Cafés\", \"productos\": {\"Café Solo\": \"1.20\", \"Cappuccino\": \"2.00\", \"Café Americano\": \"1.30\", \"Café con Leche\": \"1.50\", \"Latte Macchiato\": \"2.20\"}}, {\"categoria\": \"Helados\", \"productos\": {\"Helado de fresa\": \"2.50\", \"Helado de limón\": \"2.50\", \"Helado de manzana\": \"2.50\", \"Helado de vainilla\": \"2.50\", \"Helado de chocolate\": \"2.50\"}}, {\"categoria\": \"Bebidas\", \"productos\": {\"Agua\": \"1.50\", \"Fanta\": \"2.00\", \"Sprite\": \"2.00\", \"Refresco\": \"2.00\", \"Coca-Cola\": \"2.00\"}}, {\"categoria\": \"Postres\", \"productos\": {\"Tarta de fresa\": \"3.50\", \"Tarta de queso\": \"3.50\", \"Tarta de manzana\": \"4.00\", \"Tarta de vainilla\": \"3.50\", \"Tarta de chocolate\": \"4.00\"}}, {\"categoria\": \"Desayunos\", \"productos\": {\"Croissant\": \"1.50\", \"Zumo Natural\": \"2.50\", \"Desayuno Completo\": \"4.50\", \"Tostada con Aceite\": \"1.80\", \"Tostada con Tomate\": \"2.00\"}}, {\"categoria\": \"Meriendas\", \"productos\": {\"Batido Natural\": \"3.20\", \"Té con Pastas\": \"3.00\", \"Sándwich Mixto\": \"3.00\", \"Chocolate con Churros\": \"3.50\", \"Tostada con Mermelada\": \"2.00\"}}]\"\n            \"contacto\" => \"+34648999999\"\n            \"zona_id\" => 2\n            \"user_id\" => 5\n            \"created_at\" => \"2025-04-09 18:45:54\"\n            \"updated_at\" => \"2025-06-14 16:58:28\"\n            \"contactos_secundarios\" => \"[]\"\n            \"order\" => 10\n          ]\n          #original: array:16 [\n            \"id\" => 11\n            \"nombre\" => \"Cafetería Tarde de Sol\"\n            \"descripcion\" => \"Porro iste culpa odit ipsam enim qui et earum. Consequatur autem libero et et omnis. Optio expedita et ducimus sed saepe.\"\n            \"direccion\" => \"Rúa Rojas, 5, 6º D, 04206, As Oquendo\"\n            \"ubicacion\" => \"{\"latitud\": \"37.003775\", \"longitud\": \"-6.554554\"}\"\n            \"horario\" => \"{\"lunes\": [\"09:00-18:00\"], \"jueves\": [\"09:00-18:00\"], \"martes\": [\"09:00-18:00\"], \"domingo\": [\"09:00-18:00\"], \"sábado\": [\"09:00-18:00\"], \"viernes\": [\"09:00-18:00\"], \"miércoles\": [\"09:00-18:00\"]}\"\n            \"enlaces_sociales\" => \"[{\"url\": \"https://es-es.facebook.com/cocoamatalascanas\", \"plataforma\": \"facebook\"}, {\"url\": \"https://www.instagram.com/cocoamatalascanas/\", \"plataforma\": \"instagram\"}, {\"url\": \"https://x.com/cocoamision\", \"plataforma\": \"twitter\"}]\"\n            \"enlaces_propios\" => \"{\"Pagina Web\": \"https://www.instagram.com/cocoamatalascanas/\"}\"\n            \"precios\" => \"[{\"categoria\": \"Cafés\", \"productos\": {\"Café Solo\": \"1.20\", \"Cappuccino\": \"2.00\", \"Café Americano\": \"1.30\", \"Café con Leche\": \"1.50\", \"Latte Macchiato\": \"2.20\"}}, {\"categoria\": \"Helados\", \"productos\": {\"Helado de fresa\": \"2.50\", \"Helado de limón\": \"2.50\", \"Helado de manzana\": \"2.50\", \"Helado de vainilla\": \"2.50\", \"Helado de chocolate\": \"2.50\"}}, {\"categoria\": \"Bebidas\", \"productos\": {\"Agua\": \"1.50\", \"Fanta\": \"2.00\", \"Sprite\": \"2.00\", \"Refresco\": \"2.00\", \"Coca-Cola\": \"2.00\"}}, {\"categoria\": \"Postres\", \"productos\": {\"Tarta de fresa\": \"3.50\", \"Tarta de queso\": \"3.50\", \"Tarta de manzana\": \"4.00\", \"Tarta de vainilla\": \"3.50\", \"Tarta de chocolate\": \"4.00\"}}, {\"categoria\": \"Desayunos\", \"productos\": {\"Croissant\": \"1.50\", \"Zumo Natural\": \"2.50\", \"Desayuno Completo\": \"4.50\", \"Tostada con Aceite\": \"1.80\", \"Tostada con Tomate\": \"2.00\"}}, {\"categoria\": \"Meriendas\", \"productos\": {\"Batido Natural\": \"3.20\", \"Té con Pastas\": \"3.00\", \"Sándwich Mixto\": \"3.00\", \"Chocolate con Churros\": \"3.50\", \"Tostada con Mermelada\": \"2.00\"}}]\"\n            \"contacto\" => \"+34648999999\"\n            \"zona_id\" => 2\n            \"user_id\" => 5\n            \"created_at\" => \"2025-04-09 18:45:54\"\n            \"updated_at\" => \"2025-06-14 16:58:28\"\n            \"contactos_secundarios\" => \"[]\"\n            \"order\" => 10\n          ]\n          #changes: []\n          #casts: array:6 [\n            \"ubicacion\" => \"array\"\n            \"horario\" => \"array\"\n            \"precios\" => \"array\"\n            \"enlaces_sociales\" => \"array\"\n            \"enlaces_propios\" => \"array\"\n            \"contactos_secundarios\" => \"array\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [\n            \"categorias\" => Illuminate\\Database\\Eloquent\\Collection {#3219 …2}\n            \"zona\" => App\\Models\\Zona {#2885 …30}\n            \"suscripcion\" => App\\Models\\Suscripcion {#2803 …30}\n          ]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [\n            0 => \"nombre\"\n            1 => \"descripcion\"\n            2 => \"direccion\"\n            3 => \"ubicacion\"\n            4 => \"horario\"\n            5 => \"enlaces_sociales\"\n            6 => \"enlaces_propios\"\n            7 => \"contactos_secundarios\"\n            8 => \"contacto\"\n            9 => \"zona_id\"\n            10 => \"user_id\"\n            11 => \"precios\"\n            12 => \"order\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        11 => App\\Models\\Negocio {#3131\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [\n            \"id\" => 12\n            \"nombre\" => \"Heladería Dulce Nieve\"\n            \"descripcion\" => \"Autem numquam rerum sit illo quisquam. In iure expedita ratione soluta atque vero porro. Doloremque fuga corrupti quis molestias labore impedit est qui. Quidem voluptate officiis culpa voluptatem ut aut adipisci.\"\n            \"direccion\" => \"Paseo Jimínez, 637, 0º A, 85792, O Guzmán\"\n            \"ubicacion\" => \"{\"latitud\": \"37.007537\", \"longitud\": \"-6.554835\"}\"\n            \"horario\" => \"{\"lunes\": [\"09:00-18:00\"], \"jueves\": [\"09:00-18:00\"], \"martes\": [\"09:00-18:00\"], \"domingo\": [\"09:00-18:00\"], \"sábado\": [\"09:00-18:00\"], \"viernes\": [\"09:00-18:00\"], \"miércoles\": [\"09:00-18:00\"]}\"\n            \"enlaces_sociales\" => \"[{\"url\": \"https://es-es.facebook.com/cocoamatalascanas\", \"plataforma\": \"facebook\"}, {\"url\": \"https://www.instagram.com/cocoamatalascanas/\", \"plataforma\": \"instagram\"}, {\"url\": \"https://x.com/cocoamision\", \"plataforma\": \"twitter\"}]\"\n            \"enlaces_propios\" => \"{\"Pagina Web\": \"https://www.instagram.com/cocoamatalascanas/\"}\"\n            \"precios\" => \"[{\"categoria\": \"Cafés\", \"productos\": {\"Café Solo\": \"1.20\", \"Cappuccino\": \"2.00\", \"Café Americano\": \"1.30\", \"Café con Leche\": \"1.50\", \"Latte Macchiato\": \"2.20\"}}, {\"categoria\": \"Helados\", \"productos\": {\"Helado de fresa\": \"2.50\", \"Helado de limón\": \"2.50\", \"Helado de manzana\": \"2.50\", \"Helado de vainilla\": \"2.50\", \"Helado de chocolate\": \"2.50\"}}, {\"categoria\": \"Bebidas\", \"productos\": {\"Agua\": \"1.50\", \"Fanta\": \"2.00\", \"Sprite\": \"2.00\", \"Refresco\": \"2.00\", \"Coca-Cola\": \"2.00\"}}, {\"categoria\": \"Postres\", \"productos\": {\"Tarta de fresa\": \"3.50\", \"Tarta de queso\": \"3.50\", \"Tarta de manzana\": \"4.00\", \"Tarta de vainilla\": \"3.50\", \"Tarta de chocolate\": \"4.00\"}}, {\"categoria\": \"Desayunos\", \"productos\": {\"Croissant\": \"1.50\", \"Zumo Natural\": \"2.50\", \"Desayuno Completo\": \"4.50\", \"Tostada con Aceite\": \"1.80\", \"Tostada con Tomate\": \"2.00\"}}, {\"categoria\": \"Meriendas\", \"productos\": {\"Batido Natural\": \"3.20\", \"Té con Pastas\": \"3.00\", \"Sándwich Mixto\": \"3.00\", \"Chocolate con Churros\": \"3.50\", \"Tostada con Mermelada\": \"2.00\"}}]\"\n            \"contacto\" => \"+34648999999\"\n            \"zona_id\" => 2\n            \"user_id\" => 6\n            \"created_at\" => \"2025-04-09 18:45:54\"\n            \"updated_at\" => \"2025-06-14 16:58:28\"\n            \"contactos_secundarios\" => \"[]\"\n            \"order\" => 11\n          ]\n          #original: array:16 [\n            \"id\" => 12\n            \"nombre\" => \"Heladería Dulce Nieve\"\n            \"descripcion\" => \"Autem numquam rerum sit illo quisquam. In iure expedita ratione soluta atque vero porro. Doloremque fuga corrupti quis molestias labore impedit est qui. Quidem voluptate officiis culpa voluptatem ut aut adipisci.\"\n            \"direccion\" => \"Paseo Jimínez, 637, 0º A, 85792, O Guzmán\"\n            \"ubicacion\" => \"{\"latitud\": \"37.007537\", \"longitud\": \"-6.554835\"}\"\n            \"horario\" => \"{\"lunes\": [\"09:00-18:00\"], \"jueves\": [\"09:00-18:00\"], \"martes\": [\"09:00-18:00\"], \"domingo\": [\"09:00-18:00\"], \"sábado\": [\"09:00-18:00\"], \"viernes\": [\"09:00-18:00\"], \"miércoles\": [\"09:00-18:00\"]}\"\n            \"enlaces_sociales\" => \"[{\"url\": \"https://es-es.facebook.com/cocoamatalascanas\", \"plataforma\": \"facebook\"}, {\"url\": \"https://www.instagram.com/cocoamatalascanas/\", \"plataforma\": \"instagram\"}, {\"url\": \"https://x.com/cocoamision\", \"plataforma\": \"twitter\"}]\"\n            \"enlaces_propios\" => \"{\"Pagina Web\": \"https://www.instagram.com/cocoamatalascanas/\"}\"\n            \"precios\" => \"[{\"categoria\": \"Cafés\", \"productos\": {\"Café Solo\": \"1.20\", \"Cappuccino\": \"2.00\", \"Café Americano\": \"1.30\", \"Café con Leche\": \"1.50\", \"Latte Macchiato\": \"2.20\"}}, {\"categoria\": \"Helados\", \"productos\": {\"Helado de fresa\": \"2.50\", \"Helado de limón\": \"2.50\", \"Helado de manzana\": \"2.50\", \"Helado de vainilla\": \"2.50\", \"Helado de chocolate\": \"2.50\"}}, {\"categoria\": \"Bebidas\", \"productos\": {\"Agua\": \"1.50\", \"Fanta\": \"2.00\", \"Sprite\": \"2.00\", \"Refresco\": \"2.00\", \"Coca-Cola\": \"2.00\"}}, {\"categoria\": \"Postres\", \"productos\": {\"Tarta de fresa\": \"3.50\", \"Tarta de queso\": \"3.50\", \"Tarta de manzana\": \"4.00\", \"Tarta de vainilla\": \"3.50\", \"Tarta de chocolate\": \"4.00\"}}, {\"categoria\": \"Desayunos\", \"productos\": {\"Croissant\": \"1.50\", \"Zumo Natural\": \"2.50\", \"Desayuno Completo\": \"4.50\", \"Tostada con Aceite\": \"1.80\", \"Tostada con Tomate\": \"2.00\"}}, {\"categoria\": \"Meriendas\", \"productos\": {\"Batido Natural\": \"3.20\", \"Té con Pastas\": \"3.00\", \"Sándwich Mixto\": \"3.00\", \"Chocolate con Churros\": \"3.50\", \"Tostada con Mermelada\": \"2.00\"}}]\"\n            \"contacto\" => \"+34648999999\"\n            \"zona_id\" => 2\n            \"user_id\" => 6\n            \"created_at\" => \"2025-04-09 18:45:54\"\n            \"updated_at\" => \"2025-06-14 16:58:28\"\n            \"contactos_secundarios\" => \"[]\"\n            \"order\" => 11\n          ]\n          #changes: []\n          #casts: array:6 [\n            \"ubicacion\" => \"array\"\n            \"horario\" => \"array\"\n            \"precios\" => \"array\"\n            \"enlaces_sociales\" => \"array\"\n            \"enlaces_propios\" => \"array\"\n            \"contactos_secundarios\" => \"array\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [\n            \"categorias\" => Illuminate\\Database\\Eloquent\\Collection {#3218 …2}\n            \"zona\" => App\\Models\\Zona {#2885 …30}\n            \"suscripcion\" => App\\Models\\Suscripcion {#2804 …30}\n          ]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [\n            0 => \"nombre\"\n            1 => \"descripcion\"\n            2 => \"direccion\"\n            3 => \"ubicacion\"\n            4 => \"horario\"\n            5 => \"enlaces_sociales\"\n            6 => \"enlaces_propios\"\n            7 => \"contactos_secundarios\"\n            8 => \"contacto\"\n            9 => \"zona_id\"\n            10 => \"user_id\"\n            11 => \"precios\"\n            12 => \"order\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        12 => App\\Models\\Negocio {#3128\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [\n            \"id\" => 13\n            \"nombre\" => \"Bar Piña Colada\"\n            \"descripcion\" => \"Est alias doloremque enim. Saepe fuga ex illo est dolore et libero. Iusto suscipit accusantium molestias ut sunt maiores quae cupiditate.\"\n            \"direccion\" => \"Camiño Munguía, 32, 47º D, 86590, O Nava\"\n            \"ubicacion\" => \"{\"latitud\": \"36.993080\", \"longitud\": \"-6.544465\"}\"\n            \"horario\" => \"{\"lunes\": [\"09:00-18:00\"], \"jueves\": [\"09:00-18:00\"], \"martes\": [\"09:00-18:00\"], \"domingo\": [\"09:00-18:00\"], \"sábado\": [\"09:00-18:00\"], \"viernes\": [\"09:00-18:00\"], \"miércoles\": [\"09:00-18:00\"]}\"\n            \"enlaces_sociales\" => \"[{\"url\": \"https://es-es.facebook.com/cocoamatalascanas\", \"plataforma\": \"facebook\"}, {\"url\": \"https://www.instagram.com/cocoamatalascanas/\", \"plataforma\": \"instagram\"}, {\"url\": \"https://x.com/cocoamision\", \"plataforma\": \"twitter\"}]\"\n            \"enlaces_propios\" => \"{\"Pagina Web\": \"https://www.instagram.com/cocoamatalascanas/\"}\"\n            \"precios\" => \"[{\"categoria\": \"Cócteles\", \"productos\": {\"Mojito\": \"5.50\", \"Daiquiri\": \"5.50\", \"Margarita\": \"6.00\", \"Mint Julep\": \"6.00\", \"Cosmopolitan\": \"6.50\"}}, {\"categoria\": \"Bebidas\", \"productos\": {\"Agua\": \"1.50\", \"Fanta\": \"2.00\", \"Sprite\": \"2.00\", \"Refresco\": \"2.00\", \"Coca-Cola\": \"2.00\"}}, {\"categoria\": \"Licores\", \"productos\": {\"Ron\": \"4.50\", \"Vodka\": \"4.50\", \"Brandy\": \"4.00\", \"Whisky\": \"5.00\", \"Ginebra\": \"5.00\"}}, {\"categoria\": \"Combinados\", \"productos\": {\"Ron Cola\": \"6.50\", \"Gin Tonic\": \"7.00\", \"Cuba Libre\": \"6.50\", \"Whisky Cola\": \"7.00\", \"Vodka Naranja\": \"6.50\"}}]\"\n            \"contacto\" => \"+34648999999\"\n            \"zona_id\" => 1\n            \"user_id\" => 5\n            \"created_at\" => \"2025-04-09 18:46:01\"\n            \"updated_at\" => \"2025-06-14 16:58:28\"\n            \"contactos_secundarios\" => \"[]\"\n            \"order\" => 12\n          ]\n          #original: array:16 [\n            \"id\" => 13\n            \"nombre\" => \"Bar Piña Colada\"\n            \"descripcion\" => \"Est alias doloremque enim. Saepe fuga ex illo est dolore et libero. Iusto suscipit accusantium molestias ut sunt maiores quae cupiditate.\"\n            \"direccion\" => \"Camiño Munguía, 32, 47º D, 86590, O Nava\"\n            \"ubicacion\" => \"{\"latitud\": \"36.993080\", \"longitud\": \"-6.544465\"}\"\n            \"horario\" => \"{\"lunes\": [\"09:00-18:00\"], \"jueves\": [\"09:00-18:00\"], \"martes\": [\"09:00-18:00\"], \"domingo\": [\"09:00-18:00\"], \"sábado\": [\"09:00-18:00\"], \"viernes\": [\"09:00-18:00\"], \"miércoles\": [\"09:00-18:00\"]}\"\n            \"enlaces_sociales\" => \"[{\"url\": \"https://es-es.facebook.com/cocoamatalascanas\", \"plataforma\": \"facebook\"}, {\"url\": \"https://www.instagram.com/cocoamatalascanas/\", \"plataforma\": \"instagram\"}, {\"url\": \"https://x.com/cocoamision\", \"plataforma\": \"twitter\"}]\"\n            \"enlaces_propios\" => \"{\"Pagina Web\": \"https://www.instagram.com/cocoamatalascanas/\"}\"\n            \"precios\" => \"[{\"categoria\": \"Cócteles\", \"productos\": {\"Mojito\": \"5.50\", \"Daiquiri\": \"5.50\", \"Margarita\": \"6.00\", \"Mint Julep\": \"6.00\", \"Cosmopolitan\": \"6.50\"}}, {\"categoria\": \"Bebidas\", \"productos\": {\"Agua\": \"1.50\", \"Fanta\": \"2.00\", \"Sprite\": \"2.00\", \"Refresco\": \"2.00\", \"Coca-Cola\": \"2.00\"}}, {\"categoria\": \"Licores\", \"productos\": {\"Ron\": \"4.50\", \"Vodka\": \"4.50\", \"Brandy\": \"4.00\", \"Whisky\": \"5.00\", \"Ginebra\": \"5.00\"}}, {\"categoria\": \"Combinados\", \"productos\": {\"Ron Cola\": \"6.50\", \"Gin Tonic\": \"7.00\", \"Cuba Libre\": \"6.50\", \"Whisky Cola\": \"7.00\", \"Vodka Naranja\": \"6.50\"}}]\"\n            \"contacto\" => \"+34648999999\"\n            \"zona_id\" => 1\n            \"user_id\" => 5\n            \"created_at\" => \"2025-04-09 18:46:01\"\n            \"updated_at\" => \"2025-06-14 16:58:28\"\n            \"contactos_secundarios\" => \"[]\"\n            \"order\" => 12\n          ]\n          #changes: []\n          #casts: array:6 [\n            \"ubicacion\" => \"array\"\n            \"horario\" => \"array\"\n            \"precios\" => \"array\"\n            \"enlaces_sociales\" => \"array\"\n            \"enlaces_propios\" => \"array\"\n            \"contactos_secundarios\" => \"array\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [\n            \"categorias\" => Illuminate\\Database\\Eloquent\\Collection {#3212 …2}\n            \"zona\" => App\\Models\\Zona {#2884 …30}\n            \"suscripcion\" => App\\Models\\Suscripcion {#2805 …30}\n          ]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [\n            0 => \"nombre\"\n            1 => \"descripcion\"\n            2 => \"direccion\"\n            3 => \"ubicacion\"\n            4 => \"horario\"\n            5 => \"enlaces_sociales\"\n            6 => \"enlaces_propios\"\n            7 => \"contactos_secundarios\"\n            8 => \"contacto\"\n            9 => \"zona_id\"\n            10 => \"user_id\"\n            11 => \"precios\"\n            12 => \"order\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        13 => App\\Models\\Negocio {#3135\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [\n            \"id\" => 14\n            \"nombre\" => \"Bar Sabor Caribe\"\n            \"descripcion\" => \"Voluptas vel qui quaerat incidunt placeat distinctio. Iste commodi sunt illo voluptatem incidunt sit tempora. Qui magnam omnis labore.\"\n            \"direccion\" => \"Carrer Valero, 69, 17º D, 66904, Bustos Medio\"\n            \"ubicacion\" => \"{\"latitud\": \"36.990614\", \"longitud\": \"-6.534405\"}\"\n            \"horario\" => \"{\"lunes\": [\"09:00-18:00\"], \"jueves\": [\"09:00-18:00\"], \"martes\": [\"09:00-18:00\"], \"domingo\": [\"09:00-18:00\"], \"sábado\": [\"09:00-18:00\"], \"viernes\": [\"09:00-18:00\"], \"miércoles\": [\"09:00-18:00\"]}\"\n            \"enlaces_sociales\" => \"[{\"url\": \"https://es-es.facebook.com/cocoamatalascanas\", \"plataforma\": \"facebook\"}, {\"url\": \"https://www.instagram.com/cocoamatalascanas/\", \"plataforma\": \"instagram\"}, {\"url\": \"https://x.com/cocoamision\", \"plataforma\": \"twitter\"}]\"\n            \"enlaces_propios\" => \"{\"Pagina Web\": \"https://www.instagram.com/cocoamatalascanas/\"}\"\n            \"precios\" => \"[{\"categoria\": \"Cócteles\", \"productos\": {\"Mojito\": \"5.50\", \"Daiquiri\": \"5.50\", \"Margarita\": \"6.00\", \"Mint Julep\": \"6.00\", \"Cosmopolitan\": \"6.50\"}}, {\"categoria\": \"Bebidas\", \"productos\": {\"Agua\": \"1.50\", \"Fanta\": \"2.00\", \"Sprite\": \"2.00\", \"Refresco\": \"2.00\", \"Coca-Cola\": \"2.00\"}}, {\"categoria\": \"Licores\", \"productos\": {\"Ron\": \"4.50\", \"Vodka\": \"4.50\", \"Brandy\": \"4.00\", \"Whisky\": \"5.00\", \"Ginebra\": \"5.00\"}}, {\"categoria\": \"Combinados\", \"productos\": {\"Ron Cola\": \"6.50\", \"Gin Tonic\": \"7.00\", \"Cuba Libre\": \"6.50\", \"Whisky Cola\": \"7.00\", \"Vodka Naranja\": \"6.50\"}}]\"\n            \"contacto\" => \"+34648999999\"\n            \"zona_id\" => 2\n            \"user_id\" => 5\n            \"created_at\" => \"2025-04-09 18:46:01\"\n            \"updated_at\" => \"2025-06-14 16:58:28\"\n            \"contactos_secundarios\" => \"[]\"\n            \"order\" => 13\n          ]\n          #original: array:16 [\n            \"id\" => 14\n            \"nombre\" => \"Bar Sabor Caribe\"\n            \"descripcion\" => \"Voluptas vel qui quaerat incidunt placeat distinctio. Iste commodi sunt illo voluptatem incidunt sit tempora. Qui magnam omnis labore.\"\n            \"direccion\" => \"Carrer Valero, 69, 17º D, 66904, Bustos Medio\"\n            \"ubicacion\" => \"{\"latitud\": \"36.990614\", \"longitud\": \"-6.534405\"}\"\n            \"horario\" => \"{\"lunes\": [\"09:00-18:00\"], \"jueves\": [\"09:00-18:00\"], \"martes\": [\"09:00-18:00\"], \"domingo\": [\"09:00-18:00\"], \"sábado\": [\"09:00-18:00\"], \"viernes\": [\"09:00-18:00\"], \"miércoles\": [\"09:00-18:00\"]}\"\n            \"enlaces_sociales\" => \"[{\"url\": \"https://es-es.facebook.com/cocoamatalascanas\", \"plataforma\": \"facebook\"}, {\"url\": \"https://www.instagram.com/cocoamatalascanas/\", \"plataforma\": \"instagram\"}, {\"url\": \"https://x.com/cocoamision\", \"plataforma\": \"twitter\"}]\"\n            \"enlaces_propios\" => \"{\"Pagina Web\": \"https://www.instagram.com/cocoamatalascanas/\"}\"\n            \"precios\" => \"[{\"categoria\": \"Cócteles\", \"productos\": {\"Mojito\": \"5.50\", \"Daiquiri\": \"5.50\", \"Margarita\": \"6.00\", \"Mint Julep\": \"6.00\", \"Cosmopolitan\": \"6.50\"}}, {\"categoria\": \"Bebidas\", \"productos\": {\"Agua\": \"1.50\", \"Fanta\": \"2.00\", \"Sprite\": \"2.00\", \"Refresco\": \"2.00\", \"Coca-Cola\": \"2.00\"}}, {\"categoria\": \"Licores\", \"productos\": {\"Ron\": \"4.50\", \"Vodka\": \"4.50\", \"Brandy\": \"4.00\", \"Whisky\": \"5.00\", \"Ginebra\": \"5.00\"}}, {\"categoria\": \"Combinados\", \"productos\": {\"Ron Cola\": \"6.50\", \"Gin Tonic\": \"7.00\", \"Cuba Libre\": \"6.50\", \"Whisky Cola\": \"7.00\", \"Vodka Naranja\": \"6.50\"}}]\"\n            \"contacto\" => \"+34648999999\"\n            \"zona_id\" => 2\n            \"user_id\" => 5\n            \"created_at\" => \"2025-04-09 18:46:01\"\n            \"updated_at\" => \"2025-06-14 16:58:28\"\n            \"contactos_secundarios\" => \"[]\"\n            \"order\" => 13\n          ]\n          #changes: []\n          #casts: array:6 [\n            \"ubicacion\" => \"array\"\n            \"horario\" => \"array\"\n            \"precios\" => \"array\"\n            \"enlaces_sociales\" => \"array\"\n            \"enlaces_propios\" => \"array\"\n            \"contactos_secundarios\" => \"array\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [\n            \"categorias\" => Illuminate\\Database\\Eloquent\\Collection {#3211 …2}\n            \"zona\" => App\\Models\\Zona {#2885 …30}\n            \"suscripcion\" => App\\Models\\Suscripcion {#2806 …30}\n          ]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [\n            0 => \"nombre\"\n            1 => \"descripcion\"\n            2 => \"direccion\"\n            3 => \"ubicacion\"\n            4 => \"horario\"\n            5 => \"enlaces_sociales\"\n            6 => \"enlaces_propios\"\n            7 => \"contactos_secundarios\"\n            8 => \"contacto\"\n            9 => \"zona_id\"\n            10 => \"user_id\"\n            11 => \"precios\"\n            12 => \"order\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        14 => App\\Models\\Negocio {#3133\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [\n            \"id\" => 15\n            \"nombre\" => \"Bar Coco Loco\"\n            \"descripcion\" => \"Ut nemo corrupti labore beatae aliquid. Quis ducimus id magni sunt distinctio dolor dolorem. Harum sed impedit incidunt et.\"\n            \"direccion\" => \"Camiño Elena, 98, 20º D, 99080, Arteaga del Vallès\"\n            \"ubicacion\" => \"{\"latitud\": \"37.008848\", \"longitud\": \"-6.558051\"}\"\n            \"horario\" => \"{\"lunes\": [\"09:00-18:00\"], \"jueves\": [\"09:00-18:00\"], \"martes\": [\"09:00-18:00\"], \"domingo\": [\"09:00-18:00\"], \"sábado\": [\"09:00-18:00\"], \"viernes\": [\"09:00-18:00\"], \"miércoles\": [\"09:00-18:00\"]}\"\n            \"enlaces_sociales\" => \"[{\"url\": \"https://es-es.facebook.com/cocoamatalascanas\", \"plataforma\": \"facebook\"}, {\"url\": \"https://www.instagram.com/cocoamatalascanas/\", \"plataforma\": \"instagram\"}, {\"url\": \"https://x.com/cocoamision\", \"plataforma\": \"twitter\"}]\"\n            \"enlaces_propios\" => \"{\"Pagina Web\": \"https://www.instagram.com/cocoamatalascanas/\"}\"\n            \"precios\" => \"[{\"categoria\": \"Cócteles\", \"productos\": {\"Mojito\": \"5.50\", \"Daiquiri\": \"5.50\", \"Margarita\": \"6.00\", \"Mint Julep\": \"6.00\", \"Cosmopolitan\": \"6.50\"}}, {\"categoria\": \"Bebidas\", \"productos\": {\"Agua\": \"1.50\", \"Fanta\": \"2.00\", \"Sprite\": \"2.00\", \"Refresco\": \"2.00\", \"Coca-Cola\": \"2.00\"}}, {\"categoria\": \"Licores\", \"productos\": {\"Ron\": \"4.50\", \"Vodka\": \"4.50\", \"Brandy\": \"4.00\", \"Whisky\": \"5.00\", \"Ginebra\": \"5.00\"}}, {\"categoria\": \"Combinados\", \"productos\": {\"Ron Cola\": \"6.50\", \"Gin Tonic\": \"7.00\", \"Cuba Libre\": \"6.50\", \"Whisky Cola\": \"7.00\", \"Vodka Naranja\": \"6.50\"}}]\"\n            \"contacto\" => \"+34648999999\"\n            \"zona_id\" => 1\n            \"user_id\" => 3\n            \"created_at\" => \"2025-04-09 18:46:01\"\n            \"updated_at\" => \"2025-06-14 16:58:28\"\n            \"contactos_secundarios\" => \"[]\"\n            \"order\" => 14\n          ]\n          #original: array:16 [\n            \"id\" => 15\n            \"nombre\" => \"Bar Coco Loco\"\n            \"descripcion\" => \"Ut nemo corrupti labore beatae aliquid. Quis ducimus id magni sunt distinctio dolor dolorem. Harum sed impedit incidunt et.\"\n            \"direccion\" => \"Camiño Elena, 98, 20º D, 99080, Arteaga del Vallès\"\n            \"ubicacion\" => \"{\"latitud\": \"37.008848\", \"longitud\": \"-6.558051\"}\"\n            \"horario\" => \"{\"lunes\": [\"09:00-18:00\"], \"jueves\": [\"09:00-18:00\"], \"martes\": [\"09:00-18:00\"], \"domingo\": [\"09:00-18:00\"], \"sábado\": [\"09:00-18:00\"], \"viernes\": [\"09:00-18:00\"], \"miércoles\": [\"09:00-18:00\"]}\"\n            \"enlaces_sociales\" => \"[{\"url\": \"https://es-es.facebook.com/cocoamatalascanas\", \"plataforma\": \"facebook\"}, {\"url\": \"https://www.instagram.com/cocoamatalascanas/\", \"plataforma\": \"instagram\"}, {\"url\": \"https://x.com/cocoamision\", \"plataforma\": \"twitter\"}]\"\n            \"enlaces_propios\" => \"{\"Pagina Web\": \"https://www.instagram.com/cocoamatalascanas/\"}\"\n            \"precios\" => \"[{\"categoria\": \"Cócteles\", \"productos\": {\"Mojito\": \"5.50\", \"Daiquiri\": \"5.50\", \"Margarita\": \"6.00\", \"Mint Julep\": \"6.00\", \"Cosmopolitan\": \"6.50\"}}, {\"categoria\": \"Bebidas\", \"productos\": {\"Agua\": \"1.50\", \"Fanta\": \"2.00\", \"Sprite\": \"2.00\", \"Refresco\": \"2.00\", \"Coca-Cola\": \"2.00\"}}, {\"categoria\": \"Licores\", \"productos\": {\"Ron\": \"4.50\", \"Vodka\": \"4.50\", \"Brandy\": \"4.00\", \"Whisky\": \"5.00\", \"Ginebra\": \"5.00\"}}, {\"categoria\": \"Combinados\", \"productos\": {\"Ron Cola\": \"6.50\", \"Gin Tonic\": \"7.00\", \"Cuba Libre\": \"6.50\", \"Whisky Cola\": \"7.00\", \"Vodka Naranja\": \"6.50\"}}]\"\n            \"contacto\" => \"+34648999999\"\n            \"zona_id\" => 1\n            \"user_id\" => 3\n            \"created_at\" => \"2025-04-09 18:46:01\"\n            \"updated_at\" => \"2025-06-14 16:58:28\"\n            \"contactos_secundarios\" => \"[]\"\n            \"order\" => 14\n          ]\n          #changes: []\n          #casts: array:6 [\n            \"ubicacion\" => \"array\"\n            \"horario\" => \"array\"\n            \"precios\" => \"array\"\n            \"enlaces_sociales\" => \"array\"\n            \"enlaces_propios\" => \"array\"\n            \"contactos_secundarios\" => \"array\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [\n            \"categorias\" => Illuminate\\Database\\Eloquent\\Collection {#3209 …2}\n            \"zona\" => App\\Models\\Zona {#2884 …30}\n            \"suscripcion\" => App\\Models\\Suscripcion {#2807 …30}\n          ]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [\n            0 => \"nombre\"\n            1 => \"descripcion\"\n            2 => \"direccion\"\n            3 => \"ubicacion\"\n            4 => \"horario\"\n            5 => \"enlaces_sociales\"\n            6 => \"enlaces_propios\"\n            7 => \"contactos_secundarios\"\n            8 => \"contacto\"\n            9 => \"zona_id\"\n            10 => \"user_id\"\n            11 => \"precios\"\n            12 => \"order\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        15 => App\\Models\\Negocio {#3134\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [\n            \"id\" => 16\n            \"nombre\" => \"Complementos Bahía\"\n            \"descripcion\" => \"Beatae quod est accusantium sed ut ut omnis. Et ut laboriosam delectus et. Ut voluptate temporibus doloribus impedit quia iusto quibusdam. Odio voluptas enim ipsa recusandae.\"\n            \"direccion\" => \"Passeig Izan, 63, 15º F, 16422, Palomo Alta\"\n            \"ubicacion\" => \"{\"latitud\": \"37.009503\", \"longitud\": \"-6.559731\"}\"\n            \"horario\" => \"{\"lunes\": [\"09:00-18:00\"], \"jueves\": [\"09:00-18:00\"], \"martes\": [\"09:00-18:00\"], \"domingo\": [\"09:00-18:00\"], \"sábado\": [\"09:00-18:00\"], \"viernes\": [\"09:00-18:00\"], \"miércoles\": [\"09:00-18:00\"]}\"\n            \"enlaces_sociales\" => \"[{\"url\": \"https://es-es.facebook.com/cocoamatalascanas\", \"plataforma\": \"facebook\"}, {\"url\": \"https://www.instagram.com/cocoamatalascanas/\", \"plataforma\": \"instagram\"}, {\"url\": \"https://x.com/cocoamision\", \"plataforma\": \"twitter\"}]\"\n            \"enlaces_propios\" => \"{\"Pagina Web\": \"https://www.instagram.com/cocoamatalascanas/\"}\"\n            \"precios\" => \"[{\"categoria\": \"Ropa Hombre\", \"productos\": {\"Camisa\": \"20.00\", \"Bañador\": \"22.00\", \"Bermudas\": \"18.00\", \"Camiseta\": \"15.00\", \"Pantalón\": \"25.00\"}}, {\"categoria\": \"Ropa Mujer\", \"productos\": {\"Falda\": \"22.00\", \"Bikini\": \"25.00\", \"Vestido\": \"30.00\", \"Camiseta\": \"12.00\", \"Pantalón\": \"28.00\"}}, {\"categoria\": \"Ropa Niños\", \"productos\": {\"Vestido\": \"18.00\", \"Bañador\": \"12.00\", \"Camiseta\": \"8.00\", \"Conjunto\": \"25.00\", \"Pantalón\": \"15.00\"}}, {\"categoria\": \"Accesorios\", \"productos\": {\"Producto\": \"10.00\"}}, {\"categoria\": \"Calzado\", \"productos\": {\"Zapatos\": \"40.00\", \"Chanclas\": \"8.00\", \"Sandalias\": \"15.00\", \"Alpargatas\": \"12.00\", \"Zapatillas\": \"30.00\"}}, {\"categoria\": \"Joyería\", \"productos\": {\"Anillo\": \"14.00\", \"Collar\": \"15.00\", \"Pulsera\": \"12.00\", \"Pendientes\": \"10.00\", \"Set completo\": \"35.00\"}}, {\"categoria\": \"Complementos\", \"productos\": {\"Bolso\": \"25.00\", \"Pañuelo\": \"10.00\", \"Sombrero\": \"15.00\", \"Cinturón\": \"12.00\", \"Gafas de sol\": \"20.00\"}}]\"\n            \"contacto\" => \"+34648999999\"\n            \"zona_id\" => 1\n            \"user_id\" => 7\n            \"created_at\" => \"2025-04-09 18:46:15\"\n            \"updated_at\" => \"2025-06-14 16:58:28\"\n            \"contactos_secundarios\" => \"[]\"\n            \"order\" => 15\n          ]\n          #original: array:16 [\n            \"id\" => 16\n             …15\n          ]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        16 => App\\Models\\Negocio {#3138\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        17 => App\\Models\\Negocio {#3142\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        18 => App\\Models\\Negocio {#3170\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        19 => App\\Models\\Negocio {#3396\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        20 => App\\Models\\Negocio {#3143\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        21 => App\\Models\\Negocio {#3145\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        22 => App\\Models\\Negocio {#3193\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        23 => App\\Models\\Negocio {#3194\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        24 => App\\Models\\Negocio {#3200\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        25 => App\\Models\\Negocio {#3201\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        26 => App\\Models\\Negocio {#3204\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        27 => App\\Models\\Negocio {#3205\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        28 => App\\Models\\Negocio {#3233\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        29 => App\\Models\\Negocio {#3012\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        30 => App\\Models\\Negocio {#3011\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        31 => App\\Models\\Negocio {#3010\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        32 => App\\Models\\Negocio {#3009\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        33 => App\\Models\\Negocio {#3008\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        34 => App\\Models\\Negocio {#3007\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        35 => App\\Models\\Negocio {#3006\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        36 => App\\Models\\Negocio {#3005\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        37 => App\\Models\\Negocio {#3004\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        38 => App\\Models\\Negocio {#3003\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        39 => App\\Models\\Negocio {#3002\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        40 => App\\Models\\Negocio {#3001\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        41 => App\\Models\\Negocio {#3000\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        42 => App\\Models\\Negocio {#2999\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        43 => App\\Models\\Negocio {#2998\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        44 => App\\Models\\Negocio {#2997\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        45 => App\\Models\\Negocio {#2996\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        46 => App\\Models\\Negocio {#2995\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n      ]\n      #escapeWhenCastingToString: false\n    }\n    \"filters\" => array:4 [\n      \"localidad\" => null\n      \"zona\" => null\n      \"categoria\" => null\n      \"suscripcion\" => null\n    ]\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n  ]\n  \"name\" => \"app.filament.widgets.negocios-map-with-filters\"\n  \"component\" => \"App\\Filament\\Widgets\\NegociosMapWithFilters\"\n  \"id\" => \"Q0xgzrnkWgaymMNvbmX5\"\n]", "app.filament.widgets.negocios-map #wPI8y5bMKAWBS8UWpNoo": "array:4 [\n  \"data\" => array:22 [\n    \"negocios\" => Illuminate\\Database\\Eloquent\\Collection {#2989\n      #items: array:47 [\n        0 => App\\Models\\Negocio {#3213\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [\n            \"id\" => 1\n            \"nombre\" => \"Chiringuito Sabor Tropical\"\n            \"descripcion\" => \"Magnam aperiam doloribus ut tempora autem dolore suscipit. Quis ut illum doloremque vero et. Ut illum sequi ex explicabo. Ipsa qui culpa vero amet quos nihil.\"\n            \"direccion\" => \"Ronda Aguilar, 5, 17º 2º, 68143, Gaona Alta\"\n            \"ubicacion\" => \"{\"latitud\": \"37.009604\", \"longitud\": \"-6.563694\"}\"\n            \"horario\" => \"{\"lunes\": [\"09:00-18:00\"], \"jueves\": [\"09:00-18:00\"], \"martes\": [\"09:00-18:00\"], \"domingo\": [\"09:00-18:00\"], \"sábado\": [\"09:00-18:00\"], \"viernes\": [\"09:00-18:00\"], \"miércoles\": [\"09:00-18:00\"]}\"\n            \"enlaces_sociales\" => \"[{\"url\": \"https://es-es.facebook.com/cocoamatalascanas\", \"plataforma\": \"facebook\"}, {\"url\": \"https://www.instagram.com/cocoamatalascanas/\", \"plataforma\": \"instagram\"}, {\"url\": \"https://x.com/cocoamision\", \"plataforma\": \"twitter\"}]\"\n            \"enlaces_propios\" => \"{\"Pagina Web\": \"https://www.instagram.com/cocoamatalascanas/\"}\"\n            \"precios\" => \"[{\"categoria\": \"Ensaladas\", \"productos\": {\"Ensalada César\": \"6.50\", \"Ensalada Capresse\": \"7.00\", \"Ensalada de Frutas\": \"5.50\", \"Ensalada de Verduras\": \"5.00\", \"Ensalada Mediterránea\": \"8.00\"}}, {\"categoria\": \"Entrantes\", \"productos\": {\"Bruschetta\": \"4.50\", \"Pan de Jamón\": \"4.50\", \"Patatas Bravas\": \"4.00\", \"Ensaladilla Rusa\": \"3.50\", \"Croquetas de Pollo\": \"5.00\"}}, {\"categoria\": \"Fritos\", \"productos\": {\"Choco Frito\": \"6.00\", \"Pollo Frito\": \"6.50\", \"Patatas Fritas\": \"3.00\", \"Zucchini Fritas\": \"4.50\", \"Calamares Fritos\": \"7.00\"}}, {\"categoria\": \"Carnes\", \"productos\": {\"Pechuga de pollo\": \"9.50\", \"Bistec de ternera\": \"14.00\", \"Costilla de cerdo\": \"11.00\", \"Filete de ternera\": \"12.00\", \"Chuletón de cerdo\": \"13.50\"}}, {\"categoria\": \"Pescado\", \"productos\": {\"Filete de atún\": \"15.00\", \"Filete de bonito\": \"10.00\", \"Filete de dorado\": \"14.00\", \"Filete de merluza\": \"9.50\", \"Filete de lenguado\": \"12.00\"}}, {\"categoria\": \"Mariscos\", \"productos\": {\"Cigalas\": \"18.00\", \"Carabineros\": \"25.00\", \"Langostinos\": \"15.00\", \"Gambas blancas\": \"12.50\", \"Camarón al ajillo\": \"10.00\"}}, {\"categoria\": \"Arroces\", \"productos\": {\"Arroz con carne\": \"9.00\", \"Arroz con pollo\": \"8.50\", \"Arroz con patatas\": \"7.00\", \"Arroz con mariscos\": \"10.00\", \"Arroz con verduras\": \"7.50\"}}, {\"categoria\": \"Bebidas\", \"productos\": {\"Agua\": \"1.50\", \"Fanta\": \"2.00\", \"Sprite\": \"2.00\", \"Refresco\": \"2.00\", \"Coca-Cola\": \"2.00\"}}, {\"categoria\": \"Postres\", \"productos\": {\"Tarta de fresa\": \"3.50\", \"Tarta de queso\": \"3.50\", \"Tarta de manzana\": \"4.00\", \"Tarta de vainilla\": \"3.50\", \"Tarta de chocolate\": \"4.00\"}}, {\"categoria\": \"Helados\", \"productos\": {\"Helado de fresa\": \"2.50\", \"Helado de limón\": \"2.50\", \"Helado de manzana\": \"2.50\", \"Helado de vainilla\": \"2.50\", \"Helado de chocolate\": \"2.50\"}}, {\"categoria\": \"Cócteles\", \"productos\": {\"Mojito\": \"5.50\", \"Daiquiri\": \"5.50\", \"Margarita\": \"6.00\", \"Mint Julep\": \"6.00\", \"Cosmopolitan\": \"6.50\"}}]\"\n            \"contacto\" => \"+34648999999\"\n            \"zona_id\" => 2\n            \"user_id\" => 7\n            \"created_at\" => \"2025-04-09 18:45:19\"\n            \"updated_at\" => \"2025-04-22 16:14:37\"\n            \"contactos_secundarios\" => \"[]\"\n            \"order\" => 0\n          ]\n          #original: array:16 [\n            \"id\" => 1\n            \"nombre\" => \"Chiringuito Sabor Tropical\"\n            \"descripcion\" => \"Magnam aperiam doloribus ut tempora autem dolore suscipit. Quis ut illum doloremque vero et. Ut illum sequi ex explicabo. Ipsa qui culpa vero amet quos nihil.\"\n            \"direccion\" => \"Ronda Aguilar, 5, 17º 2º, 68143, Gaona Alta\"\n            \"ubicacion\" => \"{\"latitud\": \"37.009604\", \"longitud\": \"-6.563694\"}\"\n            \"horario\" => \"{\"lunes\": [\"09:00-18:00\"], \"jueves\": [\"09:00-18:00\"], \"martes\": [\"09:00-18:00\"], \"domingo\": [\"09:00-18:00\"], \"sábado\": [\"09:00-18:00\"], \"viernes\": [\"09:00-18:00\"], \"miércoles\": [\"09:00-18:00\"]}\"\n            \"enlaces_sociales\" => \"[{\"url\": \"https://es-es.facebook.com/cocoamatalascanas\", \"plataforma\": \"facebook\"}, {\"url\": \"https://www.instagram.com/cocoamatalascanas/\", \"plataforma\": \"instagram\"}, {\"url\": \"https://x.com/cocoamision\", \"plataforma\": \"twitter\"}]\"\n            \"enlaces_propios\" => \"{\"Pagina Web\": \"https://www.instagram.com/cocoamatalascanas/\"}\"\n            \"precios\" => \"[{\"categoria\": \"Ensaladas\", \"productos\": {\"Ensalada César\": \"6.50\", \"Ensalada Capresse\": \"7.00\", \"Ensalada de Frutas\": \"5.50\", \"Ensalada de Verduras\": \"5.00\", \"Ensalada Mediterránea\": \"8.00\"}}, {\"categoria\": \"Entrantes\", \"productos\": {\"Bruschetta\": \"4.50\", \"Pan de Jamón\": \"4.50\", \"Patatas Bravas\": \"4.00\", \"Ensaladilla Rusa\": \"3.50\", \"Croquetas de Pollo\": \"5.00\"}}, {\"categoria\": \"Fritos\", \"productos\": {\"Choco Frito\": \"6.00\", \"Pollo Frito\": \"6.50\", \"Patatas Fritas\": \"3.00\", \"Zucchini Fritas\": \"4.50\", \"Calamares Fritos\": \"7.00\"}}, {\"categoria\": \"Carnes\", \"productos\": {\"Pechuga de pollo\": \"9.50\", \"Bistec de ternera\": \"14.00\", \"Costilla de cerdo\": \"11.00\", \"Filete de ternera\": \"12.00\", \"Chuletón de cerdo\": \"13.50\"}}, {\"categoria\": \"Pescado\", \"productos\": {\"Filete de atún\": \"15.00\", \"Filete de bonito\": \"10.00\", \"Filete de dorado\": \"14.00\", \"Filete de merluza\": \"9.50\", \"Filete de lenguado\": \"12.00\"}}, {\"categoria\": \"Mariscos\", \"productos\": {\"Cigalas\": \"18.00\", \"Carabineros\": \"25.00\", \"Langostinos\": \"15.00\", \"Gambas blancas\": \"12.50\", \"Camarón al ajillo\": \"10.00\"}}, {\"categoria\": \"Arroces\", \"productos\": {\"Arroz con carne\": \"9.00\", \"Arroz con pollo\": \"8.50\", \"Arroz con patatas\": \"7.00\", \"Arroz con mariscos\": \"10.00\", \"Arroz con verduras\": \"7.50\"}}, {\"categoria\": \"Bebidas\", \"productos\": {\"Agua\": \"1.50\", \"Fanta\": \"2.00\", \"Sprite\": \"2.00\", \"Refresco\": \"2.00\", \"Coca-Cola\": \"2.00\"}}, {\"categoria\": \"Postres\", \"productos\": {\"Tarta de fresa\": \"3.50\", \"Tarta de queso\": \"3.50\", \"Tarta de manzana\": \"4.00\", \"Tarta de vainilla\": \"3.50\", \"Tarta de chocolate\": \"4.00\"}}, {\"categoria\": \"Helados\", \"productos\": {\"Helado de fresa\": \"2.50\", \"Helado de limón\": \"2.50\", \"Helado de manzana\": \"2.50\", \"Helado de vainilla\": \"2.50\", \"Helado de chocolate\": \"2.50\"}}, {\"categoria\": \"Cócteles\", \"productos\": {\"Mojito\": \"5.50\", \"Daiquiri\": \"5.50\", \"Margarita\": \"6.00\", \"Mint Julep\": \"6.00\", \"Cosmopolitan\": \"6.50\"}}]\"\n            \"contacto\" => \"+34648999999\"\n            \"zona_id\" => 2\n            \"user_id\" => 7\n            \"created_at\" => \"2025-04-09 18:45:19\"\n            \"updated_at\" => \"2025-04-22 16:14:37\"\n            \"contactos_secundarios\" => \"[]\"\n            \"order\" => 0\n          ]\n          #changes: []\n          #casts: array:6 [\n            \"ubicacion\" => \"array\"\n            \"horario\" => \"array\"\n            \"precios\" => \"array\"\n            \"enlaces_sociales\" => \"array\"\n            \"enlaces_propios\" => \"array\"\n            \"contactos_secundarios\" => \"array\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [\n            \"categorias\" => Illuminate\\Database\\Eloquent\\Collection {#2992 …2}\n            \"zona\" => App\\Models\\Zona {#2885 …30}\n            \"suscripcion\" => App\\Models\\Suscripcion {#2822 …30}\n          ]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [\n            0 => \"nombre\"\n            1 => \"descripcion\"\n            2 => \"direccion\"\n            3 => \"ubicacion\"\n            4 => \"horario\"\n            5 => \"enlaces_sociales\"\n            6 => \"enlaces_propios\"\n            7 => \"contactos_secundarios\"\n            8 => \"contacto\"\n            9 => \"zona_id\"\n            10 => \"user_id\"\n            11 => \"precios\"\n            12 => \"order\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        1 => App\\Models\\Negocio {#3013\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [\n            \"id\" => 2\n            \"nombre\" => \"Chiringuito El Cangrejo\"\n            \"descripcion\" => \"Qui hic accusantium consequuntur est et nam voluptatem. Non omnis porro ea asperiores. Et commodi non et non qui exercitationem ad. Est ut maiores porro error pariatur.\"\n            \"direccion\" => \"Praza Enrique, 76, 6º F, 74469, Los Sanz\"\n            \"ubicacion\" => \"{\"latitud\": \"37.010670\", \"longitud\": \"-6.559759\"}\"\n            \"horario\" => \"{\"lunes\": [\"09:00-18:00\"], \"jueves\": [\"09:00-18:00\"], \"martes\": [\"09:00-18:00\"], \"domingo\": [\"09:00-18:00\"], \"sábado\": [\"09:00-18:00\"], \"viernes\": [\"09:00-18:00\"], \"miércoles\": [\"09:00-18:00\"]}\"\n            \"enlaces_sociales\" => \"[{\"url\": \"https://es-es.facebook.com/cocoamatalascanas\", \"plataforma\": \"facebook\"}, {\"url\": \"https://www.instagram.com/cocoamatalascanas/\", \"plataforma\": \"instagram\"}, {\"url\": \"https://x.com/cocoamision\", \"plataforma\": \"twitter\"}]\"\n            \"enlaces_propios\" => \"{\"Pagina Web\": \"https://www.instagram.com/cocoamatalascanas/\"}\"\n            \"precios\" => \"[{\"categoria\": \"Ensaladas\", \"productos\": {\"Ensalada César\": \"6.50\", \"Ensalada Capresse\": \"7.00\", \"Ensalada de Frutas\": \"5.50\", \"Ensalada de Verduras\": \"5.00\", \"Ensalada Mediterránea\": \"8.00\"}}, {\"categoria\": \"Entrantes\", \"productos\": {\"Bruschetta\": \"4.50\", \"Pan de Jamón\": \"4.50\", \"Patatas Bravas\": \"4.00\", \"Ensaladilla Rusa\": \"3.50\", \"Croquetas de Pollo\": \"5.00\"}}, {\"categoria\": \"Fritos\", \"productos\": {\"Choco Frito\": \"6.00\", \"Pollo Frito\": \"6.50\", \"Patatas Fritas\": \"3.00\", \"Zucchini Fritas\": \"4.50\", \"Calamares Fritos\": \"7.00\"}}, {\"categoria\": \"Carnes\", \"productos\": {\"Pechuga de pollo\": \"9.50\", \"Bistec de ternera\": \"14.00\", \"Costilla de cerdo\": \"11.00\", \"Filete de ternera\": \"12.00\", \"Chuletón de cerdo\": \"13.50\"}}, {\"categoria\": \"Pescado\", \"productos\": {\"Filete de atún\": \"15.00\", \"Filete de bonito\": \"10.00\", \"Filete de dorado\": \"14.00\", \"Filete de merluza\": \"9.50\", \"Filete de lenguado\": \"12.00\"}}, {\"categoria\": \"Mariscos\", \"productos\": {\"Cigalas\": \"18.00\", \"Carabineros\": \"25.00\", \"Langostinos\": \"15.00\", \"Gambas blancas\": \"12.50\", \"Camarón al ajillo\": \"10.00\"}}, {\"categoria\": \"Arroces\", \"productos\": {\"Arroz con carne\": \"9.00\", \"Arroz con pollo\": \"8.50\", \"Arroz con patatas\": \"7.00\", \"Arroz con mariscos\": \"10.00\", \"Arroz con verduras\": \"7.50\"}}, {\"categoria\": \"Bebidas\", \"productos\": {\"Agua\": \"1.50\", \"Fanta\": \"2.00\", \"Sprite\": \"2.00\", \"Refresco\": \"2.00\", \"Coca-Cola\": \"2.00\"}}, {\"categoria\": \"Postres\", \"productos\": {\"Tarta de fresa\": \"3.50\", \"Tarta de queso\": \"3.50\", \"Tarta de manzana\": \"4.00\", \"Tarta de vainilla\": \"3.50\", \"Tarta de chocolate\": \"4.00\"}}, {\"categoria\": \"Helados\", \"productos\": {\"Helado de fresa\": \"2.50\", \"Helado de limón\": \"2.50\", \"Helado de manzana\": \"2.50\", \"Helado de vainilla\": \"2.50\", \"Helado de chocolate\": \"2.50\"}}, {\"categoria\": \"Cócteles\", \"productos\": {\"Mojito\": \"5.50\", \"Daiquiri\": \"5.50\", \"Margarita\": \"6.00\", \"Mint Julep\": \"6.00\", \"Cosmopolitan\": \"6.50\"}}]\"\n            \"contacto\" => \"+34648999999\"\n            \"zona_id\" => 1\n            \"user_id\" => 3\n            \"created_at\" => \"2025-04-09 18:45:19\"\n            \"updated_at\" => \"2025-06-14 16:58:28\"\n            \"contactos_secundarios\" => \"[]\"\n            \"order\" => 1\n          ]\n          #original: array:16 [\n            \"id\" => 2\n            \"nombre\" => \"Chiringuito El Cangrejo\"\n            \"descripcion\" => \"Qui hic accusantium consequuntur est et nam voluptatem. Non omnis porro ea asperiores. Et commodi non et non qui exercitationem ad. Est ut maiores porro error pariatur.\"\n            \"direccion\" => \"Praza Enrique, 76, 6º F, 74469, Los Sanz\"\n            \"ubicacion\" => \"{\"latitud\": \"37.010670\", \"longitud\": \"-6.559759\"}\"\n            \"horario\" => \"{\"lunes\": [\"09:00-18:00\"], \"jueves\": [\"09:00-18:00\"], \"martes\": [\"09:00-18:00\"], \"domingo\": [\"09:00-18:00\"], \"sábado\": [\"09:00-18:00\"], \"viernes\": [\"09:00-18:00\"], \"miércoles\": [\"09:00-18:00\"]}\"\n            \"enlaces_sociales\" => \"[{\"url\": \"https://es-es.facebook.com/cocoamatalascanas\", \"plataforma\": \"facebook\"}, {\"url\": \"https://www.instagram.com/cocoamatalascanas/\", \"plataforma\": \"instagram\"}, {\"url\": \"https://x.com/cocoamision\", \"plataforma\": \"twitter\"}]\"\n            \"enlaces_propios\" => \"{\"Pagina Web\": \"https://www.instagram.com/cocoamatalascanas/\"}\"\n            \"precios\" => \"[{\"categoria\": \"Ensaladas\", \"productos\": {\"Ensalada César\": \"6.50\", \"Ensalada Capresse\": \"7.00\", \"Ensalada de Frutas\": \"5.50\", \"Ensalada de Verduras\": \"5.00\", \"Ensalada Mediterránea\": \"8.00\"}}, {\"categoria\": \"Entrantes\", \"productos\": {\"Bruschetta\": \"4.50\", \"Pan de Jamón\": \"4.50\", \"Patatas Bravas\": \"4.00\", \"Ensaladilla Rusa\": \"3.50\", \"Croquetas de Pollo\": \"5.00\"}}, {\"categoria\": \"Fritos\", \"productos\": {\"Choco Frito\": \"6.00\", \"Pollo Frito\": \"6.50\", \"Patatas Fritas\": \"3.00\", \"Zucchini Fritas\": \"4.50\", \"Calamares Fritos\": \"7.00\"}}, {\"categoria\": \"Carnes\", \"productos\": {\"Pechuga de pollo\": \"9.50\", \"Bistec de ternera\": \"14.00\", \"Costilla de cerdo\": \"11.00\", \"Filete de ternera\": \"12.00\", \"Chuletón de cerdo\": \"13.50\"}}, {\"categoria\": \"Pescado\", \"productos\": {\"Filete de atún\": \"15.00\", \"Filete de bonito\": \"10.00\", \"Filete de dorado\": \"14.00\", \"Filete de merluza\": \"9.50\", \"Filete de lenguado\": \"12.00\"}}, {\"categoria\": \"Mariscos\", \"productos\": {\"Cigalas\": \"18.00\", \"Carabineros\": \"25.00\", \"Langostinos\": \"15.00\", \"Gambas blancas\": \"12.50\", \"Camarón al ajillo\": \"10.00\"}}, {\"categoria\": \"Arroces\", \"productos\": {\"Arroz con carne\": \"9.00\", \"Arroz con pollo\": \"8.50\", \"Arroz con patatas\": \"7.00\", \"Arroz con mariscos\": \"10.00\", \"Arroz con verduras\": \"7.50\"}}, {\"categoria\": \"Bebidas\", \"productos\": {\"Agua\": \"1.50\", \"Fanta\": \"2.00\", \"Sprite\": \"2.00\", \"Refresco\": \"2.00\", \"Coca-Cola\": \"2.00\"}}, {\"categoria\": \"Postres\", \"productos\": {\"Tarta de fresa\": \"3.50\", \"Tarta de queso\": \"3.50\", \"Tarta de manzana\": \"4.00\", \"Tarta de vainilla\": \"3.50\", \"Tarta de chocolate\": \"4.00\"}}, {\"categoria\": \"Helados\", \"productos\": {\"Helado de fresa\": \"2.50\", \"Helado de limón\": \"2.50\", \"Helado de manzana\": \"2.50\", \"Helado de vainilla\": \"2.50\", \"Helado de chocolate\": \"2.50\"}}, {\"categoria\": \"Cócteles\", \"productos\": {\"Mojito\": \"5.50\", \"Daiquiri\": \"5.50\", \"Margarita\": \"6.00\", \"Mint Julep\": \"6.00\", \"Cosmopolitan\": \"6.50\"}}]\"\n            \"contacto\" => \"+34648999999\"\n            \"zona_id\" => 1\n            \"user_id\" => 3\n            \"created_at\" => \"2025-04-09 18:45:19\"\n            \"updated_at\" => \"2025-06-14 16:58:28\"\n            \"contactos_secundarios\" => \"[]\"\n            \"order\" => 1\n          ]\n          #changes: []\n          #casts: array:6 [\n            \"ubicacion\" => \"array\"\n            \"horario\" => \"array\"\n            \"precios\" => \"array\"\n            \"enlaces_sociales\" => \"array\"\n            \"enlaces_propios\" => \"array\"\n            \"contactos_secundarios\" => \"array\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [\n            \"categorias\" => Illuminate\\Database\\Eloquent\\Collection {#3047 …2}\n            \"zona\" => App\\Models\\Zona {#2884 …30}\n            \"suscripcion\" => App\\Models\\Suscripcion {#2794 …30}\n          ]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [\n            0 => \"nombre\"\n            1 => \"descripcion\"\n            2 => \"direccion\"\n            3 => \"ubicacion\"\n            4 => \"horario\"\n            5 => \"enlaces_sociales\"\n            6 => \"enlaces_propios\"\n            7 => \"contactos_secundarios\"\n            8 => \"contacto\"\n            9 => \"zona_id\"\n            10 => \"user_id\"\n            11 => \"precios\"\n            12 => \"order\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        2 => App\\Models\\Negocio {#3014\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [\n            \"id\" => 3\n            \"nombre\" => \"Chiringuito Arena & Sol\"\n            \"descripcion\" => \"Possimus nobis perferendis incidunt qui ipsam. Eaque cum quos adipisci commodi sed. Blanditiis nisi reiciendis laborum laborum sunt nihil.\"\n            \"direccion\" => \"Passeig Josefa, 15, 7º B, 44549, Gonzáles del Penedès\"\n             …12\n          ]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        3 => App\\Models\\Negocio {#3015\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        4 => App\\Models\\Negocio {#3016\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        5 => App\\Models\\Negocio {#3017\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        6 => App\\Models\\Negocio {#3018\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        7 => App\\Models\\Negocio {#3019\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        8 => App\\Models\\Negocio {#3020\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        9 => App\\Models\\Negocio {#3021\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        10 => App\\Models\\Negocio {#3022\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        11 => App\\Models\\Negocio {#3131\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        12 => App\\Models\\Negocio {#3128\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        13 => App\\Models\\Negocio {#3135\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        14 => App\\Models\\Negocio {#3133\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        15 => App\\Models\\Negocio {#3134\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        16 => App\\Models\\Negocio {#3138\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        17 => App\\Models\\Negocio {#3142\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        18 => App\\Models\\Negocio {#3170\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        19 => App\\Models\\Negocio {#3396\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        20 => App\\Models\\Negocio {#3143\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        21 => App\\Models\\Negocio {#3145\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        22 => App\\Models\\Negocio {#3193\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        23 => App\\Models\\Negocio {#3194\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        24 => App\\Models\\Negocio {#3200\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        25 => App\\Models\\Negocio {#3201\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        26 => App\\Models\\Negocio {#3204\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        27 => App\\Models\\Negocio {#3205\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        28 => App\\Models\\Negocio {#3233\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        29 => App\\Models\\Negocio {#3012\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        30 => App\\Models\\Negocio {#3011\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        31 => App\\Models\\Negocio {#3010\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        32 => App\\Models\\Negocio {#3009\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        33 => App\\Models\\Negocio {#3008\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        34 => App\\Models\\Negocio {#3007\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        35 => App\\Models\\Negocio {#3006\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        36 => App\\Models\\Negocio {#3005\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        37 => App\\Models\\Negocio {#3004\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        38 => App\\Models\\Negocio {#3003\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        39 => App\\Models\\Negocio {#3002\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        40 => App\\Models\\Negocio {#3001\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        41 => App\\Models\\Negocio {#3000\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        42 => App\\Models\\Negocio {#2999\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        43 => App\\Models\\Negocio {#2998\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        44 => App\\Models\\Negocio {#2997\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        45 => App\\Models\\Negocio {#2996\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n        46 => App\\Models\\Negocio {#2995\n          #connection: \"mysql\"\n          #table: \"negocios\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:16 [ …16]\n          #original: array:16 [ …16]\n          #changes: []\n          #casts: array:6 [ …6]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: array:3 [ …3]\n          #touches: []\n          +timestamps: true\n          +usesUniqueIds: false\n          #hidden: []\n          #visible: []\n          #fillable: array:13 [ …13]\n          #guarded: array:1 [ …1]\n          +mediaConversions: []\n          +mediaCollections: []\n          #deletePreservingMedia: false\n          #unAttachedMediaLibraryItems: []\n        }\n      ]\n      #escapeWhenCastingToString: false\n    }\n    \"filters\" => array:4 [\n      \"localidad\" => null\n      \"zona\" => null\n      \"categoria\" => null\n      \"suscripcion\" => null\n    ]\n    \"height\" => \"500px\"\n    \"_flag\" => false\n    \"fitBounds\" => null\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mapOptions\" => array:2 [\n      \"center\" => array:2 [\n        0 => 0\n        1 => 0\n      ]\n      \"zoom\" => 2\n    ]\n    \"centerTo\" => array:2 [\n      \"location\" => array:2 [\n        \"lat\" => 36.9990019\n        \"lng\" => -6.5478919\n      ]\n      \"zoom\" => 14\n    ]\n    \"markers\" => array:47 [\n      0 => array:8 [\n        \"id\" => \"1\"\n        \"lat\" => 37.009604\n        \"lng\" => -6.563694\n        \"popup\" => \"\"\"\n          <b>Chiringuito Sabor Tropical</b><br/>\\n\n                              <span style=\"color: green;\">\\n\n                                  Suscripción Activa Hasta 31/12/2025\\n\n                              </span><br/>\\n\n                              <button onclick=\"Livewire.dispatch('cambiarSuscripcion', [1])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#dc3545;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Anular Suscripción\\n\n                              </button>\\n\n                              <button onclick=\"Livewire.dispatch('showEditNegocio', [1])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Editar Negocio\\n\n                              </button>\n          \"\"\"\n        \"tooltip\" => null\n        \"icon\" => array:6 [\n          \"iconUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-green.png\"\n          \"shadowUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-shadow.png\"\n          \"iconSize\" => array:2 [ …2]\n          \"iconAnchor\" => array:2 [ …2]\n          \"popupAnchor\" => array:2 [ …2]\n          \"shadowSize\" => array:2 [ …2]\n        ]\n        \"callback\" => \"\"\n        \"type\" => \"marker\"\n      ]\n      1 => array:8 [\n        \"id\" => \"2\"\n        \"lat\" => 37.01067\n        \"lng\" => -6.559759\n        \"popup\" => \"\"\"\n          <b>Chiringuito El Cangrejo</b><br/>\\n\n                              <span style=\"color: green;\">\\n\n                                  Suscripción Activa Hasta 29/08/2025\\n\n                              </span><br/>\\n\n                              <button onclick=\"Livewire.dispatch('cambiarSuscripcion', [2])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#dc3545;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Anular Suscripción\\n\n                              </button>\\n\n                              <button onclick=\"Livewire.dispatch('showEditNegocio', [2])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Editar Negocio\\n\n                              </button>\n          \"\"\"\n        \"tooltip\" => null\n        \"icon\" => array:6 [\n          \"iconUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-green.png\"\n          \"shadowUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-shadow.png\"\n          \"iconSize\" => array:2 [ …2]\n          \"iconAnchor\" => array:2 [ …2]\n          \"popupAnchor\" => array:2 [ …2]\n          \"shadowSize\" => array:2 [ …2]\n        ]\n        \"callback\" => \"\"\n        \"type\" => \"marker\"\n      ]\n      2 => array:8 [\n        \"id\" => \"3\"\n        \"lat\" => 37.011675\n        \"lng\" => -6.562664\n        \"popup\" => \"\"\"\n          <b>Chiringuito Arena & Sol</b><br/>\\n\n                              <span style=\"color: green;\">\\n\n                                  Suscripción Activa Hasta 21/06/2025\\n\n                              </span><br/>\\n\n                              <button onclick=\"Livewire.dispatch('cambiarSuscripcion', [3])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#dc3545;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Anular Suscripción\\n\n                              </button>\\n\n                              <button onclick=\"Livewire.dispatch('showEditNegocio', [3])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Editar Negocio\\n\n                              </button>\n          \"\"\"\n        \"tooltip\" => null\n        \"icon\" => array:6 [\n          \"iconUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-green.png\"\n          \"shadowUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-shadow.png\"\n          \"iconSize\" => array:2 [ …2]\n          \"iconAnchor\" => array:2 [ …2]\n          \"popupAnchor\" => array:2 [ …2]\n          \"shadowSize\" => array:2 [ …2]\n        ]\n        \"callback\" => \"\"\n        \"type\" => \"marker\"\n      ]\n      3 => array:8 [\n        \"id\" => \"4\"\n        \"lat\" => 36.985082\n        \"lng\" => -6.53034\n        \"popup\" => \"\"\"\n          <b>La Parrilla del Faro</b><br/>\\n\n                              <span style=\"color: green;\">\\n\n                                  Suscripción Activa Hasta 17/08/2025\\n\n                              </span><br/>\\n\n                              <button onclick=\"Livewire.dispatch('cambiarSuscripcion', [4])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#dc3545;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Anular Suscripción\\n\n                              </button>\\n\n                              <button onclick=\"Livewire.dispatch('showEditNegocio', [4])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Editar Negocio\\n\n                              </button>\n          \"\"\"\n        \"tooltip\" => null\n        \"icon\" => array:6 [\n          \"iconUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-green.png\"\n          \"shadowUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-shadow.png\"\n          \"iconSize\" => array:2 [ …2]\n          \"iconAnchor\" => array:2 [ …2]\n          \"popupAnchor\" => array:2 [ …2]\n          \"shadowSize\" => array:2 [ …2]\n        ]\n        \"callback\" => \"\"\n        \"type\" => \"marker\"\n      ]\n      4 => array:8 [\n        \"id\" => \"5\"\n        \"lat\" => 36.990715\n        \"lng\" => -6.530449\n        \"popup\" => \"\"\"\n          <b>Casa de la Abuela</b><br/>\\n\n                              <span style=\"color: red;\">\\n\n                                  Suscripción Inactiva Desde 07/07/2025\\n\n                              </span><br/>\\n\n                              <button onclick=\"Livewire.dispatch('cambiarSuscripcion', [5])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#28a745;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Activar Suscripción\\n\n                              </button>\\n\n                              <button onclick=\"Livewire.dispatch('showEditNegocio', [5])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Editar Negocio\\n\n                              </button>\n          \"\"\"\n        \"tooltip\" => null\n        \"icon\" => array:6 [\n          \"iconUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-red.png\"\n          \"shadowUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-shadow.png\"\n          \"iconSize\" => array:2 [ …2]\n          \"iconAnchor\" => array:2 [ …2]\n          \"popupAnchor\" => array:2 [ …2]\n          \"shadowSize\" => array:2 [ …2]\n        ]\n        \"callback\" => \"\"\n        \"type\" => \"marker\"\n      ]\n      5 => array:8 [\n        \"id\" => \"6\"\n        \"lat\" => 37.00441\n        \"lng\" => -6.550451\n        \"popup\" => \"\"\"\n          <b>Marisquería del Puerto</b><br/>\\n\n                              <span style=\"color: green;\">\\n\n                                  Suscripción Activa Hasta 25/09/2025\\n\n                              </span><br/>\\n\n                              <button onclick=\"Livewire.dispatch('cambiarSuscripcion', [6])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#dc3545;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Anular Suscripción\\n\n                              </button>\\n\n                              <button onclick=\"Livewire.dispatch('showEditNegocio', [6])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Editar Negocio\\n\n                              </button>\n          \"\"\"\n        \"tooltip\" => null\n        \"icon\" => array:6 [\n          \"iconUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-green.png\"\n          \"shadowUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-shadow.png\"\n          \"iconSize\" => array:2 [ …2]\n          \"iconAnchor\" => array:2 [ …2]\n          \"popupAnchor\" => array:2 [ …2]\n          \"shadowSize\" => array:2 [ …2]\n        ]\n        \"callback\" => \"\"\n        \"type\" => \"marker\"\n      ]\n      6 => array:8 [\n        \"id\" => \"7\"\n        \"lat\" => 36.999523\n        \"lng\" => -6.538771\n        \"popup\" => \"\"\"\n          <b>Bar de Tapas Mar Azul</b><br/>\\n\n                              <span style=\"color: red;\">\\n\n                                  Suscripción Inactiva Desde 15/09/2025\\n\n                              </span><br/>\\n\n                              <button onclick=\"Livewire.dispatch('cambiarSuscripcion', [7])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#28a745;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Activar Suscripción\\n\n                              </button>\\n\n                              <button onclick=\"Livewire.dispatch('showEditNegocio', [7])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Editar Negocio\\n\n                              </button>\n          \"\"\"\n        \"tooltip\" => null\n        \"icon\" => array:6 [\n          \"iconUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-red.png\"\n          \"shadowUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-shadow.png\"\n          \"iconSize\" => array:2 [ …2]\n          \"iconAnchor\" => array:2 [ …2]\n          \"popupAnchor\" => array:2 [ …2]\n          \"shadowSize\" => array:2 [ …2]\n        ]\n        \"callback\" => \"\"\n        \"type\" => \"marker\"\n      ]\n      7 => array:8 [\n        \"id\" => \"8\"\n        \"lat\" => 36.999479\n        \"lng\" => -6.549389\n        \"popup\" => \"\"\"\n          <b>Tapas & Más</b><br/>\\n\n                              <span style=\"color: green;\">\\n\n                                  Suscripción Activa Hasta 17/08/2025\\n\n                              </span><br/>\\n\n                              <button onclick=\"Livewire.dispatch('cambiarSuscripcion', [8])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#dc3545;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Anular Suscripción\\n\n                              </button>\\n\n                              <button onclick=\"Livewire.dispatch('showEditNegocio', [8])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Editar Negocio\\n\n                              </button>\n          \"\"\"\n        \"tooltip\" => null\n        \"icon\" => array:6 [\n          \"iconUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-green.png\"\n          \"shadowUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-shadow.png\"\n          \"iconSize\" => array:2 [ …2]\n          \"iconAnchor\" => array:2 [ …2]\n          \"popupAnchor\" => array:2 [ …2]\n          \"shadowSize\" => array:2 [ …2]\n        ]\n        \"callback\" => \"\"\n        \"type\" => \"marker\"\n      ]\n      8 => array:8 [\n        \"id\" => \"9\"\n        \"lat\" => 36.995297\n        \"lng\" => -6.537181\n        \"popup\" => \"\"\"\n          <b>El Rincón de las Tapas</b><br/>\\n\n                              <span style=\"color: green;\">\\n\n                                  Suscripción Activa Hasta 15/07/2025\\n\n                              </span><br/>\\n\n                              <button onclick=\"Livewire.dispatch('cambiarSuscripcion', [9])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#dc3545;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Anular Suscripción\\n\n                              </button>\\n\n                              <button onclick=\"Livewire.dispatch('showEditNegocio', [9])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Editar Negocio\\n\n                              </button>\n          \"\"\"\n        \"tooltip\" => null\n        \"icon\" => array:6 [\n          \"iconUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-green.png\"\n          \"shadowUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-shadow.png\"\n          \"iconSize\" => array:2 [ …2]\n          \"iconAnchor\" => array:2 [ …2]\n          \"popupAnchor\" => array:2 [ …2]\n          \"shadowSize\" => array:2 [ …2]\n        ]\n        \"callback\" => \"\"\n        \"type\" => \"marker\"\n      ]\n      9 => array:8 [\n        \"id\" => \"10\"\n        \"lat\" => 36.986461\n        \"lng\" => -6.527904\n        \"popup\" => \"\"\"\n          <b>Heladería Delicias Frías</b><br/>\\n\n                              <span style=\"color: green;\">\\n\n                                  Suscripción Activa Hasta 28/08/2025\\n\n                              </span><br/>\\n\n                              <button onclick=\"Livewire.dispatch('cambiarSuscripcion', [10])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#dc3545;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Anular Suscripción\\n\n                              </button>\\n\n                              <button onclick=\"Livewire.dispatch('showEditNegocio', [10])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Editar Negocio\\n\n                              </button>\n          \"\"\"\n        \"tooltip\" => null\n        \"icon\" => array:6 [\n          \"iconUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-green.png\"\n          \"shadowUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-shadow.png\"\n          \"iconSize\" => array:2 [ …2]\n          \"iconAnchor\" => array:2 [ …2]\n          \"popupAnchor\" => array:2 [ …2]\n          \"shadowSize\" => array:2 [ …2]\n        ]\n        \"callback\" => \"\"\n        \"type\" => \"marker\"\n      ]\n      10 => array:8 [\n        \"id\" => \"11\"\n        \"lat\" => 37.003775\n        \"lng\" => -6.554554\n        \"popup\" => \"\"\"\n          <b>Cafetería Tarde de Sol</b><br/>\\n\n                              <span style=\"color: green;\">\\n\n                                  Suscripción Activa Hasta 21/08/2025\\n\n                              </span><br/>\\n\n                              <button onclick=\"Livewire.dispatch('cambiarSuscripcion', [11])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#dc3545;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Anular Suscripción\\n\n                              </button>\\n\n                              <button onclick=\"Livewire.dispatch('showEditNegocio', [11])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Editar Negocio\\n\n                              </button>\n          \"\"\"\n        \"tooltip\" => null\n        \"icon\" => array:6 [\n          \"iconUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-green.png\"\n          \"shadowUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-shadow.png\"\n          \"iconSize\" => array:2 [ …2]\n          \"iconAnchor\" => array:2 [ …2]\n          \"popupAnchor\" => array:2 [ …2]\n          \"shadowSize\" => array:2 [ …2]\n        ]\n        \"callback\" => \"\"\n        \"type\" => \"marker\"\n      ]\n      11 => array:8 [\n        \"id\" => \"12\"\n        \"lat\" => 37.007537\n        \"lng\" => -6.554835\n        \"popup\" => \"\"\"\n          <b>Heladería Dulce Nieve</b><br/>\\n\n                              <span style=\"color: green;\">\\n\n                                  Suscripción Activa Hasta 10/07/2025\\n\n                              </span><br/>\\n\n                              <button onclick=\"Livewire.dispatch('cambiarSuscripcion', [12])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#dc3545;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Anular Suscripción\\n\n                              </button>\\n\n                              <button onclick=\"Livewire.dispatch('showEditNegocio', [12])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Editar Negocio\\n\n                              </button>\n          \"\"\"\n        \"tooltip\" => null\n        \"icon\" => array:6 [\n          \"iconUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-green.png\"\n          \"shadowUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-shadow.png\"\n          \"iconSize\" => array:2 [ …2]\n          \"iconAnchor\" => array:2 [ …2]\n          \"popupAnchor\" => array:2 [ …2]\n          \"shadowSize\" => array:2 [ …2]\n        ]\n        \"callback\" => \"\"\n        \"type\" => \"marker\"\n      ]\n      12 => array:8 [\n        \"id\" => \"13\"\n        \"lat\" => 36.99308\n        \"lng\" => -6.544465\n        \"popup\" => \"\"\"\n          <b>Bar Piña Colada</b><br/>\\n\n                              <span style=\"color: green;\">\\n\n                                  Suscripción Activa Hasta 11/08/2025\\n\n                              </span><br/>\\n\n                              <button onclick=\"Livewire.dispatch('cambiarSuscripcion', [13])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#dc3545;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Anular Suscripción\\n\n                              </button>\\n\n                              <button onclick=\"Livewire.dispatch('showEditNegocio', [13])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Editar Negocio\\n\n                              </button>\n          \"\"\"\n        \"tooltip\" => null\n        \"icon\" => array:6 [\n          \"iconUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-green.png\"\n          \"shadowUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-shadow.png\"\n          \"iconSize\" => array:2 [ …2]\n          \"iconAnchor\" => array:2 [ …2]\n          \"popupAnchor\" => array:2 [ …2]\n          \"shadowSize\" => array:2 [ …2]\n        ]\n        \"callback\" => \"\"\n        \"type\" => \"marker\"\n      ]\n      13 => array:8 [\n        \"id\" => \"14\"\n        \"lat\" => 36.990614\n        \"lng\" => -6.534405\n        \"popup\" => \"\"\"\n          <b>Bar Sabor Caribe</b><br/>\\n\n                              <span style=\"color: green;\">\\n\n                                  Suscripción Activa Hasta 12/09/2025\\n\n                              </span><br/>\\n\n                              <button onclick=\"Livewire.dispatch('cambiarSuscripcion', [14])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#dc3545;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Anular Suscripción\\n\n                              </button>\\n\n                              <button onclick=\"Livewire.dispatch('showEditNegocio', [14])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Editar Negocio\\n\n                              </button>\n          \"\"\"\n        \"tooltip\" => null\n        \"icon\" => array:6 [\n          \"iconUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-green.png\"\n          \"shadowUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-shadow.png\"\n          \"iconSize\" => array:2 [ …2]\n          \"iconAnchor\" => array:2 [ …2]\n          \"popupAnchor\" => array:2 [ …2]\n          \"shadowSize\" => array:2 [ …2]\n        ]\n        \"callback\" => \"\"\n        \"type\" => \"marker\"\n      ]\n      14 => array:8 [\n        \"id\" => \"15\"\n        \"lat\" => 37.008848\n        \"lng\" => -6.558051\n        \"popup\" => \"\"\"\n          <b>Bar Coco Loco</b><br/>\\n\n                              <span style=\"color: green;\">\\n\n                                  Suscripción Activa Hasta 15/05/2025\\n\n                              </span><br/>\\n\n                              <button onclick=\"Livewire.dispatch('cambiarSuscripcion', [15])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#dc3545;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Anular Suscripción\\n\n                              </button>\\n\n                              <button onclick=\"Livewire.dispatch('showEditNegocio', [15])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Editar Negocio\\n\n                              </button>\n          \"\"\"\n        \"tooltip\" => null\n        \"icon\" => array:6 [\n          \"iconUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-green.png\"\n          \"shadowUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-shadow.png\"\n          \"iconSize\" => array:2 [ …2]\n          \"iconAnchor\" => array:2 [ …2]\n          \"popupAnchor\" => array:2 [ …2]\n          \"shadowSize\" => array:2 [ …2]\n        ]\n        \"callback\" => \"\"\n        \"type\" => \"marker\"\n      ]\n      15 => array:8 [\n        \"id\" => \"16\"\n        \"lat\" => 37.009503\n        \"lng\" => -6.559731\n        \"popup\" => \"\"\"\n          <b>Complementos Bahía</b><br/>\\n\n                              <span style=\"color: green;\">\\n\n                                  Suscripción Activa Hasta 22/09/2025\\n\n                              </span><br/>\\n\n                              <button onclick=\"Livewire.dispatch('cambiarSuscripcion', [16])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#dc3545;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Anular Suscripción\\n\n                              </button>\\n\n                              <button onclick=\"Livewire.dispatch('showEditNegocio', [16])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Editar Negocio\\n\n                              </button>\n          \"\"\"\n        \"tooltip\" => null\n        \"icon\" => array:6 [\n          \"iconUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-green.png\"\n          \"shadowUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-shadow.png\"\n          \"iconSize\" => array:2 [ …2]\n          \"iconAnchor\" => array:2 [ …2]\n          \"popupAnchor\" => array:2 [ …2]\n          \"shadowSize\" => array:2 [ …2]\n        ]\n        \"callback\" => \"\"\n        \"type\" => \"marker\"\n      ]\n      16 => array:8 [\n        \"id\" => \"17\"\n        \"lat\" => 37.003782\n        \"lng\" => -6.55594\n        \"popup\" => \"\"\"\n          <b>Accesorios Playa Azul</b><br/>\\n\n                              <span style=\"color: green;\">\\n\n                                  Suscripción Activa Hasta 07/09/2025\\n\n                              </span><br/>\\n\n                              <button onclick=\"Livewire.dispatch('cambiarSuscripcion', [17])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#dc3545;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Anular Suscripción\\n\n                              </button>\\n\n                              <button onclick=\"Livewire.dispatch('showEditNegocio', [17])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Editar Negocio\\n\n                              </button>\n          \"\"\"\n        \"tooltip\" => null\n        \"icon\" => array:6 [\n          \"iconUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-green.png\"\n          \"shadowUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-shadow.png\"\n          \"iconSize\" => array:2 [ …2]\n          \"iconAnchor\" => array:2 [ …2]\n          \"popupAnchor\" => array:2 [ …2]\n          \"shadowSize\" => array:2 [ …2]\n        ]\n        \"callback\" => \"\"\n        \"type\" => \"marker\"\n      ]\n      17 => array:8 [\n        \"id\" => \"18\"\n        \"lat\" => 36.988392\n        \"lng\" => -6.524959\n        \"popup\" => \"\"\"\n          <b>Moda Playera</b><br/>\\n\n                              <span style=\"color: green;\">\\n\n                                  Suscripción Activa Hasta 09/10/2025\\n\n                              </span><br/>\\n\n                              <button onclick=\"Livewire.dispatch('cambiarSuscripcion', [18])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#dc3545;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Anular Suscripción\\n\n                              </button>\\n\n                              <button onclick=\"Livewire.dispatch('showEditNegocio', [18])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Editar Negocio\\n\n                              </button>\n          \"\"\"\n        \"tooltip\" => null\n        \"icon\" => array:6 [\n          \"iconUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-green.png\"\n          \"shadowUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-shadow.png\"\n          \"iconSize\" => array:2 [ …2]\n          \"iconAnchor\" => array:2 [ …2]\n          \"popupAnchor\" => array:2 [ …2]\n          \"shadowSize\" => array:2 [ …2]\n        ]\n        \"callback\" => \"\"\n        \"type\" => \"marker\"\n      ]\n      18 => array:8 [\n        \"id\" => \"19\"\n        \"lat\" => 36.999112\n        \"lng\" => -6.55526\n        \"popup\" => \"\"\"\n          <b>Recuerdos del Mar</b><br/>\\n\n                              <span style=\"color: green;\">\\n\n                                  Suscripción Activa Hasta 15/08/2025\\n\n                              </span><br/>\\n\n                              <button onclick=\"Livewire.dispatch('cambiarSuscripcion', [19])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#dc3545;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Anular Suscripción\\n\n                              </button>\\n\n                              <button onclick=\"Livewire.dispatch('showEditNegocio', [19])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Editar Negocio\\n\n                              </button>\n          \"\"\"\n        \"tooltip\" => null\n        \"icon\" => array:6 [\n          \"iconUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-green.png\"\n          \"shadowUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-shadow.png\"\n          \"iconSize\" => array:2 [ …2]\n          \"iconAnchor\" => array:2 [ …2]\n          \"popupAnchor\" => array:2 [ …2]\n          \"shadowSize\" => array:2 [ …2]\n        ]\n        \"callback\" => \"\"\n        \"type\" => \"marker\"\n      ]\n      19 => array:8 [\n        \"id\" => \"20\"\n        \"lat\" => 37.004529\n        \"lng\" => -6.552267\n        \"popup\" => \"\"\"\n          <b>Regalos Playa Sur</b><br/>\\n\n                              <span style=\"color: green;\">\\n\n                                  Suscripción Activa Hasta 25/09/2025\\n\n                              </span><br/>\\n\n                              <button onclick=\"Livewire.dispatch('cambiarSuscripcion', [20])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#dc3545;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Anular Suscripción\\n\n                              </button>\\n\n                              <button onclick=\"Livewire.dispatch('showEditNegocio', [20])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Editar Negocio\\n\n                              </button>\n          \"\"\"\n        \"tooltip\" => null\n        \"icon\" => array:6 [\n          \"iconUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-green.png\"\n          \"shadowUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-shadow.png\"\n          \"iconSize\" => array:2 [ …2]\n          \"iconAnchor\" => array:2 [ …2]\n          \"popupAnchor\" => array:2 [ …2]\n          \"shadowSize\" => array:2 [ …2]\n        ]\n        \"callback\" => \"\"\n        \"type\" => \"marker\"\n      ]\n      20 => array:8 [\n        \"id\" => \"21\"\n        \"lat\" => 36.985428\n        \"lng\" => -6.529028\n        \"popup\" => \"\"\"\n          <b>Regalos y Recuerdos Oceanía</b><br/>\\n\n                              <span style=\"color: green;\">\\n\n                                  Suscripción Activa Hasta 07/08/2025\\n\n                              </span><br/>\\n\n                              <button onclick=\"Livewire.dispatch('cambiarSuscripcion', [21])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#dc3545;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Anular Suscripción\\n\n                              </button>\\n\n                              <button onclick=\"Livewire.dispatch('showEditNegocio', [21])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Editar Negocio\\n\n                              </button>\n          \"\"\"\n        \"tooltip\" => null\n        \"icon\" => array:6 [\n          \"iconUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-green.png\"\n          \"shadowUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-shadow.png\"\n          \"iconSize\" => array:2 [ …2]\n          \"iconAnchor\" => array:2 [ …2]\n          \"popupAnchor\" => array:2 [ …2]\n          \"shadowSize\" => array:2 [ …2]\n        ]\n        \"callback\" => \"\"\n        \"type\" => \"marker\"\n      ]\n      21 => array:8 [\n        \"id\" => \"22\"\n        \"lat\" => 37.00053\n        \"lng\" => -6.557429\n        \"popup\" => \"\"\"\n          <b>Windsurf School Mar Azul</b><br/>\\n\n                              <span style=\"color: green;\">\\n\n                                  Suscripción Activa Hasta 23/08/2025\\n\n                              </span><br/>\\n\n                              <button onclick=\"Livewire.dispatch('cambiarSuscripcion', [22])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#dc3545;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Anular Suscripción\\n\n                              </button>\\n\n                              <button onclick=\"Livewire.dispatch('showEditNegocio', [22])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Editar Negocio\\n\n                              </button>\n          \"\"\"\n        \"tooltip\" => null\n        \"icon\" => array:6 [\n          \"iconUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-green.png\"\n          \"shadowUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-shadow.png\"\n          \"iconSize\" => array:2 [ …2]\n          \"iconAnchor\" => array:2 [ …2]\n          \"popupAnchor\" => array:2 [ …2]\n          \"shadowSize\" => array:2 [ …2]\n        ]\n        \"callback\" => \"\"\n        \"type\" => \"marker\"\n      ]\n      22 => array:8 [\n        \"id\" => \"23\"\n        \"lat\" => 37.001803\n        \"lng\" => -6.544078\n        \"popup\" => \"\"\"\n          <b>Flyboard Beach Fun</b><br/>\\n\n                              <span style=\"color: green;\">\\n\n                                  Suscripción Activa Hasta 29/07/2025\\n\n                              </span><br/>\\n\n                              <button onclick=\"Livewire.dispatch('cambiarSuscripcion', [23])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#dc3545;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Anular Suscripción\\n\n                              </button>\\n\n                              <button onclick=\"Livewire.dispatch('showEditNegocio', [23])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Editar Negocio\\n\n                              </button>\n          \"\"\"\n        \"tooltip\" => null\n        \"icon\" => array:6 [\n          \"iconUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-green.png\"\n          \"shadowUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-shadow.png\"\n          \"iconSize\" => array:2 [ …2]\n          \"iconAnchor\" => array:2 [ …2]\n          \"popupAnchor\" => array:2 [ …2]\n          \"shadowSize\" => array:2 [ …2]\n        ]\n        \"callback\" => \"\"\n        \"type\" => \"marker\"\n      ]\n      23 => array:8 [\n        \"id\" => \"24\"\n        \"lat\" => 36.989609\n        \"lng\" => -6.525775\n        \"popup\" => \"\"\"\n          <b>Paddle Surf Atlántico</b><br/>\\n\n                              <span style=\"color: green;\">\\n\n                                  Suscripción Activa Hasta 10/09/2025\\n\n                              </span><br/>\\n\n                              <button onclick=\"Livewire.dispatch('cambiarSuscripcion', [24])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#dc3545;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Anular Suscripción\\n\n                              </button>\\n\n                              <button onclick=\"Livewire.dispatch('showEditNegocio', [24])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Editar Negocio\\n\n                              </button>\n          \"\"\"\n        \"tooltip\" => null\n        \"icon\" => array:6 [\n          \"iconUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-green.png\"\n          \"shadowUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-shadow.png\"\n          \"iconSize\" => array:2 [ …2]\n          \"iconAnchor\" => array:2 [ …2]\n          \"popupAnchor\" => array:2 [ …2]\n          \"shadowSize\" => array:2 [ …2]\n        ]\n        \"callback\" => \"\"\n        \"type\" => \"marker\"\n      ]\n      24 => array:8 [\n        \"id\" => \"25\"\n        \"lat\" => 36.997168\n        \"lng\" => -6.538684\n        \"popup\" => \"\"\"\n          <b>Ruta Nocturna Mar y Luna</b><br/>\\n\n                              <span style=\"color: green;\">\\n\n                                  Suscripción Activa Hasta 12/05/2025\\n\n                              </span><br/>\\n\n                              <button onclick=\"Livewire.dispatch('cambiarSuscripcion', [25])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#dc3545;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Anular Suscripción\\n\n                              </button>\\n\n                              <button onclick=\"Livewire.dispatch('showEditNegocio', [25])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Editar Negocio\\n\n                              </button>\n          \"\"\"\n        \"tooltip\" => null\n        \"icon\" => array:6 [\n          \"iconUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-green.png\"\n          \"shadowUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-shadow.png\"\n          \"iconSize\" => array:2 [ …2]\n          \"iconAnchor\" => array:2 [ …2]\n          \"popupAnchor\" => array:2 [ …2]\n          \"shadowSize\" => array:2 [ …2]\n        ]\n        \"callback\" => \"\"\n        \"type\" => \"marker\"\n      ]\n      25 => array:8 [\n        \"id\" => \"26\"\n        \"lat\" => 36.987619\n        \"lng\" => -6.535322\n        \"popup\" => \"\"\"\n          <b>Ruta Faro del Finisterre</b><br/>\\n\n                              <span style=\"color: green;\">\\n\n                                  Suscripción Activa Hasta 06/07/2025\\n\n                              </span><br/>\\n\n                              <button onclick=\"Livewire.dispatch('cambiarSuscripcion', [26])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#dc3545;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Anular Suscripción\\n\n                              </button>\\n\n                              <button onclick=\"Livewire.dispatch('showEditNegocio', [26])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Editar Negocio\\n\n                              </button>\n          \"\"\"\n        \"tooltip\" => null\n        \"icon\" => array:6 [\n          \"iconUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-green.png\"\n          \"shadowUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-shadow.png\"\n          \"iconSize\" => array:2 [ …2]\n          \"iconAnchor\" => array:2 [ …2]\n          \"popupAnchor\" => array:2 [ …2]\n          \"shadowSize\" => array:2 [ …2]\n        ]\n        \"callback\" => \"\"\n        \"type\" => \"marker\"\n      ]\n      26 => array:8 [\n        \"id\" => \"27\"\n        \"lat\" => 37.003477\n        \"lng\" => -6.559385\n        \"popup\" => \"\"\"\n          <b>Excursión Rocío Mágico</b><br/>\\n\n                              <span style=\"color: green;\">\\n\n                                  Suscripción Activa Hasta 06/08/2025\\n\n                              </span><br/>\\n\n                              <button onclick=\"Livewire.dispatch('cambiarSuscripcion', [27])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#dc3545;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Anular Suscripción\\n\n                              </button>\\n\n                              <button onclick=\"Livewire.dispatch('showEditNegocio', [27])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Editar Negocio\\n\n                              </button>\n          \"\"\"\n        \"tooltip\" => null\n        \"icon\" => array:6 [\n          \"iconUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-green.png\"\n          \"shadowUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-shadow.png\"\n          \"iconSize\" => array:2 [ …2]\n          \"iconAnchor\" => array:2 [ …2]\n          \"popupAnchor\" => array:2 [ …2]\n          \"shadowSize\" => array:2 [ …2]\n        ]\n        \"callback\" => \"\"\n        \"type\" => \"marker\"\n      ]\n      27 => array:8 [\n        \"id\" => \"28\"\n        \"lat\" => 36.996988\n        \"lng\" => -6.545094\n        \"popup\" => \"\"\"\n          <b>Peluquería Brisa Marina</b><br/>\\n\n                              <span style=\"color: green;\">\\n\n                                  Suscripción Activa Hasta 02/06/2025\\n\n                              </span><br/>\\n\n                              <button onclick=\"Livewire.dispatch('cambiarSuscripcion', [28])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#dc3545;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Anular Suscripción\\n\n                              </button>\\n\n                              <button onclick=\"Livewire.dispatch('showEditNegocio', [28])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Editar Negocio\\n\n                              </button>\n          \"\"\"\n        \"tooltip\" => null\n        \"icon\" => array:6 [\n          \"iconUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-green.png\"\n          \"shadowUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-shadow.png\"\n          \"iconSize\" => array:2 [ …2]\n          \"iconAnchor\" => array:2 [ …2]\n          \"popupAnchor\" => array:2 [ …2]\n          \"shadowSize\" => array:2 [ …2]\n        ]\n        \"callback\" => \"\"\n        \"type\" => \"marker\"\n      ]\n      28 => array:8 [\n        \"id\" => \"29\"\n        \"lat\" => 37.008775\n        \"lng\" => -6.562643\n        \"popup\" => \"\"\"\n          <b>Imagen Beach</b><br/>\\n\n                              <span style=\"color: green;\">\\n\n                                  Suscripción Activa Hasta 21/07/2025\\n\n                              </span><br/>\\n\n                              <button onclick=\"Livewire.dispatch('cambiarSuscripcion', [29])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#dc3545;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Anular Suscripción\\n\n                              </button>\\n\n                              <button onclick=\"Livewire.dispatch('showEditNegocio', [29])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Editar Negocio\\n\n                              </button>\n          \"\"\"\n        \"tooltip\" => null\n        \"icon\" => array:6 [\n          \"iconUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-green.png\"\n          \"shadowUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-shadow.png\"\n          \"iconSize\" => array:2 [ …2]\n          \"iconAnchor\" => array:2 [ …2]\n          \"popupAnchor\" => array:2 [ …2]\n          \"shadowSize\" => array:2 [ …2]\n        ]\n        \"callback\" => \"\"\n        \"type\" => \"marker\"\n      ]\n      29 => array:8 [\n        \"id\" => \"30\"\n        \"lat\" => 37.008041\n        \"lng\" => -6.564848\n        \"popup\" => \"\"\"\n          <b>Estética & Peluquería Mar</b><br/>\\n\n                              <span style=\"color: green;\">\\n\n                                  Suscripción Activa Hasta 26/05/2025\\n\n                              </span><br/>\\n\n                              <button onclick=\"Livewire.dispatch('cambiarSuscripcion', [30])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#dc3545;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Anular Suscripción\\n\n                              </button>\\n\n                              <button onclick=\"Livewire.dispatch('showEditNegocio', [30])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Editar Negocio\\n\n                              </button>\n          \"\"\"\n        \"tooltip\" => null\n        \"icon\" => array:6 [\n          \"iconUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-green.png\"\n          \"shadowUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-shadow.png\"\n          \"iconSize\" => array:2 [ …2]\n          \"iconAnchor\" => array:2 [ …2]\n          \"popupAnchor\" => array:2 [ …2]\n          \"shadowSize\" => array:2 [ …2]\n        ]\n        \"callback\" => \"\"\n        \"type\" => \"marker\"\n      ]\n      30 => array:8 [\n        \"id\" => \"31\"\n        \"lat\" => 36.990021\n        \"lng\" => -6.523403\n        \"popup\" => \"\"\"\n          <b>Taxi Express Playa</b><br/>\\n\n                              <span style=\"color: green;\">\\n\n                                  Suscripción Activa Hasta 04/09/2025\\n\n                              </span><br/>\\n\n                              <button onclick=\"Livewire.dispatch('cambiarSuscripcion', [31])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#dc3545;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Anular Suscripción\\n\n                              </button>\\n\n                              <button onclick=\"Livewire.dispatch('showEditNegocio', [31])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Editar Negocio\\n\n                              </button>\n          \"\"\"\n        \"tooltip\" => null\n        \"icon\" => array:6 [\n          \"iconUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-green.png\"\n          \"shadowUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-shadow.png\"\n          \"iconSize\" => array:2 [ …2]\n          \"iconAnchor\" => array:2 [ …2]\n          \"popupAnchor\" => array:2 [ …2]\n          \"shadowSize\" => array:2 [ …2]\n        ]\n        \"callback\" => \"\"\n        \"type\" => \"marker\"\n      ]\n      31 => array:8 [\n        \"id\" => \"32\"\n        \"lat\" => 37.006685\n        \"lng\" => -6.557307\n        \"popup\" => \"\"\"\n          <b>Taxi Puesta de Sol</b><br/>\\n\n                              <span style=\"color: green;\">\\n\n                                  Suscripción Activa Hasta 25/05/2025\\n\n                              </span><br/>\\n\n                              <button onclick=\"Livewire.dispatch('cambiarSuscripcion', [32])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#dc3545;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Anular Suscripción\\n\n                              </button>\\n\n                              <button onclick=\"Livewire.dispatch('showEditNegocio', [32])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Editar Negocio\\n\n                              </button>\n          \"\"\"\n        \"tooltip\" => null\n        \"icon\" => array:6 [\n          \"iconUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-green.png\"\n          \"shadowUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-shadow.png\"\n          \"iconSize\" => array:2 [ …2]\n          \"iconAnchor\" => array:2 [ …2]\n          \"popupAnchor\" => array:2 [ …2]\n          \"shadowSize\" => array:2 [ …2]\n        ]\n        \"callback\" => \"\"\n        \"type\" => \"marker\"\n      ]\n      32 => array:8 [\n        \"id\" => \"33\"\n        \"lat\" => 36.990319\n        \"lng\" => -6.540966\n        \"popup\" => \"\"\"\n          <b>Taxi Faro Sur</b><br/>\\n\n                              <span style=\"color: green;\">\\n\n                                  Suscripción Activa Hasta 17/06/2025\\n\n                              </span><br/>\\n\n                              <button onclick=\"Livewire.dispatch('cambiarSuscripcion', [33])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#dc3545;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Anular Suscripción\\n\n                              </button>\\n\n                              <button onclick=\"Livewire.dispatch('showEditNegocio', [33])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Editar Negocio\\n\n                              </button>\n          \"\"\"\n        \"tooltip\" => null\n        \"icon\" => array:6 [\n          \"iconUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-green.png\"\n          \"shadowUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-shadow.png\"\n          \"iconSize\" => array:2 [ …2]\n          \"iconAnchor\" => array:2 [ …2]\n          \"popupAnchor\" => array:2 [ …2]\n          \"shadowSize\" => array:2 [ …2]\n        ]\n        \"callback\" => \"\"\n        \"type\" => \"marker\"\n      ]\n      33 => array:8 [\n        \"id\" => \"34\"\n        \"lat\" => 36.987686\n        \"lng\" => -6.532654\n        \"popup\" => \"\"\"\n          <b>Hotel Vista Mar</b><br/>\\n\n                              <span style=\"color: green;\">\\n\n                                  Suscripción Activa Hasta 02/06/2025\\n\n                              </span><br/>\\n\n                              <button onclick=\"Livewire.dispatch('cambiarSuscripcion', [34])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#dc3545;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Anular Suscripción\\n\n                              </button>\\n\n                              <button onclick=\"Livewire.dispatch('showEditNegocio', [34])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Editar Negocio\\n\n                              </button>\n          \"\"\"\n        \"tooltip\" => null\n        \"icon\" => array:6 [\n          \"iconUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-green.png\"\n          \"shadowUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-shadow.png\"\n          \"iconSize\" => array:2 [ …2]\n          \"iconAnchor\" => array:2 [ …2]\n          \"popupAnchor\" => array:2 [ …2]\n          \"shadowSize\" => array:2 [ …2]\n        ]\n        \"callback\" => \"\"\n        \"type\" => \"marker\"\n      ]\n      34 => array:8 [\n        \"id\" => \"35\"\n        \"lat\" => 36.985309\n        \"lng\" => -6.532355\n        \"popup\" => \"\"\"\n          <b>Resort Costa Dorada</b><br/>\\n\n                              <span style=\"color: green;\">\\n\n                                  Suscripción Activa Hasta 12/06/2025\\n\n                              </span><br/>\\n\n                              <button onclick=\"Livewire.dispatch('cambiarSuscripcion', [35])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#dc3545;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Anular Suscripción\\n\n                              </button>\\n\n                              <button onclick=\"Livewire.dispatch('showEditNegocio', [35])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Editar Negocio\\n\n                              </button>\n          \"\"\"\n        \"tooltip\" => null\n        \"icon\" => array:6 [\n          \"iconUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-green.png\"\n          \"shadowUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-shadow.png\"\n          \"iconSize\" => array:2 [ …2]\n          \"iconAnchor\" => array:2 [ …2]\n          \"popupAnchor\" => array:2 [ …2]\n          \"shadowSize\" => array:2 [ …2]\n        ]\n        \"callback\" => \"\"\n        \"type\" => \"marker\"\n      ]\n      35 => array:8 [\n        \"id\" => \"36\"\n        \"lat\" => 37.003746\n        \"lng\" => -6.551682\n        \"popup\" => \"\"\"\n          <b>Bungalows Playa Real</b><br/>\\n\n                              <span style=\"color: green;\">\\n\n                                  Suscripción Activa Hasta 25/05/2025\\n\n                              </span><br/>\\n\n                              <button onclick=\"Livewire.dispatch('cambiarSuscripcion', [36])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#dc3545;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Anular Suscripción\\n\n                              </button>\\n\n                              <button onclick=\"Livewire.dispatch('showEditNegocio', [36])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Editar Negocio\\n\n                              </button>\n          \"\"\"\n        \"tooltip\" => null\n        \"icon\" => array:6 [\n          \"iconUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-green.png\"\n          \"shadowUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-shadow.png\"\n          \"iconSize\" => array:2 [ …2]\n          \"iconAnchor\" => array:2 [ …2]\n          \"popupAnchor\" => array:2 [ …2]\n          \"shadowSize\" => array:2 [ …2]\n        ]\n        \"callback\" => \"\"\n        \"type\" => \"marker\"\n      ]\n      36 => array:8 [\n        \"id\" => \"37\"\n        \"lat\" => 37.004793\n        \"lng\" => -6.554851\n        \"popup\" => \"\"\"\n          <b>Supermercado Costero</b><br/>\\n\n                              <span style=\"color: green;\">\\n\n                                  Suscripción Activa Hasta 10/07/2025\\n\n                              </span><br/>\\n\n                              <button onclick=\"Livewire.dispatch('cambiarSuscripcion', [37])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#dc3545;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Anular Suscripción\\n\n                              </button>\\n\n                              <button onclick=\"Livewire.dispatch('showEditNegocio', [37])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Editar Negocio\\n\n                              </button>\n          \"\"\"\n        \"tooltip\" => null\n        \"icon\" => array:6 [\n          \"iconUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-green.png\"\n          \"shadowUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-shadow.png\"\n          \"iconSize\" => array:2 [ …2]\n          \"iconAnchor\" => array:2 [ …2]\n          \"popupAnchor\" => array:2 [ …2]\n          \"shadowSize\" => array:2 [ …2]\n        ]\n        \"callback\" => \"\"\n        \"type\" => \"marker\"\n      ]\n      37 => array:8 [\n        \"id\" => \"38\"\n        \"lat\" => 37.00669\n        \"lng\" => -6.556338\n        \"popup\" => \"\"\"\n          <b>Alimentación Del Mar</b><br/>\\n\n                              <span style=\"color: green;\">\\n\n                                  Suscripción Activa Hasta 29/09/2025\\n\n                              </span><br/>\\n\n                              <button onclick=\"Livewire.dispatch('cambiarSuscripcion', [38])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#dc3545;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Anular Suscripción\\n\n                              </button>\\n\n                              <button onclick=\"Livewire.dispatch('showEditNegocio', [38])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Editar Negocio\\n\n                              </button>\n          \"\"\"\n        \"tooltip\" => null\n        \"icon\" => array:6 [\n          \"iconUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-green.png\"\n          \"shadowUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-shadow.png\"\n          \"iconSize\" => array:2 [ …2]\n          \"iconAnchor\" => array:2 [ …2]\n          \"popupAnchor\" => array:2 [ …2]\n          \"shadowSize\" => array:2 [ …2]\n        ]\n        \"callback\" => \"\"\n        \"type\" => \"marker\"\n      ]\n      38 => array:8 [\n        \"id\" => \"39\"\n        \"lat\" => 36.991446\n        \"lng\" => -6.542806\n        \"popup\" => \"\"\"\n          <b>Mercado Fresco Costa Sur</b><br/>\\n\n                              <span style=\"color: red;\">\\n\n                                  Suscripción Inactiva Desde 28/08/2025\\n\n                              </span><br/>\\n\n                              <button onclick=\"Livewire.dispatch('cambiarSuscripcion', [39])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#28a745;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Activar Suscripción\\n\n                              </button>\\n\n                              <button onclick=\"Livewire.dispatch('showEditNegocio', [39])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Editar Negocio\\n\n                              </button>\n          \"\"\"\n        \"tooltip\" => null\n        \"icon\" => array:6 [\n          \"iconUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-red.png\"\n          \"shadowUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-shadow.png\"\n          \"iconSize\" => array:2 [ …2]\n          \"iconAnchor\" => array:2 [ …2]\n          \"popupAnchor\" => array:2 [ …2]\n          \"shadowSize\" => array:2 [ …2]\n        ]\n        \"callback\" => \"\"\n        \"type\" => \"marker\"\n      ]\n      39 => array:8 [\n        \"id\" => \"40\"\n        \"lat\" => 36.998155\n        \"lng\" => -6.545047\n        \"popup\" => \"\"\"\n          <b>Farmacia Atlántico</b><br/>\\n\n                              <span style=\"color: green;\">\\n\n                                  Suscripción Activa Hasta 02/08/2025\\n\n                              </span><br/>\\n\n                              <button onclick=\"Livewire.dispatch('cambiarSuscripcion', [40])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#dc3545;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Anular Suscripción\\n\n                              </button>\\n\n                              <button onclick=\"Livewire.dispatch('showEditNegocio', [40])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Editar Negocio\\n\n                              </button>\n          \"\"\"\n        \"tooltip\" => null\n        \"icon\" => array:6 [\n          \"iconUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-green.png\"\n          \"shadowUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-shadow.png\"\n          \"iconSize\" => array:2 [ …2]\n          \"iconAnchor\" => array:2 [ …2]\n          \"popupAnchor\" => array:2 [ …2]\n          \"shadowSize\" => array:2 [ …2]\n        ]\n        \"callback\" => \"\"\n        \"type\" => \"marker\"\n      ]\n      40 => array:8 [\n        \"id\" => \"41\"\n        \"lat\" => 36.995955\n        \"lng\" => -6.536564\n        \"popup\" => \"\"\"\n          <b>Farmacia Sol y Salud</b><br/>\\n\n                              <span style=\"color: green;\">\\n\n                                  Suscripción Activa Hasta 23/09/2025\\n\n                              </span><br/>\\n\n                              <button onclick=\"Livewire.dispatch('cambiarSuscripcion', [41])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#dc3545;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Anular Suscripción\\n\n                              </button>\\n\n                              <button onclick=\"Livewire.dispatch('showEditNegocio', [41])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Editar Negocio\\n\n                              </button>\n          \"\"\"\n        \"tooltip\" => null\n        \"icon\" => array:6 [\n          \"iconUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-green.png\"\n          \"shadowUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-shadow.png\"\n          \"iconSize\" => array:2 [ …2]\n          \"iconAnchor\" => array:2 [ …2]\n          \"popupAnchor\" => array:2 [ …2]\n          \"shadowSize\" => array:2 [ …2]\n        ]\n        \"callback\" => \"\"\n        \"type\" => \"marker\"\n      ]\n      41 => array:8 [\n        \"id\" => \"42\"\n        \"lat\" => 37.002932\n        \"lng\" => -6.544834\n        \"popup\" => \"\"\"\n          <b>Farmacia del Mar</b><br/>\\n\n                              <span style=\"color: red;\">\\n\n                                  Suscripción Inactiva Desde 12/07/2025\\n\n                              </span><br/>\\n\n                              <button onclick=\"Livewire.dispatch('cambiarSuscripcion', [42])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#28a745;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Activar Suscripción\\n\n                              </button>\\n\n                              <button onclick=\"Livewire.dispatch('showEditNegocio', [42])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Editar Negocio\\n\n                              </button>\n          \"\"\"\n        \"tooltip\" => null\n        \"icon\" => array:6 [\n          \"iconUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-red.png\"\n          \"shadowUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-shadow.png\"\n          \"iconSize\" => array:2 [ …2]\n          \"iconAnchor\" => array:2 [ …2]\n          \"popupAnchor\" => array:2 [ …2]\n          \"shadowSize\" => array:2 [ …2]\n        ]\n        \"callback\" => \"\"\n        \"type\" => \"marker\"\n      ]\n      42 => array:8 [\n        \"id\" => \"43\"\n        \"lat\" => 36.997976\n        \"lng\" => -6.55229\n        \"popup\" => \"\"\"\n          <b>Información Turística Faro</b><br/>\\n\n                              <span style=\"color: green;\">\\n\n                                  Suscripción Activa Hasta 09/09/2025\\n\n                              </span><br/>\\n\n                              <button onclick=\"Livewire.dispatch('cambiarSuscripcion', [43])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#dc3545;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Anular Suscripción\\n\n                              </button>\\n\n                              <button onclick=\"Livewire.dispatch('showEditNegocio', [43])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Editar Negocio\\n\n                              </button>\n          \"\"\"\n        \"tooltip\" => null\n        \"icon\" => array:6 [\n          \"iconUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-green.png\"\n          \"shadowUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-shadow.png\"\n          \"iconSize\" => array:2 [ …2]\n          \"iconAnchor\" => array:2 [ …2]\n          \"popupAnchor\" => array:2 [ …2]\n          \"shadowSize\" => array:2 [ …2]\n        ]\n        \"callback\" => \"\"\n        \"type\" => \"marker\"\n      ]\n      43 => array:8 [\n        \"id\" => \"44\"\n        \"lat\" => 36.995952\n        \"lng\" => -6.535522\n        \"popup\" => \"\"\"\n          <b>Oficina de Turismo Playa Azul</b><br/>\\n\n                              <span style=\"color: red;\">\\n\n                                  Suscripción Inactiva Desde 13/08/2025\\n\n                              </span><br/>\\n\n                              <button onclick=\"Livewire.dispatch('cambiarSuscripcion', [44])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#28a745;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Activar Suscripción\\n\n                              </button>\\n\n                              <button onclick=\"Livewire.dispatch('showEditNegocio', [44])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Editar Negocio\\n\n                              </button>\n          \"\"\"\n        \"tooltip\" => null\n        \"icon\" => array:6 [\n          \"iconUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-red.png\"\n          \"shadowUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-shadow.png\"\n          \"iconSize\" => array:2 [ …2]\n          \"iconAnchor\" => array:2 [ …2]\n          \"popupAnchor\" => array:2 [ …2]\n          \"shadowSize\" => array:2 [ …2]\n        ]\n        \"callback\" => \"\"\n        \"type\" => \"marker\"\n      ]\n      44 => array:8 [\n        \"id\" => \"45\"\n        \"lat\" => 36.993682\n        \"lng\" => -6.531168\n        \"popup\" => \"\"\"\n          <b>Oficina de Servicios Playa</b><br/>\\n\n                              <span style=\"color: green;\">\\n\n                                  Suscripción Activa Hasta 04/06/2025\\n\n                              </span><br/>\\n\n                              <button onclick=\"Livewire.dispatch('cambiarSuscripcion', [45])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#dc3545;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Anular Suscripción\\n\n                              </button>\\n\n                              <button onclick=\"Livewire.dispatch('showEditNegocio', [45])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Editar Negocio\\n\n                              </button>\n          \"\"\"\n        \"tooltip\" => null\n        \"icon\" => array:6 [\n          \"iconUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-green.png\"\n          \"shadowUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-shadow.png\"\n          \"iconSize\" => array:2 [ …2]\n          \"iconAnchor\" => array:2 [ …2]\n          \"popupAnchor\" => array:2 [ …2]\n          \"shadowSize\" => array:2 [ …2]\n        ]\n        \"callback\" => \"\"\n        \"type\" => \"marker\"\n      ]\n      45 => array:8 [\n        \"id\" => \"46\"\n        \"lat\" => 0.0\n        \"lng\" => 0.0\n        \"popup\" => \"\"\"\n          <b>Negocio de prueba</b><br/>\\n\n                              <span style=\"color: red;\">\\n\n                                  Suscripción Inactiva Desde 12/04/2025\\n\n                              </span><br/>\\n\n                              <button onclick=\"Livewire.dispatch('cambiarSuscripcion', [46])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#28a745;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Activar Suscripción\\n\n                              </button>\\n\n                              <button onclick=\"Livewire.dispatch('showEditNegocio', [46])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Editar Negocio\\n\n                              </button>\n          \"\"\"\n        \"tooltip\" => null\n        \"icon\" => array:6 [\n          \"iconUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-red.png\"\n          \"shadowUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-shadow.png\"\n          \"iconSize\" => array:2 [ …2]\n          \"iconAnchor\" => array:2 [ …2]\n          \"popupAnchor\" => array:2 [ …2]\n          \"shadowSize\" => array:2 [ …2]\n        ]\n        \"callback\" => \"\"\n        \"type\" => \"marker\"\n      ]\n      46 => array:8 [\n        \"id\" => \"47\"\n        \"lat\" => 0.0\n        \"lng\" => 0.0\n        \"popup\" => \"\"\"\n          <b>Negocio de prueba</b><br/>\\n\n                              <span style=\"color: red;\">\\n\n                                  Suscripción Inactiva Desde 12/04/2025\\n\n                              </span><br/>\\n\n                              <button onclick=\"Livewire.dispatch('cambiarSuscripcion', [47])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#28a745;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Activar Suscripción\\n\n                              </button>\\n\n                              <button onclick=\"Livewire.dispatch('showEditNegocio', [47])\" \\n\n                                  style='margin-top:5px;padding:5px 10px;background:#007bff;color:white;border:none;border-radius:4px;cursor:pointer;'>\\n\n                                  Editar Negocio\\n\n                              </button>\n          \"\"\"\n        \"tooltip\" => null\n        \"icon\" => array:6 [\n          \"iconUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-red.png\"\n          \"shadowUrl\" => \"https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-shadow.png\"\n          \"iconSize\" => array:2 [ …2]\n          \"iconAnchor\" => array:2 [ …2]\n          \"popupAnchor\" => array:2 [ …2]\n          \"shadowSize\" => array:2 [ …2]\n        ]\n        \"callback\" => \"\"\n        \"type\" => \"marker\"\n      ]\n    ]\n    \"polylines\" => []\n    \"polygones\" => []\n    \"rectangles\" => []\n    \"circles\" => []\n  ]\n  \"name\" => \"app.filament.widgets.negocios-map\"\n  \"component\" => \"App\\Filament\\Widgets\\NegociosMap\"\n  \"id\" => \"wPI8y5bMKAWBS8UWpNoo\"\n]"}, "count": 2}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 2, "messages": [{"message": "[\n  ability => system.access-panel,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1453777819 data-indent-pad=\"  \"><span class=sf-dump-note>system.access-panel </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">system.access-panel</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1453777819\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.483683, "xdebug_link": null}, {"message": "[\n  ability => system.access-panel,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-669490203 data-indent-pad=\"  \"><span class=sf-dump-note>system.access-panel </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">system.access-panel</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-669490203\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.501078, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "https://mia.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate<a href=\"phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FHandleRequests%2FHandleRequests.php&line=79\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/livewire/livewire/src/Mechanisms/HandleRequests/HandleRequests.php:79-110</a>", "middleware": "web", "duration": "2.86s", "peak_memory": "54MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1356641193 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1356641193\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-951529854 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">AgJTb7TMVHyj2iNWz2N8rcvkS7xhFsqvfp5eqrTe</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"677 characters\">{&quot;data&quot;:{&quot;negocios&quot;:null,&quot;filters&quot;:[{&quot;localidad&quot;:null,&quot;zona&quot;:null,&quot;categoria&quot;:null,&quot;suscripcion&quot;:null},{&quot;s&quot;:&quot;arr&quot;}],&quot;componentFileAttachments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActions&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsArguments&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsData&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}],&quot;mountedFormComponentActionsComponents&quot;:[[],{&quot;s&quot;:&quot;arr&quot;}]},&quot;memo&quot;:{&quot;id&quot;:&quot;Q0xgzrnkWgaymMNvbmX5&quot;,&quot;name&quot;:&quot;app.filament.widgets.negocios-map-with-filters&quot;,&quot;path&quot;:&quot;admin&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;lazyLoaded&quot;:false,&quot;lazyIsolated&quot;:true,&quot;errors&quot;:[],&quot;locale&quot;:&quot;es&quot;},&quot;checksum&quot;:&quot;3ef27e68206af3d54ecb2328d48d651a04a1de4aed849cbebab2330ffe63940f&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"10 characters\">__lazyLoad</span>\"\n          \"<span class=sf-dump-key>params</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"248 characters\">eyJkYXRhIjp7ImZvck1vdW50IjpbW10seyJzIjoiYXJyIn1dfSwibWVtbyI6eyJpZCI6InBYTENQU20yRkJZVEI5enJSaDlPIiwibmFtZSI6Il9fbW91bnRQYXJhbXNDb250YWluZXIifSwiY2hlY2tzdW0iOiIyMmM3YTdkNDVhOGU4NDRhMzM5MzExMWEzOTc1OTMyYzQzZDk4NDg0MTAzOGZkZjM0NTFkYTQ4OWU1NThmM2IwIn0=</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-951529854\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-628868102 data-indent-pad=\"  \"><span class=sf-dump-note>array:21</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mia.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1166</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"57 characters\">&quot;Brave&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">es-ES,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">https://mia.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">https://mia.test/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"709 characters\">XSRF-TOKEN=eyJpdiI6IlJmZjZBWExkWWNWWFhBVTV6U2xzVHc9PSIsInZhbHVlIjoicHlNR3BQZ004S0NiYmFJM25FUVpsajBlVkpxM1MyblUzSGhyK1FUdS9oQ0t5YlduSXAva2RmM3QvTytFUnV3ZWJrMGdyWVB0UUVNOVJzRlpHdldLZ0o3NCtmUXFYVmwySkdLSUZ0OVhNOEdhK3VaZlFOWmVnYkNVMjRTcmJPMFQiLCJtYWMiOiI3NjdmY2VkN2VjM2ZmODJlMTdkNmI3MjY5Yzc0ZDJiMzU4YTAzY2I2YmJhMmMxYzZkZWZjYzUzMmJiYzIyZGZkIiwidGFnIjoiIn0%3D; mia_session=eyJpdiI6IjdSTmZFOEpZditmbzhJQlFES3Nad0E9PSIsInZhbHVlIjoiT0RSMnBBTE93ZFE3RVdONWVaSU9pckJkOWpCci8zcll1cHNMWWQwbmdDb1VOTU82SUZIYzI3R1dqdHVkU0hWTkxGS3QyeXp5dllNNDZDaDVraEROOC9FN0VucUJqSXR6cVZDcDN2M25IZTJWUDh0RVp0V1UzaURiL1VCZU5tbDkiLCJtYWMiOiJjNTdhYzY0YzdjYjk3ZTY2ZTIzNjA1YjUyMTQxNzU5MTIwZmUzZTg2NjFiZTMyZWI0YzdlYWQ5ZjYyYWE3NDkzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-628868102\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-455480369 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">AgJTb7TMVHyj2iNWz2N8rcvkS7xhFsqvfp5eqrTe</span>\"\n  \"<span class=sf-dump-key>mia_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">LqdP3Q0O7a3cwoJmYgCvHDyb4m34SlxmUHxhBxPb</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-455480369\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1761986994 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 10:18:40 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1761986994\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1749079534 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">AgJTb7TMVHyj2iNWz2N8rcvkS7xhFsqvfp5eqrTe</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"22 characters\">https://mia.test/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$wMJBDtnQUSeJQj5gCLNgV.9Yd/u2xb7xicPuuj0GtQ51C1KTyTSXC</span>\"\n  \"<span class=sf-dump-key>48040ef7f2542b39b9ba9a72983b0d88_filters</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>localidad</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>zona</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>categoria</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>suscripcion</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1749079534\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://mia.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}