<?php

namespace App\Models;

use App\Enums\EstadoSuscripcion;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Suscripcion extends Model
{
    use HasFactory;

    protected $table = 'suscripciones';

    protected $fillable = [
        'negocio_id',
        'plan',
        'status',
        'started_at',
        'ends_at',
    ];

    protected $casts = [
        'status' => EstadoSuscripcion::class,
        'started_at' => 'datetime',
        'ends_at' => 'datetime',
    ];

    public function negocio()
    {
        return $this->belongsTo(Negocio::class);
    }

    public function pagos()
    {
        return $this->hasMany(PagoSuscripcion::class);
    }
}
