<?php if (isset($component)) { $__componentOriginal511d4862ff04963c3c16115c05a86a9d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal511d4862ff04963c3c16115c05a86a9d = $attributes; } ?>
<?php $component = Illuminate\View\DynamicComponent::resolve(['component' => $getFieldWrapperView()] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dynamic-component'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\DynamicComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['field' => $field]); ?>
    <?php
        $state = $getState() ?? [
            'latitud_min' => '',
            'latitud_max' => '',
            'longitud_min' => '',
            'longitud_max' => '',
        ];
        // Si se definieron límites, calcular centro para el mapa
        $centerLat = ($state['latitud_min'] && $state['latitud_max']) 
            ? (($state['latitud_min'] + $state['latitud_max']) / 2) 
            : 36.9990019;
        $centerLng = ($state['longitud_min'] && $state['longitud_max']) 
            ? (($state['longitud_min'] + $state['longitud_max']) / 2) 
            : -6.5478919;
    ?>

    <div 
        x-data="mapRectangleComponent({
            latitud_min: '<?php echo e($state['latitud_min'] ?? $centerLat - 0.01); ?>',
            latitud_max: '<?php echo e($state['latitud_max'] ?? $centerLat + 0.01); ?>',
            longitud_min: '<?php echo e($state['longitud_min'] ?? $centerLng - 0.01); ?>',
            longitud_max: '<?php echo e($state['longitud_max'] ?? $centerLng + 0.01); ?>',
            centerLat: '<?php echo e($centerLat); ?>',
            centerLng: '<?php echo e($centerLng); ?>'
        })" 
        x-init="initMap()"
        class="w-full h-72 relative space-y-4"
        wire:ignore
    >
        <div class="flex items-center gap-2">
            <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-o-map-pin'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-5 h-5 text-primary-500']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
            
            <span 
                class="text-sm font-medium text-gray-700 dark:text-gray-300"
                x-text="
                    (bounds.latitud_min && bounds.latitud_max && bounds.longitud_min && bounds.longitud_max)
                        ? `Límites: ${bounds.latitud_min}, ${bounds.longitud_min} – ${bounds.latitud_max}, ${bounds.longitud_max}`
                        : 'Selecciona los límites'
                "
            ></span>
        </div>
        <div id="map-<?php echo e($getStatePath()); ?>" class="w-full h-full rounded" style="height: 400px;"></div>
    </div>

    <script>
        function mapRectangleComponent(initial) {
            return {
                // Valores iniciales del rectángulo
                bounds: {
                    latitud_min: initial.latitud_min,
                    latitud_max: initial.latitud_max,
                    longitud_min: initial.longitud_min,
                    longitud_max: initial.longitud_max,
                },
                map: null,
                rectangle: null,
                drawnItems: null,
                dreawControl: null,
                initMap() {
                    // Definir centro del mapa
                    let centerLat = parseFloat(initial.centerLat) || 36.9990019;
                    let centerLng = parseFloat(initial.centerLng) || -6.5478919;

                    this.map = L.map('map-<?php echo e($getStatePath()); ?>').setView([centerLat, centerLng], 13);

                    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                        maxZoom: 19,
                        attribution: '© OpenStreetMap contributors'
                    }).addTo(this.map);

                    // Crear el grupo de capas editables y añadirlo al mapa
                    this.drawnItems = new L.FeatureGroup();
                    this.map.addLayer(this.drawnItems);

                    // Configurar el control de dibujo/edición
                    this.drawControl = new L.Control.Draw({
                        draw: {
                            polygon: false,
                            polyline: false,
                            circle: false,
                            circlemarker: false,
                            marker: false,
                            rectangle: false, // no permitimos crear nuevos rectángulos
                        },
                        edit: {
                            featureGroup: this.drawnItems, // Asocia el grupo editable
                            edit: true,
                            remove: false,
                        }
                    });
                    this.map.addControl(this.drawControl);

                    // Definir límites iniciales
                    let southWest, northEast;
                    if (initial.latitud_min && initial.longitud_min && initial.latitud_max && initial.longitud_max) {
                        southWest = L.latLng(initial.latitud_min, initial.longitud_min);
                        northEast = L.latLng(initial.latitud_max, initial.longitud_max);
                    } else {
                        // Límites por defecto basados en el centro
                        southWest = L.latLng(centerLat - 0.01, centerLng - 0.01);
                        northEast = L.latLng(centerLat + 0.01, centerLng + 0.01);
                    }
                    let rectangleBounds = L.latLngBounds(southWest, northEast);

                    // Crear el rectángulo y agregarlo al grupo editable
                    this.rectangle = L.rectangle(rectangleBounds, {color: "#ff7800", weight: 1});
                    this.drawnItems.addLayer(this.rectangle);

                    // Ajustar el mapa para mostrar el rectángulo
                    this.map.fitBounds(rectangleBounds);

                    // Iniciar el modo de edición programáticamente para activar los "handlers"
                    // Nota: el acceso a _toolbars.edit puede cambiar según la versión de Leaflet Draw
                    setTimeout(() => {
                        this.drawControl._toolbars.edit._modes.edit.handler.enable();
                    }, 500);

                    // Escuchar el evento 'edit' para actualizar el estado
                    this.map.on('draw:edited', (e) => {
                        e.layers.eachLayer((layer) => {
                            if (layer._leaflet_id === this.rectangle._leaflet_id) {
                                let b = layer.getBounds();
                                let newState = {
                                    latitud_min: b.getSouthWest().lat.toFixed(6),
                                    longitud_min: b.getSouthWest().lng.toFixed(6),
                                    latitud_max: b.getNorthEast().lat.toFixed(6),
                                    longitud_max: b.getNorthEast().lng.toFixed(6),
                                };
                                this.bounds = newState; // Actualiza la variable para la etiqueta dinámica
                                window.Livewire.find('<?php echo e($_instance->getId()); ?>').set('<?php echo e($getStatePath()); ?>', newState);
                            }
                        });
                    });
                }
            }
        }
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal511d4862ff04963c3c16115c05a86a9d)): ?>
<?php $attributes = $__attributesOriginal511d4862ff04963c3c16115c05a86a9d; ?>
<?php unset($__attributesOriginal511d4862ff04963c3c16115c05a86a9d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal511d4862ff04963c3c16115c05a86a9d)): ?>
<?php $component = $__componentOriginal511d4862ff04963c3c16115c05a86a9d; ?>
<?php unset($__componentOriginal511d4862ff04963c3c16115c05a86a9d); ?>
<?php endif; ?>
<?php /**PATH C:\Proyectos\webapps\mia\webapp\resources\views/filament/forms/components/map-rectangle-field.blade.php ENDPATH**/ ?>