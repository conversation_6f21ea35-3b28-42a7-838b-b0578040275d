<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PagoSuscripcion extends Model
{
    /** @use HasFactory<\Database\Factories\PagoSuscripcionFactory> */
    use HasFactory;

    protected $table = 'pago_suscripcions';

    protected $fillable = [
        'suscripcion_id',
        'metodo_pago',
        'importe',
        'transaccion_id',
        'estado',
        'fecha_pago',
    ];

    public function suscripcion()
    {
        return $this->belongsTo(Suscripcion::class);
    }

    public function negocio()
    {
        return $this->hasOneThrough(
            Negocio::class,
            Suscripcion::class,
            'id', // Foreign key on suscripciones table
            'id', // Foreign key on negocios table
            'suscripcion_id', // Local key on pago_suscripcions table
            'negocio_id' // Local key on suscripciones table
        );
    }
}
