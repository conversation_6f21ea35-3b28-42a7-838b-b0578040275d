
<?php if (isset($component)) { $__componentOriginal511d4862ff04963c3c16115c05a86a9d = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal511d4862ff04963c3c16115c05a86a9d = $attributes; } ?>
<?php $component = Illuminate\View\DynamicComponent::resolve(['component' => $getFieldWrapperView()] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('dynamic-component'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\DynamicComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['field' => $field]); ?>
    <?php
        // Convertimos el estado a un formato adecuado para Alpine.js
        $state = $getState() ?? [
            'lunes'     => [],
            'martes'    => [],
            'miércoles' => [],
            'jueves'    => [],
            'viernes'   => [],
            'sábado'    => [],
            'domingo'   => [],
        ];

        // Transformamos cada tramo horario "09:00-18:00" a ['09:00', '18:00']
        foreach ($state as $day => $tramos) {
            // Asegurar que $tramos sea siempre un array
            $tramos = is_array($tramos) ? $tramos : [];
            $state[$day] = array_map(fn($tramo) => explode('-', $tramo), $tramos);
        }
    ?>

    <table class="w-full border-collapse">
        <colgroup>
            <col style="width: 1%;">
            <col>
        </colgroup>
        <thead>
            <tr>
                <th class="border px-4 py-2 text-left bg-gray-200 dark:bg-gray-700 dark:text-gray-200">Día</th>
                <th class="border px-4 py-2 text-left bg-gray-200 dark:bg-gray-700 dark:text-gray-200">Tramos horarios</th>
            </tr>
        </thead>
        <tbody>
            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $state; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $day => $tramos): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <tr class="bg-white dark:bg-gray-800" 
                    x-data="{
                        tramos: <?php echo \Illuminate\Support\Js::from($tramos)->toHtml() ?>, 
                        addTramo() { 
                          if (this.tramos.length < 4) {
                            this.tramos.push(['', '']); 
                            this.updateState(); 
                          }
                        }, 
                        removeTramo(index) { 
                          this.tramos.splice(index, 1); 
                          this.updateState(); 
                        }, 
                        updateState() { 
                          window.Livewire.find('<?php echo e($_instance->getId()); ?>').set('<?php echo e($getStatePath()); ?>.<?php echo e($day); ?>', this.tramos
                              .map(t => t[0] && t[1] ? `${t[0]}-${t[1]}` : '')
                              .filter(Boolean)
                          ); 
                        }
                    }">
                    <td class="border px-4 py-2 text-gray-800 dark:text-gray-100">
                        <?php echo e(ucfirst($day)); ?>

                    </td>
                    <td class="border px-4 py-2">
                        <div class="flex flex-wrap gap-2">
                            <template x-for="(tramo, index) in tramos" :key="index">
                                <div class="flex items-center gap-2 border rounded p-2 bg-gray-50 dark:bg-gray-700 text-gray-800 dark:text-gray-100">
                                    <input type="time" x-model="tramo[0]" @change="updateState()" class="w-24 border rounded p-2 bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-100">
                                    <span>-</span>
                                    <input type="time" x-model="tramo[1]" @change="updateState()" class="w-24 border rounded p-2 bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-100">
                                    <button type="button" @click="removeTramo(index)" class="text-red-500 hover:text-red-700">
                                        <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-s-trash'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'fi-icon-btn relative flex items-center justify-center rounded-lg outline-none transition duration-75 focus-visible:ring-2 -m-1.5 h-5 w-5 fi-color-custom text-custom-500 hover:text-custom-600 focus-visible:ring-custom-600 dark:text-custom-400 dark:hover:text-custom-300 dark:focus-visible:ring-custom-500 fi-color-danger fi-ac-action fi-ac-icon-btn-action','style' => '--c-300:var(--danger-300);--c-400:var(--danger-400);--c-500:var(--danger-500);--c-600:var(--danger-600);']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                                    </button>
                                </div>
                            </template>

                            <button type="button" x-show="tramos.length < 4" @click="addTramo()"
                                title="Añadir tramo horario"
                                class="flex items-center justify-center gap-1 p-2 rounded text-white bg-primary-600 hover:bg-primary-700">
                                <span>+</span>
                                <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('heroicon-s-clock'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\BladeUI\Icons\Components\Svg::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'h-5 w-5']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                            </button>
                        </div>
                    </td>
                </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
        </tbody>
    </table>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal511d4862ff04963c3c16115c05a86a9d)): ?>
<?php $attributes = $__attributesOriginal511d4862ff04963c3c16115c05a86a9d; ?>
<?php unset($__attributesOriginal511d4862ff04963c3c16115c05a86a9d); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal511d4862ff04963c3c16115c05a86a9d)): ?>
<?php $component = $__componentOriginal511d4862ff04963c3c16115c05a86a9d; ?>
<?php unset($__componentOriginal511d4862ff04963c3c16115c05a86a9d); ?>
<?php endif; ?>
<?php /**PATH C:\Proyectos\webapps\mia\webapp\resources\views/filament/forms/components/custom-key-value.blade.php ENDPATH**/ ?>