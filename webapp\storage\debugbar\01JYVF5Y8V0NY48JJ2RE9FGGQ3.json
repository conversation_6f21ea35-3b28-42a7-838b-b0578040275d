{"__meta": {"id": "01JYVF5Y8V0NY48JJ2RE9FGGQ3", "datetime": "2025-06-28 14:31:06", "utime": **********.268088, "method": "GET", "uri": "/admin/pago-suscripcions/create", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751121064.765672, "end": **********.268103, "duration": 1.5024309158325195, "duration_str": "1.5s", "measures": [{"label": "Booting", "start": 1751121064.765672, "relative_start": 0, "end": **********.46513, "relative_end": **********.46513, "duration": 0.***************, "duration_str": "699ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.465144, "relative_start": 0.****************, "end": **********.268105, "relative_end": 2.1457672119140625e-06, "duration": 0.****************, "duration_str": "803ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.963199, "relative_start": 1.****************, "end": **********.966415, "relative_end": **********.966415, "duration": 0.0032160282135009766, "duration_str": "3.22ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.113073, "relative_start": 1.****************, "end": **********.113073, "relative_end": **********.113073, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.117924, "relative_start": 1.****************, "end": **********.117924, "relative_end": **********.117924, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.12125, "relative_start": 1.3555779457092285, "end": **********.12125, "relative_end": **********.12125, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7efa8d8730e6e64b895c482f47ff6151", "start": **********.125066, "relative_start": 1.3593940734863281, "end": **********.125066, "relative_end": **********.125066, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.128348, "relative_start": 1.3626761436462402, "end": **********.128348, "relative_end": **********.128348, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": **********.131591, "relative_start": 1.3659191131591797, "end": **********.131591, "relative_end": **********.131591, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.135848, "relative_start": 1.370176076889038, "end": **********.135848, "relative_end": **********.135848, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.139312, "relative_start": 1.3736400604248047, "end": **********.139312, "relative_end": **********.139312, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.14216, "relative_start": 1.3764879703521729, "end": **********.14216, "relative_end": **********.14216, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": **********.157913, "relative_start": 1.3922410011291504, "end": **********.157913, "relative_end": **********.157913, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-maps::styles", "start": **********.254921, "relative_start": 1.4892489910125732, "end": **********.254921, "relative_end": **********.254921, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-maps::scripts", "start": **********.258542, "relative_start": 1.4928700923919678, "end": **********.258542, "relative_end": **********.258542, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.259893, "relative_start": 1.4942209720611572, "end": **********.259993, "relative_end": **********.259993, "duration": 0.00010013580322265625, "duration_str": "100μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.266053, "relative_start": 1.5003809928894043, "end": **********.266152, "relative_end": **********.266152, "duration": 9.894371032714844e-05, "duration_str": "99μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 49773152, "peak_usage_str": "47MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.45.0", "PHP Version": "8.3.8", "Environment": "local", "Debug Mode": "Enabled", "URL": "mia.test", "Timezone": "UTC", "Locale": "es"}}, "views": {"count": 12, "nb_templates": 12, "templates": [{"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.113037, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.117896, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}}, {"name": "__components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.121224, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}}, {"name": "__components::7efa8d8730e6e64b895c482f47ff6151", "param_count": null, "params": [], "start": **********.125039, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\storage\\framework\\views/7efa8d8730e6e64b895c482f47ff6151.blade.php__components::7efa8d8730e6e64b895c482f47ff6151", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fstorage%2Fframework%2Fviews%2F7efa8d8730e6e64b895c482f47ff6151.blade.php&line=1", "ajax": false, "filename": "7efa8d8730e6e64b895c482f47ff6151.blade.php", "line": "?"}}, {"name": "__components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.128321, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}}, {"name": "__components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": **********.131565, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.135822, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.139284, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.142132, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "__components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": **********.157885, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}}, {"name": "filament-maps::styles", "param_count": null, "params": [], "start": **********.254882, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\vendor\\webbingbrasil\\filament-maps\\src\\/../resources/views/styles.blade.phpfilament-maps::styles", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Fwebbingbrasil%2Ffilament-maps%2Fresources%2Fviews%2Fstyles.blade.php&line=1", "ajax": false, "filename": "styles.blade.php", "line": "?"}}, {"name": "filament-maps::scripts", "param_count": null, "params": [], "start": **********.258515, "type": "blade", "hash": "bladeC:\\Proyectos\\webapps\\mia\\webapp\\vendor\\webbingbrasil\\filament-maps\\src\\/../resources/views/scripts.blade.phpfilament-maps::scripts", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Fwebbingbrasil%2Ffilament-maps%2Fresources%2Fviews%2Fscripts.blade.php&line=1", "ajax": false, "filename": "scripts.blade.php", "line": "?"}}]}, "queries": {"count": 52, "nb_statements": 52, "nb_visible_statements": 52, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02396, "accumulated_duration_str": "23.96ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `sessions` where `id` = 'OGF0xmQNkiM3P99CWPuIofWU8bS7yWbRQ0GqToUS' limit 1", "type": "query", "params": [], "bindings": ["OGF0xmQNkiM3P99CWPuIofWU8bS7yWbRQ0GqToUS"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 117}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 105}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 89}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "start": **********.971859, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:97", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=97", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "97"}, "connection": "proyecto-mba", "explain": null, "start_percent": 0, "width_percent": 7.346}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 180}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.982171, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "proyecto-mba", "explain": null, "start_percent": 7.346, "width_percent": 1.92}, {"sql": "select * from `cache` where `key` in ('spatie.permission.cache')", "type": "query", "params": [], "bindings": ["spatie.permission.cache"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 105}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 117}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 418}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.985525, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:130", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 130}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=130", "ajax": false, "filename": "DatabaseStore.php", "line": "130"}, "connection": "proyecto-mba", "explain": null, "start_percent": 9.265, "width_percent": 1.669}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` in (1) and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 571}], "start": **********.991788, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:328", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=328", "ajax": false, "filename": "HasPermissions.php", "line": "328"}, "connection": "proyecto-mba", "explain": null, "start_percent": 10.935, "width_percent": 2.129}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 314}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 217}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 263}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 128}], "start": **********.9942381, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:241", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=241", "ajax": false, "filename": "HasRoles.php", "line": "241"}, "connection": "proyecto-mba", "explain": null, "start_percent": 13.063, "width_percent": 2.838}, {"sql": "select * from `suscripciones`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 785}, {"index": 16, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 17, "namespace": null, "name": "vendor/filament/forms/src/Components/Concerns/HasOptions.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Concerns\\HasOptions.php", "line": 32}, {"index": 18, "namespace": "view", "name": "filament-forms::components.select", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\/../resources/views/components/select.blade.php", "line": 77}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.042438, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "Select.php:785", "source": {"index": 15, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 785}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Ffilament%2Fforms%2Fsrc%2FComponents%2FSelect.php&line=785", "ajax": false, "filename": "Select.php", "line": "785"}, "connection": "proyecto-mba", "explain": null, "start_percent": 15.902, "width_percent": 3.548}, {"sql": "select * from `negocios` where `negocios`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.046108, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 19.449, "width_percent": 2.295}, {"sql": "select * from `negocios` where `negocios`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.04799, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 21.745, "width_percent": 1.92}, {"sql": "select * from `negocios` where `negocios`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.0495791, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 23.664, "width_percent": 1.169}, {"sql": "select * from `negocios` where `negocios`.`id` = 4 limit 1", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.050849, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 24.833, "width_percent": 1.043}, {"sql": "select * from `negocios` where `negocios`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.0520458, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 25.876, "width_percent": 0.918}, {"sql": "select * from `negocios` where `negocios`.`id` = 6 limit 1", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.053284, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 26.795, "width_percent": 1.336}, {"sql": "select * from `negocios` where `negocios`.`id` = 7 limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.054706, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 28.13, "width_percent": 2.588}, {"sql": "select * from `negocios` where `negocios`.`id` = 8 limit 1", "type": "query", "params": [], "bindings": [8], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.056453, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 30.718, "width_percent": 1.336}, {"sql": "select * from `negocios` where `negocios`.`id` = 9 limit 1", "type": "query", "params": [], "bindings": [9], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.057778, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 32.053, "width_percent": 0.918}, {"sql": "select * from `negocios` where `negocios`.`id` = 10 limit 1", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.058992, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 32.972, "width_percent": 1.461}, {"sql": "select * from `negocios` where `negocios`.`id` = 11 limit 1", "type": "query", "params": [], "bindings": [11], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.0603368, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 34.432, "width_percent": 2.546}, {"sql": "select * from `negocios` where `negocios`.`id` = 12 limit 1", "type": "query", "params": [], "bindings": [12], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.062114, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 36.978, "width_percent": 2.671}, {"sql": "select * from `negocios` where `negocios`.`id` = 13 limit 1", "type": "query", "params": [], "bindings": [13], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.064333, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 39.649, "width_percent": 2.337}, {"sql": "select * from `negocios` where `negocios`.`id` = 14 limit 1", "type": "query", "params": [], "bindings": [14], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.065997, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 41.987, "width_percent": 1.711}, {"sql": "select * from `negocios` where `negocios`.`id` = 15 limit 1", "type": "query", "params": [], "bindings": [15], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.0675552, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 43.698, "width_percent": 1.92}, {"sql": "select * from `negocios` where `negocios`.`id` = 16 limit 1", "type": "query", "params": [], "bindings": [16], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.06915, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 45.618, "width_percent": 1.461}, {"sql": "select * from `negocios` where `negocios`.`id` = 17 limit 1", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.070529, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 47.078, "width_percent": 1.085}, {"sql": "select * from `negocios` where `negocios`.`id` = 18 limit 1", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.0717409, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 48.164, "width_percent": 0.96}, {"sql": "select * from `negocios` where `negocios`.`id` = 19 limit 1", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.072896, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 49.124, "width_percent": 0.918}, {"sql": "select * from `negocios` where `negocios`.`id` = 20 limit 1", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.0743032, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 50.042, "width_percent": 2.671}, {"sql": "select * from `negocios` where `negocios`.`id` = 21 limit 1", "type": "query", "params": [], "bindings": [21], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.0762331, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 52.713, "width_percent": 2.045}, {"sql": "select * from `negocios` where `negocios`.`id` = 22 limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.078056, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 54.758, "width_percent": 2.254}, {"sql": "select * from `negocios` where `negocios`.`id` = 23 limit 1", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.0797439, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 57.012, "width_percent": 1.669}, {"sql": "select * from `negocios` where `negocios`.`id` = 24 limit 1", "type": "query", "params": [], "bindings": [24], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.081522, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 58.681, "width_percent": 1.628}, {"sql": "select * from `negocios` where `negocios`.`id` = 25 limit 1", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.0829961, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 60.309, "width_percent": 1.169}, {"sql": "select * from `negocios` where `negocios`.`id` = 26 limit 1", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.084354, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 61.477, "width_percent": 0.96}, {"sql": "select * from `negocios` where `negocios`.`id` = 27 limit 1", "type": "query", "params": [], "bindings": [27], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.085526, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 62.437, "width_percent": 0.918}, {"sql": "select * from `negocios` where `negocios`.`id` = 28 limit 1", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.086662, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 63.356, "width_percent": 0.876}, {"sql": "select * from `negocios` where `negocios`.`id` = 29 limit 1", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.087856, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 64.232, "width_percent": 2.045}, {"sql": "select * from `negocios` where `negocios`.`id` = 30 limit 1", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.089354, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 66.277, "width_percent": 2.254}, {"sql": "select * from `negocios` where `negocios`.`id` = 31 limit 1", "type": "query", "params": [], "bindings": [31], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.0913188, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 68.531, "width_percent": 1.628}, {"sql": "select * from `negocios` where `negocios`.`id` = 32 limit 1", "type": "query", "params": [], "bindings": [32], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.092765, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 70.159, "width_percent": 1.92}, {"sql": "select * from `negocios` where `negocios`.`id` = 33 limit 1", "type": "query", "params": [], "bindings": [33], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.09447, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 72.078, "width_percent": 1.002}, {"sql": "select * from `negocios` where `negocios`.`id` = 34 limit 1", "type": "query", "params": [], "bindings": [34], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.095664, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 73.08, "width_percent": 0.918}, {"sql": "select * from `negocios` where `negocios`.`id` = 35 limit 1", "type": "query", "params": [], "bindings": [35], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.096812, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 73.998, "width_percent": 0.835}, {"sql": "select * from `negocios` where `negocios`.`id` = 36 limit 1", "type": "query", "params": [], "bindings": [36], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.097924, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 74.833, "width_percent": 0.835}, {"sql": "select * from `negocios` where `negocios`.`id` = 37 limit 1", "type": "query", "params": [], "bindings": [37], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.099031, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 75.668, "width_percent": 0.918}, {"sql": "select * from `negocios` where `negocios`.`id` = 38 limit 1", "type": "query", "params": [], "bindings": [38], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.100175, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 76.586, "width_percent": 0.918}, {"sql": "select * from `negocios` where `negocios`.`id` = 39 limit 1", "type": "query", "params": [], "bindings": [39], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.101343, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 77.504, "width_percent": 0.751}, {"sql": "select * from `negocios` where `negocios`.`id` = 40 limit 1", "type": "query", "params": [], "bindings": [40], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.102445, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 78.255, "width_percent": 0.876}, {"sql": "select * from `negocios` where `negocios`.`id` = 41 limit 1", "type": "query", "params": [], "bindings": [41], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.103692, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 79.132, "width_percent": 0.71}, {"sql": "select * from `negocios` where `negocios`.`id` = 42 limit 1", "type": "query", "params": [], "bindings": [42], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.104788, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 79.841, "width_percent": 0.876}, {"sql": "select * from `negocios` where `negocios`.`id` = 43 limit 1", "type": "query", "params": [], "bindings": [43], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.105913, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 80.718, "width_percent": 1.628}, {"sql": "select * from `negocios` where `negocios`.`id` = 44 limit 1", "type": "query", "params": [], "bindings": [44], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.107355, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 82.346, "width_percent": 1.878}, {"sql": "select * from `negocios` where `negocios`.`id` = 45 limit 1", "type": "query", "params": [], "bindings": [45], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, {"index": 23, "namespace": null, "name": "vendor/filament/support/src/Concerns/EvaluatesClosures.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\support\\src\\Concerns\\EvaluatesClosures.php", "line": 35}, {"index": 24, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 1116}, {"index": 25, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 787}, {"index": 29, "namespace": null, "name": "vendor/filament/forms/src/Components/Select.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\filament\\forms\\src\\Components\\Select.php", "line": 786}], "start": **********.108984, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "PagoSuscripcionResource.php:40", "source": {"index": 22, "namespace": null, "name": "app/Filament/Resources/PagoSuscripcionResource.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\app\\Filament\\Resources\\PagoSuscripcionResource.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FFilament%2FResources%2FPagoSuscripcionResource.php&line=40", "ajax": false, "filename": "PagoSuscripcionResource.php", "line": "40"}, "connection": "proyecto-mba", "explain": null, "start_percent": 84.224, "width_percent": 1.336}, {"sql": "update `sessions` set `payload` = 'YTo3OntzOjY6Il90b2tlbiI7czo0MDoibEZ5bk0xbldDM3dpUGR0cmhtODI0TnRBWUhpWU1FYTI3anZDb3FKQiI7czozOiJ1cmwiO2E6MDp7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjQ3OiJodHRwczovL21pYS50ZXN0L2FkbWluL3BhZ28tc3VzY3JpcGNpb25zL2NyZWF0ZSI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fXM6NTA6ImxvZ2luX3dlYl81OWJhMzZhZGRjMmIyZjk0MDE1ODBmMDE0YzdmNThlYTRlMzA5ODlkIjtpOjE7czoxNzoicGFzc3dvcmRfaGFzaF93ZWIiO3M6NjA6IiQyeSQxMiRXRWxoUlF1YjhqSjU0Rkswa3ZQbXRlbzdvT1BrZzhXSkp5ZzBIUEkxMVhJR0xscUxKa25VMiI7czo4OiJmaWxhbWVudCI7YTowOnt9fQ==', `last_activity` = **********, `user_id` = 1, `ip_address` = '127.0.0.1', `user_agent` = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' where `id` = 'OGF0xmQNkiM3P99CWPuIofWU8bS7yWbRQ0GqToUS'", "type": "query", "params": [], "bindings": ["YTo3OntzOjY6Il90b2tlbiI7czo0MDoibEZ5bk0xbldDM3dpUGR0cmhtODI0TnRBWUhpWU1FYTI3anZDb3FKQiI7czozOiJ1cmwiO2E6MDp7fXM6OToiX3ByZXZpb3VzIjthOjE6e3M6MzoidXJsIjtzOjQ3OiJodHRwczovL21pYS50ZXN0L2FkbWluL3BhZ28tc3VzY3JpcGNpb25zL2NyZWF0ZSI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fXM6NTA6ImxvZ2luX3dlYl81OWJhMzZhZGRjMmIyZjk0MDE1ODBmMDE0YzdmNThlYTRlMzA5ODlkIjtpOjE7czoxNzoicGFzc3dvcmRfaGFzaF93ZWIiO3M6NjA6IiQyeSQxMiRXRWxoUlF1YjhqSjU0Rkswa3ZQbXRlbzdvT1BrZzhXSkp5ZzBIUEkxMVhJR0xscUxKa25VMiI7czo4OiJmaWxhbWVudCI7YTowOnt9fQ==", **********, 1, "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "OGF0xmQNkiM3P99CWPuIofWU8bS7yWbRQ0GqToUS"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 140}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 176}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 245}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 130}], "start": **********.261102, "duration": 0.00346, "duration_str": "3.46ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:173", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Proyectos\\webapps\\mia\\webapp\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=173", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "173"}, "connection": "proyecto-mba", "explain": null, "start_percent": 85.559, "width_percent": 14.441}]}, "models": {"data": {"App\\Models\\Suscripcion": {"value": 45, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FModels%2FSuscripcion.php&line=1", "ajax": false, "filename": "Suscripcion.php", "line": "?"}}, "App\\Models\\Negocio": {"value": 45, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FModels%2FNegocio.php&line=1", "ajax": false, "filename": "Negocio.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 93, "is_counter": true}, "livewire": {"data": {"app.filament.resources.pago-suscripcion-resource.pages.create-pago-suscripcion #8Fx6adNFmvn6JSSoG82m": "array:4 [\n  \"data\" => array:18 [\n    \"record\" => null\n    \"data\" => array:6 [\n      \"suscripcion_id\" => null\n      \"metodo_pago\" => null\n      \"importe\" => null\n      \"fecha_pago\" => null\n      \"transaccion_id\" => null\n      \"estado\" => null\n    ]\n    \"previousUrl\" => \"https://mia.test/admin/pago-suscripcions\"\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"savedDataHash\" => null\n  ]\n  \"name\" => \"app.filament.resources.pago-suscripcion-resource.pages.create-pago-suscripcion\"\n  \"component\" => \"App\\Filament\\Resources\\PagoSuscripcionResource\\Pages\\CreatePagoSuscripcion\"\n  \"id\" => \"8Fx6adNFmvn6JSSoG82m\"\n]", "filament.livewire.global-search #ygEdke52ck361os51ods": "array:4 [\n  \"data\" => array:1 [\n    \"search\" => \"\"\n  ]\n  \"name\" => \"filament.livewire.global-search\"\n  \"component\" => \"Filament\\Livewire\\GlobalSearch\"\n  \"id\" => \"ygEdke52ck361os51ods\"\n]", "filament.livewire.notifications #XuahwZxdRlVXq7RoVmWT": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#2665\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"XuahwZxdRlVXq7RoVmWT\"\n]"}, "count": 3}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 69, "messages": [{"message": "[\n  ability => system.access-panel,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-984619320 data-indent-pad=\"  \"><span class=sf-dump-note>system.access-panel </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"19 characters\">system.access-panel</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-984619320\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.998557, "xdebug_link": null}, {"message": "[\n  ability => system.admin-dashboard,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1383088661 data-indent-pad=\"  \"><span class=sf-dump-note>system.admin-dashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">system.admin-dashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1383088661\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.00705, "xdebug_link": null}, {"message": "[\n  ability => create,\n  target => App\\Models\\PagoSuscripcion,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\PagoSuscripcion]\n]", "message_html": "<pre class=sf-dump id=sf-dump-109366240 data-indent-pad=\"  \"><span class=sf-dump-note>create App\\Models\\PagoSuscripcion</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"6 characters\">create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\PagoSuscripcion</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; App\\Models\\PagoSuscripcion]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-109366240\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.007309, "xdebug_link": null}, {"message": "[\n  ability => system.admin-dashboard,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-310447377 data-indent-pad=\"  \"><span class=sf-dump-note>system.admin-dashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">system.admin-dashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-310447377\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.022384, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\PagoSuscripcion,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\PagoSuscripcion]\n]", "message_html": "<pre class=sf-dump id=sf-dump-906209029 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\PagoSuscripcion</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\PagoSuscripcion</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; App\\Models\\PagoSuscripcion]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-906209029\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.022562, "xdebug_link": null}, {"message": "[\n  ability => system.admin,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-70404194 data-indent-pad=\"  \"><span class=sf-dump-note>system.admin </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">system.admin</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-70404194\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.159569, "xdebug_link": null}, {"message": "[\n  ability => user.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-462508611 data-indent-pad=\"  \"><span class=sf-dump-note>user.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">user.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-462508611\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.160749, "xdebug_link": null}, {"message": "[\n  ability => user.admin-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1166170990 data-indent-pad=\"  \"><span class=sf-dump-note>user.admin-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">user.admin-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1166170990\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.161335, "xdebug_link": null}, {"message": "[\n  ability => system.admin,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1898987930 data-indent-pad=\"  \"><span class=sf-dump-note>system.admin </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">system.admin</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1898987930\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.161934, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\AppVersion,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\AppVersion]\n]", "message_html": "<pre class=sf-dump id=sf-dump-274257335 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\AppVersion</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\AppVersion</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"28 characters\">[0 =&gt; App\\Models\\AppVersion]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-274257335\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.1628, "xdebug_link": null}, {"message": "[\n  ability => categoria.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1147849729 data-indent-pad=\"  \"><span class=sf-dump-note>categoria.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">categoria.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1147849729\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.163871, "xdebug_link": null}, {"message": "[\n  ability => categoria.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1291276420 data-indent-pad=\"  \"><span class=sf-dump-note>categoria.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">categoria.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1291276420\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.165567, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Categoria,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Categoria]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2005098768 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Categoria</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Categoria</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Categoria]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2005098768\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.165714, "xdebug_link": null}, {"message": "[\n  ability => user.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-903240608 data-indent-pad=\"  \"><span class=sf-dump-note>user.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">user.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-903240608\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.166462, "xdebug_link": null}, {"message": "[\n  ability => user.admin-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1901915046 data-indent-pad=\"  \"><span class=sf-dump-note>user.admin-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">user.admin-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1901915046\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.167005, "xdebug_link": null}, {"message": "[\n  ability => system.admin-dashboard,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2055417614 data-indent-pad=\"  \"><span class=sf-dump-note>system.admin-dashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">system.admin-dashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2055417614\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.167598, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Evento,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Evento]\n]", "message_html": "<pre class=sf-dump id=sf-dump-334372911 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Evento</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Evento</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Evento]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-334372911\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.16844, "xdebug_link": null}, {"message": "[\n  ability => localidad.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1026596238 data-indent-pad=\"  \"><span class=sf-dump-note>localidad.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localidad.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1026596238\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.168986, "xdebug_link": null}, {"message": "[\n  ability => localidad.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-602481273 data-indent-pad=\"  \"><span class=sf-dump-note>localidad.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localidad.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-602481273\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.17019, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Localidad,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Localidad]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2009016526 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Localidad</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Localidad</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Localidad]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2009016526\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.170397, "xdebug_link": null}, {"message": "[\n  ability => negocio.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-863443666 data-indent-pad=\"  \"><span class=sf-dump-note>negocio.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">negocio.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-863443666\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.171352, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Negocio,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Negocio]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1754192743 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Negocio</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Negocio</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Negocio]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1754192743\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.172503, "xdebug_link": null}, {"message": "[\n  ability => system.admin-dashboard,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1151805058 data-indent-pad=\"  \"><span class=sf-dump-note>system.admin-dashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">system.admin-dashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1151805058\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.173892, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\PagoSuscripcion,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\PagoSuscripcion]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1159092966 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\PagoSuscripcion</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\PagoSuscripcion</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; App\\Models\\PagoSuscripcion]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1159092966\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.174048, "xdebug_link": null}, {"message": "[\n  ability => system.permissions,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-945021691 data-indent-pad=\"  \"><span class=sf-dump-note>system.permissions </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">system.permissions</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-945021691\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.174807, "xdebug_link": null}, {"message": "[\n  ability => system.permissions,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1950327992 data-indent-pad=\"  \"><span class=sf-dump-note>system.permissions </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">system.permissions</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1950327992\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.176155, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\Permission\\Models\\Permission,\n  result => true,\n  user => 1,\n  arguments => [0 => <PERSON><PERSON>\\Permission\\Models\\Permission]\n]", "message_html": "<pre class=sf-dump id=sf-dump-959393547 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Permission\\Models\\Permission</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Spatie\\Permission\\Models\\Permission</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"42 characters\">[0 =&gt; Spatie\\Permission\\Models\\Permission]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-959393547\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.176324, "xdebug_link": null}, {"message": "[\n  ability => system.roles,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2051815964 data-indent-pad=\"  \"><span class=sf-dump-note>system.roles </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">system.roles</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2051815964\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.177328, "xdebug_link": null}, {"message": "[\n  ability => system.roles,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-378686273 data-indent-pad=\"  \"><span class=sf-dump-note>system.roles </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">system.roles</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-378686273\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.178803, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\Permission\\Models\\Role,\n  result => true,\n  user => 1,\n  arguments => [0 => <PERSON><PERSON>\\Permission\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-265812454 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Permission\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Spatie\\Permission\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"36 characters\">[0 =&gt; Spatie\\Permission\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-265812454\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.178966, "xdebug_link": null}, {"message": "[\n  ability => user.admin-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1887009143 data-indent-pad=\"  \"><span class=sf-dump-note>user.admin-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">user.admin-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1887009143\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.179792, "xdebug_link": null}, {"message": "[\n  ability => user.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-709084476 data-indent-pad=\"  \"><span class=sf-dump-note>user.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">user.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-709084476\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.181154, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-418287552 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-418287552\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.181307, "xdebug_link": null}, {"message": "[\n  ability => zona.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1629851491 data-indent-pad=\"  \"><span class=sf-dump-note>zona.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">zona.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1629851491\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.18194, "xdebug_link": null}, {"message": "[\n  ability => zona.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-333168555 data-indent-pad=\"  \"><span class=sf-dump-note>zona.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">zona.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-333168555\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.183166, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Zona,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Zona]\n]", "message_html": "<pre class=sf-dump id=sf-dump-850893401 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Zona</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Zona</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Zona]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-850893401\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.18332, "xdebug_link": null}, {"message": "[\n  ability => system.admin-dashboard,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-222720077 data-indent-pad=\"  \"><span class=sf-dump-note>system.admin-dashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">system.admin-dashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-222720077\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.190953, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\PagoSuscripcion,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\PagoSuscripcion]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1368324767 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\PagoSuscripcion</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\PagoSuscripcion</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; App\\Models\\PagoSuscripcion]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1368324767\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.191226, "xdebug_link": null}, {"message": "[\n  ability => system.admin,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2028380090 data-indent-pad=\"  \"><span class=sf-dump-note>system.admin </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">system.admin</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2028380090\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.211116, "xdebug_link": null}, {"message": "[\n  ability => user.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-572761096 data-indent-pad=\"  \"><span class=sf-dump-note>user.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">user.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-572761096\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.211967, "xdebug_link": null}, {"message": "[\n  ability => user.admin-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2034631424 data-indent-pad=\"  \"><span class=sf-dump-note>user.admin-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">user.admin-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2034631424\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.212736, "xdebug_link": null}, {"message": "[\n  ability => system.admin,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1685027925 data-indent-pad=\"  \"><span class=sf-dump-note>system.admin </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">system.admin</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1685027925\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.213362, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\AppVersion,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\AppVersion]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1499113293 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\AppVersion</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"21 characters\">App\\Models\\AppVersion</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"28 characters\">[0 =&gt; App\\Models\\AppVersion]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1499113293\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.214017, "xdebug_link": null}, {"message": "[\n  ability => categoria.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1478572845 data-indent-pad=\"  \"><span class=sf-dump-note>categoria.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">categoria.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1478572845\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.214654, "xdebug_link": null}, {"message": "[\n  ability => categoria.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-415660966 data-indent-pad=\"  \"><span class=sf-dump-note>categoria.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">categoria.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-415660966\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.215537, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Categoria,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Categoria]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1564654777 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Categoria</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Categoria</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Categoria]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1564654777\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.215684, "xdebug_link": null}, {"message": "[\n  ability => user.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-378380211 data-indent-pad=\"  \"><span class=sf-dump-note>user.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">user.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-378380211\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.216422, "xdebug_link": null}, {"message": "[\n  ability => user.admin-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-906657253 data-indent-pad=\"  \"><span class=sf-dump-note>user.admin-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">user.admin-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-906657253\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.217121, "xdebug_link": null}, {"message": "[\n  ability => system.admin-dashboard,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-644666197 data-indent-pad=\"  \"><span class=sf-dump-note>system.admin-dashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">system.admin-dashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-644666197\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.217743, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Evento,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Evento]\n]", "message_html": "<pre class=sf-dump id=sf-dump-161310964 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Evento</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"17 characters\">App\\Models\\Evento</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"24 characters\">[0 =&gt; App\\Models\\Evento]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-161310964\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.218354, "xdebug_link": null}, {"message": "[\n  ability => localidad.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-298337436 data-indent-pad=\"  \"><span class=sf-dump-note>localidad.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localidad.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-298337436\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.21886, "xdebug_link": null}, {"message": "[\n  ability => localidad.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1417462019 data-indent-pad=\"  \"><span class=sf-dump-note>localidad.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">localidad.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1417462019\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.219869, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Localidad,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Localidad]\n]", "message_html": "<pre class=sf-dump id=sf-dump-127844434 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Localidad</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"20 characters\">App\\Models\\Localidad</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"27 characters\">[0 =&gt; App\\Models\\Localidad]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-127844434\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.220027, "xdebug_link": null}, {"message": "[\n  ability => negocio.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-953689726 data-indent-pad=\"  \"><span class=sf-dump-note>negocio.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">negocio.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-953689726\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.220623, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Negocio,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Negocio]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1141374378 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Negocio</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"18 characters\">App\\Models\\Negocio</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"25 characters\">[0 =&gt; App\\Models\\Negocio]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1141374378\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.221246, "xdebug_link": null}, {"message": "[\n  ability => system.admin-dashboard,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1107541762 data-indent-pad=\"  \"><span class=sf-dump-note>system.admin-dashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">system.admin-dashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1107541762\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.222435, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\PagoSuscripcion,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\PagoSuscripcion]\n]", "message_html": "<pre class=sf-dump id=sf-dump-927636314 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\PagoSuscripcion</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"26 characters\">App\\Models\\PagoSuscripcion</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[0 =&gt; App\\Models\\PagoSuscripcion]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-927636314\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.222576, "xdebug_link": null}, {"message": "[\n  ability => system.permissions,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-885320805 data-indent-pad=\"  \"><span class=sf-dump-note>system.permissions </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">system.permissions</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-885320805\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.223404, "xdebug_link": null}, {"message": "[\n  ability => system.permissions,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-2085311594 data-indent-pad=\"  \"><span class=sf-dump-note>system.permissions </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">system.permissions</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2085311594\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.224452, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\Permission\\Models\\Permission,\n  result => true,\n  user => 1,\n  arguments => [0 => <PERSON><PERSON>\\Permission\\Models\\Permission]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1391968374 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Permission\\Models\\Permission</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Spatie\\Permission\\Models\\Permission</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"42 characters\">[0 =&gt; Spatie\\Permission\\Models\\Permission]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1391968374\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.224593, "xdebug_link": null}, {"message": "[\n  ability => system.roles,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-714399185 data-indent-pad=\"  \"><span class=sf-dump-note>system.roles </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">system.roles</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-714399185\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.225334, "xdebug_link": null}, {"message": "[\n  ability => system.roles,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1318162683 data-indent-pad=\"  \"><span class=sf-dump-note>system.roles </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"12 characters\">system.roles</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1318162683\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.226349, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => <PERSON><PERSON>\\Permission\\Models\\Role,\n  result => true,\n  user => 1,\n  arguments => [0 => <PERSON><PERSON>\\Permission\\Models\\Role]\n]", "message_html": "<pre class=sf-dump id=sf-dump-911873168 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny Spatie\\Permission\\Models\\Role</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Spatie\\Permission\\Models\\Role</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"36 characters\">[0 =&gt; Spatie\\Permission\\Models\\Role]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-911873168\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.22649, "xdebug_link": null}, {"message": "[\n  ability => user.admin-users,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1939751577 data-indent-pad=\"  \"><span class=sf-dump-note>user.admin-users </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"16 characters\">user.admin-users</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1939751577\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.227316, "xdebug_link": null}, {"message": "[\n  ability => user.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-179060494 data-indent-pad=\"  \"><span class=sf-dump-note>user.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">user.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-179060494\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.228393, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\User,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\User]\n]", "message_html": "<pre class=sf-dump id=sf-dump-937071961 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\User</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\User</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\User]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-937071961\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.228541, "xdebug_link": null}, {"message": "[\n  ability => zona.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-85185143 data-indent-pad=\"  \"><span class=sf-dump-note>zona.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">zona.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-85185143\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.229075, "xdebug_link": null}, {"message": "[\n  ability => zona.list,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1752544859 data-indent-pad=\"  \"><span class=sf-dump-note>zona.list </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">zona.list</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1752544859\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.229915, "xdebug_link": null}, {"message": "[\n  ability => viewAny,\n  target => App\\Models\\Zona,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Zona]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1875416738 data-indent-pad=\"  \"><span class=sf-dump-note>viewAny App\\Models\\Zona</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"7 characters\">viewAny</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"15 characters\">App\\Models\\Zona</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"22 characters\">[0 =&gt; App\\Models\\Zona]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1875416738\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.230055, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "https://mia.test/admin/pago-suscripcions/create", "action_name": "filament.admin.resources.pago-suscripcions.create", "controller_action": "App\\Filament\\Resources\\PagoSuscripcionResource\\Pages\\CreatePagoSuscripcion", "uri": "GET admin/pago-suscripcions/create", "controller": "App\\Filament\\Resources\\PagoSuscripcionResource\\Pages\\CreatePagoSuscripcion@render<a href=\"phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/pago-suscripcions", "file": "<a href=\"phpstorm://open?file=C%3A%2FProyectos%2Fwebapps%2Fmia%2Fwebapp%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Pages/BasePage.php:51-59</a>", "middleware": "panel:admin, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Filament\\Http\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, Filament\\Http\\Middleware\\Authenticate", "duration": "1.09s", "peak_memory": "54MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mia.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"57 characters\">&quot;Brave&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"96 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">es-ES,es;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">https://mia.test/admin/pago-suscripcions</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"709 characters\">XSRF-TOKEN=eyJpdiI6InlHcjVDaVgraVlnZXRzcXN2bVZnRmc9PSIsInZhbHVlIjoiWmRQVy9XVGJ6Nk1HRnlSZDY3aG9QMkdOOERZTFpHOG8rZEtmTStLVUJiWWZQeXIyanpKWnhtNWlQazhuWHA1VmloUUJHY1pxS1NSRGFBZmNNemxXay84NFNReFlDcnY1WFk5Q2lTaitqa2R1K3Y2Y2tXTHBlUTBmMlF0K0V3U1oiLCJtYWMiOiJmZjRjMmM0OTA1ZTFkN2Y5MDJlNjFjMDI4YTBmODE2ZjQxYjgxNTNjOWM1ZGRmZDBmNzU5NGU1MmZiZGUxZDBmIiwidGFnIjoiIn0%3D; mia_session=eyJpdiI6IktHbWpuTC9SazJHM1ZnTDl6MnQxbkE9PSIsInZhbHVlIjoiZ0ZQakFTYXd4UFAwWlErZytBazF3TThCeGViWkxzM1pvSlR6LzBjaGhiNko0L0lsVDVnYXRINzFlcUdUOFdCZkhOVGpGSHVvbDhTZkRSSm1sSGJZc2c2WTNSRzZIbVJzMzVhSXpWMzh4VUhHbWNaMHBrOUoxZWE4bUExOW9hZlciLCJtYWMiOiJhZDM1YTJkYTNlYjhlNDA1MjUxYTc5MzA3MTBlZmViNjQ4MjI4NGY0Yjk2MTZhZjM1MWEzNTRmZTkwNDhkNTliIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-494900113 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lFynM1nWC3wiPdtrhm824NtAYHiYMEa27jvCoqJB</span>\"\n  \"<span class=sf-dump-key>mia_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OGF0xmQNkiM3P99CWPuIofWU8bS7yWbRQ0GqToUS</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-494900113\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-874929942 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 28 Jun 2025 14:31:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"436 characters\">XSRF-TOKEN=eyJpdiI6IlA5cnl2UlpPSVRpOHM3N0NGSVE3UUE9PSIsInZhbHVlIjoidkg2NDNIYmErekRCSS94WXdlSzVXaWVYb0RCaHlRSTB6SVBoU2dNWmc1YWU2bzg1S0VGTDV4RWYrcUVWMTBaY3lsVzlLbVpnMHF5SDhxWHJlekZDc0lDWWFiK0VoNjh2Mit6bHlGU0ZUdFhwb0phdmJML05vK3BLQUJtQmFFTzciLCJtYWMiOiI3ZGM2MWE1OTdlM2I0YWQzMWFhZTJjMzA3YTlkYTc1Y2NlNWJmZTc1YjczYzBkOTkxNWIwMzY4MjYxNzg0MGY4IiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 16:31:06 GMT; Max-Age=7200; path=/; secure; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"447 characters\">mia_session=eyJpdiI6ImZxaVJpT09PbTFTZlJ2cXFoTzkzOXc9PSIsInZhbHVlIjoibjJ3dks0TlU2ZEswUXcxYjdXRVQrS3NNalhiODlwUHVmUXY5UjZydm5lZjlkSHRnY2d6Z3g1bzNDTWkrOFdRL2FyZFpJN09hTS9ZQ09aUU9ldUVEYk8vMmlveGNKRVMwNmd3aUJ5SHNkQk94b2lzNFFxV3ZWTFJaMjFhbzcyZGkiLCJtYWMiOiJlODZjMWM0NzMxZTgyMjlkZjhhOTRiNWJlMWU5MTBiOTQwNDgzOGY2ODc2MzJhNjgwMWYyOWI0NGU2NTFhYTVkIiwidGFnIjoiIn0%3D; expires=Sat, 28 Jun 2025 16:31:06 GMT; Max-Age=7200; path=/; secure; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"408 characters\">XSRF-TOKEN=eyJpdiI6IlA5cnl2UlpPSVRpOHM3N0NGSVE3UUE9PSIsInZhbHVlIjoidkg2NDNIYmErekRCSS94WXdlSzVXaWVYb0RCaHlRSTB6SVBoU2dNWmc1YWU2bzg1S0VGTDV4RWYrcUVWMTBaY3lsVzlLbVpnMHF5SDhxWHJlekZDc0lDWWFiK0VoNjh2Mit6bHlGU0ZUdFhwb0phdmJML05vK3BLQUJtQmFFTzciLCJtYWMiOiI3ZGM2MWE1OTdlM2I0YWQzMWFhZTJjMzA3YTlkYTc1Y2NlNWJmZTc1YjczYzBkOTkxNWIwMzY4MjYxNzg0MGY4IiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 16:31:06 GMT; path=/; secure</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"419 characters\">mia_session=eyJpdiI6ImZxaVJpT09PbTFTZlJ2cXFoTzkzOXc9PSIsInZhbHVlIjoibjJ3dks0TlU2ZEswUXcxYjdXRVQrS3NNalhiODlwUHVmUXY5UjZydm5lZjlkSHRnY2d6Z3g1bzNDTWkrOFdRL2FyZFpJN09hTS9ZQ09aUU9ldUVEYk8vMmlveGNKRVMwNmd3aUJ5SHNkQk94b2lzNFFxV3ZWTFJaMjFhbzcyZGkiLCJtYWMiOiJlODZjMWM0NzMxZTgyMjlkZjhhOTRiNWJlMWU5MTBiOTQwNDgzOGY2ODc2MzJhNjgwMWYyOWI0NGU2NTFhYTVkIiwidGFnIjoiIn0%3D; expires=Sat, 28-Jun-2025 16:31:06 GMT; path=/; secure; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-874929942\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-886294081 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lFynM1nWC3wiPdtrhm824NtAYHiYMEa27jvCoqJB</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"47 characters\">https://mia.test/admin/pago-suscripcions/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$WElhRQub8jJ54FK0kvPmteo7oOPkg8WJJyg0HPI11XIGLlqLJknU2</span>\"\n  \"<span class=sf-dump-key>filament</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-886294081\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://mia.test/admin/pago-suscripcions/create", "action_name": "filament.admin.resources.pago-suscripcions.create", "controller_action": "App\\Filament\\Resources\\PagoSuscripcionResource\\Pages\\CreatePagoSuscripcion"}, "badge": null}}