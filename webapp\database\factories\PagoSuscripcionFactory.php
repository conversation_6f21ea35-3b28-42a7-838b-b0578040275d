<?php

namespace Database\Factories;

use App\Models\Suscripcion;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\PagoSuscripcion>
 */
class PagoSuscripcionFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'suscripcion_id' => Suscripcion::factory(),
            'metodo_pago' => $this->faker->randomElement(['bizum', 'transferencia', 'efectivo']),
            'importe' => $this->faker->randomFloat(2, 10, 100),
            'fecha_pago' => $this->faker->date(),
            'transaccion_id' => $this->faker->uuid(),
            'estado' => $this->faker->randomElement(['pendiente', 'completado', 'rechazado']),
        ];
    }
}
